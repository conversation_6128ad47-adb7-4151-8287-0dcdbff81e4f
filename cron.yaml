apiVersion: batch/v1
kind: CronJob
metadata:
  name: pullticketsboss
spec:
  schedule: "*/10 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: pullticketsboss
              image: "registry.digitalocean.com/sitefotos2/sitefotos:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running pullticketsboss && cd lib && php maintjobs/pullticketsboss.php && echo pullticketsboss Done"]
              env:
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: dailyverizonemail
spec:
  schedule: "0 12 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: dailyverizonemail
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running dailyverizonemails && cd /node-server/utils && node email_cron.js && echo dailyverizonemails Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: READ_ONLY_DATABASE_PARAMS_HOST
                  value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
                - name: READ_ONLY_DATABASE_PARAMS_PORT
                  value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
                - name: READ_ONLY_DATABASE_PARAMS_DBNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_USERNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
                  value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: parsebossqueue
spec:
  schedule: "*/1 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: parsebossqueue
              image: "registry.digitalocean.com/sitefotos2/sitefotos:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running parsebossqueue && cd lib && php maintjobs/parsebossqueue.php && echo parsebossqueue Done"]
              env:
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: processbossincomingqueue
spec:
  schedule: "*/1 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: processbossincomingqueue
              image: "registry.digitalocean.com/sitefotos2/sitefotos:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running processbossincomingqueue && cd lib && php maintjobs/processbossincomingqueue.php && echo processbossincomingqueue Done"]
              env:
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: sendnewsharedphotoemails
spec:
  schedule: "0 10 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: sendnewsharedphotoemails
              image: "registry.digitalocean.com/sitefotos2/sitefotos:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running sendnewsharedphotoemails && cd lib && php maintjobs/send_new_shared_photo_emails.php && echo sendnewsharedphotoemails Done"]
              env:
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: sendnewvendorphotoemails
spec:
  schedule: "0 10 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: sendnewvendorphotoemails
              image: "registry.digitalocean.com/sitefotos2/sitefotos:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running sendnewvendorphotoemails && cd lib && php maintjobs/send_new_vendor_photo_emails.php && echo sendnewvendorphotoemails Done"]
              env:
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: node-background-tasks-every-three-hours
spec:
  schedule: "0 */3 * * *"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: node-background-tasks-every-three-hours
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running node-background-tasks-every-three-hours && cd /node-server/crons && node background.tasks.every.three.hours.cron.js && echo node-background-tasks-every-three-hours Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: READ_ONLY_DATABASE_PARAMS_HOST
                  value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
                - name: READ_ONLY_DATABASE_PARAMS_PORT
                  value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
                - name: READ_ONLY_DATABASE_PARAMS_DBNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_USERNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
                  value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
                - name: POSTGRES_PARAMS_HOST
                  value: "${POSTGRES_PARAMS_HOST}"
                - name: POSTGRES_PARAMS_PORT
                  value: "${POSTGRES_PARAMS_PORT}"
                - name: FLEETDB_PARAMS_DBNAME
                  value: "${FLEETDB_PARAMS_DBNAME}"
                - name: FLEETDB_PARAMS_USERNAME
                  value: "${FLEETDB_PARAMS_USERNAME}"
                - name: FLEETDB_PARAMS_PASSWORD
                  value: "${FLEETDB_PARAMS_PASSWORD}"
                - name: QUICKBOOKS_CLIENT_ID
                  value: "${QUICKBOOKS_CLIENT_ID}"
                - name: QUICKBOOKS_CLIENT_SECRET
                  value: "${QUICKBOOKS_CLIENT_SECRET}"
                - name: BASE_URL
                  value: "${BASE_URL}"
                - name: CHARGIFY_SUB_DOMAIN
                  value: "${CHARGIFY_SUB_DOMAIN}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
                - name: CHARGIFY_API_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only charge in master
                      name: chargify-api-key
                      key: CHARGIFY_API_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: node-background-tasks-every-twenty-four-hours
spec:
  schedule: "0 0 * * *"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: node-background-tasks-every-twenty-four-hours
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              resources:
                requests:
                  cpu: "500m"
                  memory: "256Mi"
                limits:
                  cpu: "1"
                  memory: "1024Mi"
              imagePullPolicy: IfNotPresent
              workingDir: /node-server  
              command: ["/bin/bash", "-c"]
              args: ["echo Running node-background-tasks-every-twenty-four-hours && node crons/background.tasks.every.twenty.four.hours.cron.js && echo node-background-tasks-every-twenty-four-hours Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: READ_ONLY_DATABASE_PARAMS_HOST
                  value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
                - name: READ_ONLY_DATABASE_PARAMS_PORT
                  value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
                - name: READ_ONLY_DATABASE_PARAMS_DBNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_USERNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
                  value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
                - name: POSTGRES_PARAMS_HOST
                  value: "${POSTGRES_PARAMS_HOST}"
                - name: POSTGRES_PARAMS_PORT
                  value: "${POSTGRES_PARAMS_PORT}"
                - name: FLEETDB_PARAMS_DBNAME
                  value: "${FLEETDB_PARAMS_DBNAME}"
                - name: FLEETDB_PARAMS_USERNAME
                  value: "${FLEETDB_PARAMS_USERNAME}"
                - name: FLEETDB_PARAMS_PASSWORD
                  value: "${FLEETDB_PARAMS_PASSWORD}"
                - name: QUICKBOOKS_CLIENT_ID
                  value: "${QUICKBOOKS_CLIENT_ID}"
                - name: QUICKBOOKS_CLIENT_SECRET
                  value: "${QUICKBOOKS_CLIENT_SECRET}"
                - name: BASE_URL
                  value: "${BASE_URL}"
                - name: CHARGIFY_SUB_DOMAIN
                  value: "${CHARGIFY_SUB_DOMAIN}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
                - name: CHARGIFY_API_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only charge in master
                      name: chargify-api-key
                      key: CHARGIFY_API_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: node-background-tasks-every-hour
spec:
  schedule: "0 * * * *"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: node-background-tasks-every-hour
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              resources:
                requests:
                  cpu: "500m"
                  memory: "256Mi"
                limits:
                  cpu: "1"
                  memory: "1024Mi"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running node-background-tasks-every-hour && cd /node-server/crons && node background.tasks.every.hour.cron.js && echo node-background-tasks-every-hour Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: READ_ONLY_DATABASE_PARAMS_HOST
                  value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
                - name: READ_ONLY_DATABASE_PARAMS_PORT
                  value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
                - name: READ_ONLY_DATABASE_PARAMS_DBNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_USERNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
                  value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
                - name: REDIS_PERSISTENT_SERVICE_HOST
                  value: "${REDIS_PERSISTENT_SERVICE_HOST}"
                - name: REDIS_PERSISTENT_SERVICE_PORT
                  value: "${REDIS_PERSISTENT_SERVICE_PORT}"
                - name: REDIS_PERSISTENT_USERNAME
                  value: "${REDIS_PERSISTENT_USERNAME}"
                - name: REDIS_PERSISTENT_PASSWORD
                  value: "${REDIS_PERSISTENT_PASSWORD}"
                - name: QUICKBOOKS_CLIENT_ID
                  value: "${QUICKBOOKS_CLIENT_ID}"
                - name: QUICKBOOKS_CLIENT_SECRET
                  value: "${QUICKBOOKS_CLIENT_SECRET}"
                - name: BASE_URL
                  value: "${BASE_URL}"
                - name: CHARGIFY_SUB_DOMAIN
                  value: "${CHARGIFY_SUB_DOMAIN}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
                - name: CHARGIFY_API_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only charge in master
                      name: chargify-api-key
                      key: CHARGIFY_API_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: node-background-tasks-every-fifteen-minutes
spec:
  schedule: "*/15 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: node-background-tasks-every-fifteen-minutes
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running node-background-tasks-every-fifteen-minutes && cd /node-server/crons && node background.tasks.every.fifteen.minutes.cron.js && echo node-background-tasks-every-fifteen-minutes Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: READ_ONLY_DATABASE_PARAMS_HOST
                  value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
                - name: READ_ONLY_DATABASE_PARAMS_PORT
                  value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
                - name: READ_ONLY_DATABASE_PARAMS_DBNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_USERNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
                  value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
                - name: POSTGRES_PARAMS_HOST
                  value: "${POSTGRES_PARAMS_HOST}"
                - name: POSTGRES_PARAMS_PORT
                  value: "${POSTGRES_PARAMS_PORT}"
                - name: FLEETDB_PARAMS_DBNAME
                  value: "${FLEETDB_PARAMS_DBNAME}"
                - name: FLEETDB_PARAMS_USERNAME
                  value: "${FLEETDB_PARAMS_USERNAME}"
                - name: FLEETDB_PARAMS_PASSWORD
                  value: "${FLEETDB_PARAMS_PASSWORD}"
                - name: QUICKBOOKS_CLIENT_ID
                  value: "${QUICKBOOKS_CLIENT_ID}"
                - name: QUICKBOOKS_CLIENT_SECRET
                  value: "${QUICKBOOKS_CLIENT_SECRET}"
                - name: BASE_URL
                  value: "${BASE_URL}"
                - name: CHARGIFY_SUB_DOMAIN
                  value: "${CHARGIFY_SUB_DOMAIN}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
                - name: CHARGIFY_API_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only charge in master
                      name: chargify-api-key
                      key: CHARGIFY_API_KEY
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: node-background-tasks-every-six-hours
spec:
  schedule: "0 */6 * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: node-background-tasks-every-six-hours
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running node-background-tasks-every-six-hours && cd /node-server/crons && node background.tasks.every.six.hours.cron.js && echo node-background-tasks-every-six-hours Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_ADAPTOR
                  value: "PDO_MYSQL"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: READ_ONLY_DATABASE_PARAMS_HOST
                  value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
                - name: READ_ONLY_DATABASE_PARAMS_PORT
                  value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
                - name: READ_ONLY_DATABASE_PARAMS_DBNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_USERNAME
                  value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
                - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
                  value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
                - name: POSTGRES_PARAMS_HOST
                  value: "${POSTGRES_PARAMS_HOST}"
                - name: POSTGRES_PARAMS_PORT
                  value: "${POSTGRES_PARAMS_PORT}"
                - name: FLEETDB_PARAMS_DBNAME
                  value: "${FLEETDB_PARAMS_DBNAME}"
                - name: FLEETDB_PARAMS_USERNAME
                  value: "${FLEETDB_PARAMS_USERNAME}"
                - name: FLEETDB_PARAMS_PASSWORD
                  value: "${FLEETDB_PARAMS_PASSWORD}"
                - name: QUICKBOOKS_CLIENT_ID
                  value: "${QUICKBOOKS_CLIENT_ID}"
                - name: QUICKBOOKS_CLIENT_SECRET
                  value: "${QUICKBOOKS_CLIENT_SECRET}"
                - name: BASE_URL
                  value: "${BASE_URL}"
                - name: CHARGIFY_SUB_DOMAIN
                  value: "${CHARGIFY_SUB_DOMAIN}"
                - name: MANDRILL_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only email in master
                      name: mandrill-api-key
                      key: MANDRILL_KEY
                - name: CHARGIFY_API_KEY
                  valueFrom:
                    secretKeyRef:
                      optional: true # only charge in master
                      name: chargify-api-key
                      key: CHARGIFY_API_KEY
---

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: node-cleanup-logs
spec:
  schedule: "0 0 */10 * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: do-registry
          containers:
            - name: node-cleanup-logs
              image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
              imagePullPolicy: IfNotPresent
              command: ["/bin/bash", "-c"]
              args: ["echo Running node-cleanup-logs && cd /node-server/crons && node cleanup.logs.cron.js && echo node-cleanup-logs Done"]
              env:
                - name: NODE_ENV
                  value: "${NODE_ENV}"
                - name: K8S_BRANCH 
                  value: "${BRANCH}"
                - name: DATABASE_PARAMS_HOST
                  value: "${DATABASE_PARAMS_HOST}"
                - name: DATABASE_PARAMS_PORT
                  value: "${DATABASE_PARAMS_PORT}"
                - name: DATABASE_PARAMS_DBNAME
                  value: "${DATABASE_PARAMS_DBNAME}"
                - name: DATABASE_PARAMS_USERNAME
                  value: "${DATABASE_PARAMS_USERNAME}"
                - name: DATABASE_PARAMS_PASSWORD
                  value: "${DATABASE_PARAMS_PASSWORD}"
                - name: WORKORDERS_S3_ENDPOINT
                  value: "${WORKORDERS_S3_ENDPOINT}"
                - name: WORKORDERS_S3_REGION
                  value: "${WORKORDERS_S3_REGION}"
                - name: WORKORDERS_S3_ACCESS_KEY
                  value: "${WORKORDERS_S3_ACCESS_KEY}"
                - name: WORKORDERS_S3_SECRET_KEY
                  value: "${WORKORDERS_S3_SECRET_KEY}"
