const { parentPort, workerData } = require('worker_threads');
const { awaitQuery, getDirectConnection } = require('../utils/db');
const { addServiceHistoryAuditTrail } = require('../services/services-trades-equipment.services');

const BATCH_SIZE = 1000; // Process 1000 records at a time (increased for performance)

// Bulk audit trail function for performance
async function addBulkServiceHistoryAuditTrail(connection, vendorId, userContactId, serviceIds, auditEntries) {
  if (!serviceIds.length || !auditEntries.length) return;

  const values = [];
  const placeholders = [];

  // Build bulk insert data
  for (const serviceId of serviceIds) {
    for (const entry of auditEntries) {
      values.push(
        vendorId,
        userContactId,
        serviceId,
        entry.action,
        entry.field,
        null, // oldValue
        String(entry.value), // newValue
        'Bulk update via cross-page selection'
      );
      placeholders.push('(?, ?, ?, ?, ?, ?, ?, ?)');
    }
  }

  if (values.length === 0) return;

  const sql = `
    INSERT INTO sitefotos_service_history_audit_trail
    (sshat_vendor_id, sshat_user_id, sshat_service_id, sshat_action, sshat_field_changed,
     sshat_old_value, sshat_new_value, sshat_note)
    VALUES ${placeholders.join(', ')}
  `;

  await connection.execute(sql, values);
}

// Main worker function
async function processBulkUpdate() {
  const {
    jobId,
    filters,
    sorters,
    start,
    end,
    updateData,
    vendorId,
    restrictedUser,
    userContactId
  } = workerData;

  let connection;

  try {
    console.log(`[Worker ${jobId}] Starting bulk update process...`);
    console.log(`[Worker ${jobId}] Vendor ID: ${vendorId}, Start: ${start}, End: ${end}`);
    console.log(`[Worker ${jobId}] Update data:`, updateData);

    // Get all filtered service IDs using the existing helper
    const serviceIds = await getFilteredServiceIds(
      vendorId,
      restrictedUser,
      userContactId,
      filters,
      sorters,
      start,
      end
    );

    console.log(`[Worker ${jobId}] Found ${serviceIds.length} service IDs to process`);
    
    const total = serviceIds.length;
    
    // Send initial job update with total count
    parentPort.postMessage({
      type: 'updateJob',
      jobId,
      data: {
        total,
        processed: 0,
        status: 'processing'
      }
    });
    
    if (total === 0) {
      parentPort.postMessage({
        type: 'complete',
        jobId,
        data: {
          status: 'completed',
          total: 0,
          processed: 0,
          message: 'No services found matching the criteria'
        }
      });
      return;
    }
    
    // Process in batches
    let processed = 0;
    const errors = [];
    const startTime = Date.now();
    
    // Get a connection for batch processing
    connection = await getDirectConnection();
    await connection.beginTransaction();
    
    for (let i = 0; i < serviceIds.length; i += BATCH_SIZE) {
      const batch = serviceIds.slice(i, Math.min(i + BATCH_SIZE, serviceIds.length));
      
      try {
        await processBatch(connection, batch, updateData, vendorId, userContactId);
        processed += batch.length;
        
        // Calculate ETA
        const elapsed = Date.now() - startTime;
        const rate = processed / elapsed; // items per ms
        const remaining = total - processed;
        const eta = remaining / rate; // ms remaining

        console.log(`[Worker ${jobId}] Progress: ${processed}/${total} (${Math.round((processed / total) * 100)}%)`);

        // Send progress update
        parentPort.postMessage({
          type: 'progress',
          jobId,
          data: {
            processed,
            total,
            percentage: Math.round((processed / total) * 100),
            eta: isNaN(eta) || !isFinite(eta) ? null : Math.round(eta / 1000) // seconds, handle NaN/Infinity
          }
        });
        
      } catch (error) {
        console.error(`Error processing batch ${Math.floor(i / BATCH_SIZE) + 1}:`, error);
        errors.push({
          batch: Math.floor(i / BATCH_SIZE) + 1,
          error: error.message,
          ids: batch
        });
        
        // Continue processing other batches
        processed += batch.length; // Count as processed even if failed
      }
      
      // Small delay between batches to avoid overwhelming the database
      if (i + BATCH_SIZE < serviceIds.length) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
    
    // Commit all changes
    await connection.commit();
    
    // Send completion message
    parentPort.postMessage({
      type: 'complete',
      jobId,
      data: {
        status: errors.length > 0 ? 'completed_with_errors' : 'completed',
        total,
        processed,
        errors: errors.length > 0 ? errors : undefined,
        successCount: processed - (errors.length * BATCH_SIZE),
        message: `Successfully processed ${processed} of ${total} services`
      }
    });
    
  } catch (error) {
    console.error('Worker fatal error:', error);
    
    // Rollback on fatal error
    if (connection) {
      try {
        await connection.rollback();
      } catch (rollbackError) {
        console.error('Rollback error:', rollbackError);
      }
    }
    
    parentPort.postMessage({
      type: 'error',
      jobId,
      data: {
        status: 'failed',
        error: error.message,
        stack: error.stack
      }
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Helper function to get filtered service IDs (copied from controller)
async function getFilteredServiceIds(vendorId, restrictedUser, userContactId, filters, sorters, startDate, endDate) {
  const fieldMap = {
    'Service': 'svc.swsd_service_name',
    'Status': 'svc.swsd_service_status',
    'Source': 'svc.swsd_service_source',
    'TradeTitle': 'st.st_trade',
    'Site': 'mb.mb_nickname',
    'site_id': 'mb.mb_id',
    'Zone': 'gz.sgz_geo_zone',
    'InternalManager': `CONCAT(ic.sf_contact_fname, ' ', ic.sf_contact_lname)`,
    'Notes': 'svc.swsd_notes',
    'WorkOrder': 'swos.swos_workorder_id',
    'ExternalWorkOrder': 'swo.swo_external_id',
    'ParentFormId': 'sfs.sfs_form_id',
    'Form': 'sfs.sfs_form_name',
    'People': 'svc.swsd_people',
    'Hours': 'svc.swsd_hours',
    'Start': 'sfs.sfs_checkin',
    'Stop': 'sfs.sfs_checkout',
    'snow_inch': 'svc.swsd_snow_inch',
    'swsd_event': 'svc.swsd_event',
    'ProviderText': `CONCAT(mv.vendor_fname, ' ', mv.vendor_lname)`,
    'Client': `icc.sf_contact_company_name`,
    'swsd_qb_invoice_id': 'svc.swsd_qb_invoice_id',
    'swsd_qb_bill_id': 'svc.swsd_qb_bill_id',
    'UnixTime': 'svc.swsd_date_time',
    'swsd_vendor_sequence_id': 'svc.swsd_vendor_sequence_id',
    'form_submission_id': 'sfs.sfs_id',
    'ClientPricingUnit': 'CASE WHEN mb.mb_client_profile_data IS NOT NULL THEN JSON_UNQUOTE(JSON_EXTRACT(mb.mb_client_profile_data, "$.pricing_unit")) ELSE NULL END',
    'ContractorPricingUnit': 'CASE WHEN mb.mb_contractor_profile_data IS NOT NULL THEN JSON_UNQUOTE(JSON_EXTRACT(mb.mb_contractor_profile_data, "$.pricing_unit")) ELSE NULL END'
  };

  let baseQuery = `
    FROM sitefotos_wp_services_data svc
    LEFT JOIN maptile_building mb ON mb.mb_id = svc.swsd_site_id
    LEFT JOIN sitefotos_geofence_zones gz ON gz.sgz_zone_id = mb.mb_zone_id
    LEFT JOIN maptile_city city ON city.CityId = mb.mb_city
    LEFT JOIN maptile_state state ON state.id = mb.mb_state
    LEFT JOIN sitefotos_contacts ic ON ic.sf_contact_id = mb.mb_manager
    LEFT JOIN sitefotos_contacts_company_view icc ON icc.sf_contact_id = mb.mb_client
    LEFT JOIN maptile_vendors mv ON mv.vendor_id = svc.swsd_uploader_vid
    LEFT JOIN vendor_services vs ON vs.vs_service_id = svc.swsd_service_id
    LEFT JOIN sitefotos_trades st ON st.st_id = vs.vs_trade_id
    LEFT JOIN sitefotos_forms_submitted sfs ON sfs.sfs_id = svc.swsd_form_submission_reference
    LEFT JOIN sitefotos_work_orders_submissions swos ON swos.swos_form_submission_id = svc.swsd_form_submission_reference
    LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id
    LEFT JOIN (
      SELECT sf_contact_email, CONCAT(sf_contact_fname, ' ', sf_contact_lname) AS InternalContact
      FROM sitefotos_contacts
      WHERE sf_contact_vendorid = ?
      GROUP BY sf_contact_email
    ) ic2 ON ic2.sf_contact_email = svc.swsd_email
  `;

  let whereClauses = [
    `(svc.swsd_vendor_id = ? OR svc.swsd_uploader_vid = ?)`,
    `svc.swsd_date_time >= ?`,
    `svc.swsd_date_time < ?`,
    `svc.swsd_service_status != 'TEMPLOG'`
  ];

  let queryParams = [
    vendorId, // for ic2 subquery
    vendorId, // for main WHERE svc.swsd_vendor_id
    vendorId, // for main WHERE svc.swsd_uploader_vid
    startDate,
    endDate
  ];

  if (restrictedUser && userContactId > 0) {
    whereClauses.push(`(mb.mb_manager = ? OR mb.mb_manager_secondary = ? OR mb.mb_manager_third = ?)`);
    queryParams.push(userContactId, userContactId, userContactId);
  }

  // Apply filters
  if (filters && Array.isArray(filters)) {
    filters.forEach(filter => {
      const dbField = fieldMap[filter.field] || filter.field;
      if (!dbField) return;

      switch (filter.type) {
        case 'like':
          whereClauses.push(`${dbField} LIKE ?`);
          queryParams.push(`%${filter.value}%`);
          break;
        case '=':
          whereClauses.push(`${dbField} = ?`);
          queryParams.push(filter.value);
          break;
        case 'in':
        case 'function':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            const placeholders = filter.value.map(() => '?').join(',');
            whereClauses.push(`${dbField} IN (${placeholders})`);
            queryParams.push(...filter.value);
          } else if (!Array.isArray(filter.value) && filter.value) {
            whereClauses.push(`${dbField} = ?`);
            queryParams.push(filter.value);
          }
          break;
        case '>=':
          whereClauses.push(`${dbField} >= ?`);
          queryParams.push(filter.value);
          break;
        case '<=':
          whereClauses.push(`${dbField} <= ?`);
          queryParams.push(filter.value);
          break;
      }
    });
  }

  const sql = `SELECT DISTINCT svc.swsd_id ${baseQuery} WHERE ${whereClauses.join(' AND ')}`;
  const results = await awaitQuery(sql, queryParams);
  return results.map(row => row.swsd_id);
}

// Process a batch of service IDs
async function processBatch(connection, serviceIds, updateData, vendorId, userContactId) {
  const { snowTotal, eventName, hours, people, status } = updateData;
  
  // Build UPDATE query based on provided fields
  const setClause = [];
  const params = [];
  const auditEntries = [];
  
  if (snowTotal !== undefined && snowTotal !== null) {
    setClause.push('swsd_snow_inch = ?');
    params.push(snowTotal);
    auditEntries.push({ action: 'SNOW', field: 'swsd_snow_inch', value: snowTotal });
  }
  if (eventName !== undefined && eventName !== null) {
    setClause.push('swsd_event = ?');
    params.push(eventName);
    auditEntries.push({ action: 'EVENT', field: 'swsd_event', value: eventName });
  }
  if (hours !== undefined && hours !== null) {
    setClause.push('swsd_hours = ?');
    params.push(hours);
    auditEntries.push({ action: 'HOURS', field: 'swsd_hours', value: hours });
  }
  if (people !== undefined && people !== null) {
    setClause.push('swsd_people = ?');
    params.push(people);
    auditEntries.push({ action: 'PEOPLE', field: 'swsd_people', value: people });
  }
  if (status !== undefined && status !== null) {
    setClause.push('swsd_service_status = ?');
    params.push(status);
    auditEntries.push({ action: 'STATUS_CHANGE', field: 'swsd_service_status', value: status });
  }
  
  if (setClause.length === 0) {
    throw new Error('No update fields provided');
  }
  
  // Add service IDs to params
  params.push(...serviceIds);
  
  // Execute update
  const query = `
    UPDATE sitefotos_wp_services_data 
    SET ${setClause.join(', ')}
    WHERE swsd_id IN (${serviceIds.map(() => '?').join(',')})
    AND swsd_vendor_id = ?
  `;
  params.push(vendorId); // Add vendor check for safety
  
  await connection.execute(query, params);
  
  // Add audit trail entries using bulk insert for performance
  if (auditEntries.length > 0) {
    try {
      await addBulkServiceHistoryAuditTrail(
        connection,
        vendorId,
        userContactId || 0,
        serviceIds,
        auditEntries
      );
      console.log(`[Worker] Added bulk audit trail for ${serviceIds.length} services (${auditEntries.length} entries each)`);
    } catch (auditError) {
      console.error(`Error adding bulk audit trail:`, auditError);
      // Continue processing even if audit fails
    }
  }
}

// Start processing
processBulkUpdate();
