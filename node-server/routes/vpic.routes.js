const express = require('express');
const controller = require('../controllers/vpic.controller');
const caseFMSController = require('../controllers/casefms-controller');

const router = express.Router();

router.get('/bodata', controller.backOfficeData);
router.get('/get-trades', controller.getTrades);
router.get('/get-vendor-app-settings', controller.getVendorAppSettings);
router.post('/set-vendor-app-settings', controller.setVendorAppSettings);
router.get('/send-proposal', controller.sendProposal);
router.get('/integrations',controller.getUserIntegrations);
router.post('/ww-req-cert-hist-data', controller.weatherWorksRequestCertifiedHistoricalData);
router.post('/mc-neil-report', controller.mcNeilReport);
router.get('/time-zone', controller.getTimeZoneInfo);
router.post('/report-broken-camera', controller.reportBrokenCameraToGoogleSheet);
router.post('/casefms/sendMagicLink', caseFMSController.sendMagicLink);
router.post('/casefms/generateCommonJWT', caseFMSController.generateCommonJWT);
router.get('/vendor-services', controller.getVendorServices);
router.get('/time-sync-delay', controller.timeSyncDelay);
router.get('/wells-fargo-maps', controller.wellsFargoMaps);
router.get('/wells-fargo-landscape-maps', controller.wellsFargoLandscapeMaps);
router.post('/settings', controller.setVendorSettings);
router.get('/inactive-materials', controller.getInactiveMaterials);
router.get('/inactive-services', controller.getInactiveServices);
router.get('/service-types', controller.getServiceTypes);
router.get('/getlivemappresets', controller.getLiveMapPresets);
//Todo: Nasir, maybe needs to be done per vendor login
router.get('/updatebuildingtimezones', controller.updateBuildingTimezones);
module.exports = router
