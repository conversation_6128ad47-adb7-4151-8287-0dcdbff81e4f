require("../sentry/instrument-index.js");
const { awaitQuery, deleteObj } = require('../utils/db');
const { s3Client, PutObjectCommand } = require('../utils/s3');
const { Readable } = require('stream');

const LOG_CLEANUP_CONFIG = {
    s3Bucket: 'sitefotos-logs-backup',
    tables: [
        {
            name: 'sitefotos_api_logs',
            cleanupThresholdDays: 180,
            timestampColumn: 'sal_timestamp',
            primaryKey: 'sal_id',
        },
        {
            name: 'sitefotos_webhooks_logs',
            cleanupThresholdDays: 180,
            timestampColumn: 'swl_created_at',
            primaryKey: 'swl_id',
        },
        {
            name: 'sitefotos_boss_tm_log',
            cleanupThresholdDays: 180,
            timestampColumn: 'sbtl_datetime',
            primaryKey: 'sbtl_id',
        },
        {
            name: 'sitefotos_service_history_audit_trail',
            cleanupThresholdDays: 365,
            timestampColumn: 'sshat_created_at',
            primaryKey: 'sshat_id',
        }
    ]
};

async function backupToS3(tableName, data) {
    const now = new Date();
    const dateString = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
    const key = `${tableName}_${dateString}.csv`;

    const csvData = data.map(row => Object.values(row).join(',')).join('\n');
    const buffer = Buffer.from(csvData, 'utf-8');

    const command = new PutObjectCommand({
        Bucket: LOG_CLEANUP_CONFIG.s3Bucket,
        Key: key,
        Body: buffer,
        ContentType: 'text/csv',
        ContentLength: buffer.length,
    });

    await s3Client.send(command);
    console.log(`Successfully backed up ${data.length} records from ${tableName} to S3 bucket ${LOG_CLEANUP_CONFIG.s3Bucket}`);
}

async function main() {
    console.log('Starting log cleanup cron job...');

    for (const tableConfig of LOG_CLEANUP_CONFIG.tables) {
        const { name: tableName, cleanupThresholdDays, timestampColumn } = tableConfig;
        try {
            console.log(`Processing table: ${tableName}`);

            const thresholdDate = new Date();
            thresholdDate.setDate(thresholdDate.getDate() - cleanupThresholdDays);

            const oldRecords = await awaitQuery(
                `SELECT * FROM ?? WHERE ?? < ?`,
                [tableName, timestampColumn, thresholdDate]
            );

            if (oldRecords.length > 0) {
                console.log(`Found ${oldRecords.length} old records in ${tableName}.`);
                await backupToS3(tableName, oldRecords);

                const idsToDelete = oldRecords.map(record => record[tableConfig.primaryKey]);
                if (idsToDelete.length > 0) {
                    await awaitQuery(`DELETE FROM ?? WHERE ?? IN (?)`, [tableName, tableConfig.primaryKey, idsToDelete]);
                    console.log(`Successfully deleted ${idsToDelete.length} records from ${tableName}.`);
                }
            } else {
                console.log(`No old records found in ${tableName}.`);
            }
        } catch (error) {
            console.error(`Error processing table ${tableConfig.name}:`, error);
        }
    }

    console.log('Log cleanup cron job finished.');
    process.exit();
}

main();
