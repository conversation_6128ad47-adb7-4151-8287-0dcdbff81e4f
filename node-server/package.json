{"name": "express-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node --inspect=0.0.0.0:9229 index.js", "watch": "nodemon --inspect=0.0.0.0:9229 index.js", "test": "mocha --exit", "start-mobile": "node index-mobile.js", "watch-mobile": "nodemon index-mobile.js", "start-app-data": "node index-app-data.js", "watch-app-data": "nodemon --inspect=0.0.0.0:9231 index-app-data.js", "start-api": "node index-api.js", "watch-api": "nodemon index-api.js"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.51.0", "@aws-sdk/client-ses": "^3.645.0", "@aws-sdk/s3-presigned-post": "^3.51.0", "@google-cloud/storage": "^7.6.0", "@langchain/pinecone": "^0.0.7", "@mailchimp/mailchimp_transactional": "^1.0.50", "@pinecone-database/pinecone": "^2.2.2", "@sentry/node": "^9.14.0", "@sentry/profiling-node": "^9.14.0", "@supabase/supabase-js": "^2.2.0", "@turf/turf": "^6.5.0", "addresser": "^1.1.19", "apns2": "^11.7.0", "aws-sdk": "^2.1093.0", "bcrypt": "5.1.0", "bullmq": "^5.49.0", "canvas": "^2.10.1", "cheerio": "^1.0.0-rc.12", "chrono-node": "^2.2.7", "cookie-parser": "^1.4.7", "dayjs": "^1.10.5", "dotenv": "^10.0.0", "ejs": "^3.1.8", "express": "^5.1.0", "firebase-admin": "^12.1.0", "generate-password": "^1.7.1", "geo-tz": "^7.0.6", "google-auth-library": "^9.11.0", "googleapis": "^140.0.1", "gpt-3-encoder": "^1.1.4", "hashids": "2.2.10", "https-proxy-agent": "^7.0.2", "intuit-oauth": "^4.0.0", "ioredis": "^5.6.1", "json": "^11.0.0", "jsonpath": "^1.1.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.2.9", "libphonenumber-js": "^1.9.19", "lodash": "^4.17.21", "luxon": "^1.27.0", "mandrill-api": "^1.0.45", "merge-base64": "^1.0.4", "mocha": "^9.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.43", "mongoose": "^5.12.11", "multer": "^1.4.5-lts.1", "mustache": "^4.2.0", "mysql2": "^3.11.5", "node-geocoder": "^3.27.0", "node-html-parser": "^6.1.1", "node-pdftk": "^2.1.3", "node-redis-pubsub": "^5.0.0", "nodemon": "^2.0.7", "numeral": "^2.0.6", "openai": "^4.0.0", "openapi-snippet": "github:kuchhadiyaa/openapi-snippet", "p-limit": "^6.2.0", "papaparse": "^5.4.1", "path": "^0.12.7", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "phone-formatter": "^0.0.2", "php-serialize": "^4.0.2", "php-unserialize": "0.0.1", "proj4": "^2.8.0", "redis": "^4.7.0", "request": "^2.88.2", "sanitize-html": "^2.13.0", "sha1": "^1.1.1", "sharp": "^0.33.5", "smartsheet": "^4.2.1", "stripe": "^11.18.0", "swagger-jsdoc": "^6.1.0", "uniqid": "^5.3.0", "uuid": "^8.3.2", "wkhtmltopdf": "^0.4.0", "xss": "^1.0.15", "zod": "^3.23.8"}, "devDependencies": {"sinon": "^11.1.1", "twilio": "^3.63.1", "uniqid": "^5.3.0", "ws": "^8.14.2"}}