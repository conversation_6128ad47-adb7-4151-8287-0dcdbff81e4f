const {unserialize} = require('php-unserialize');
const {awaitQuery, insertObj, updateObj,awaitSafeQuery} = require('../utils/db')

const {validateVendorAccessCode,UserPrivilege,login} = require("../utils/vendor")
const {sendToGoogleChat} = require("../utils/common")
const wellsfargoServices = require('../services/wellsfargo.services');
const chaseServices = require('../services/chase-services');
const verizonServices = require('../services/verizon-services');

const WELLSFARGO_VENDOR_ID = 15183;
const CHASE_VENDOR_ID = 10897;
var FormData = require('form-data');
const { find } = require('geo-tz')

const getTimeZoneInfo = async (req, res, next) => {
  try {
    const tz = find(req.query.lat, req.query.lng)
    res.json(tz)

  } catch (ex) {
    console.log(ex)
    next(ex)
  }

}

const wellsFargoMaps = async (req, res, next) => {
  try {
    let sql = `SELECT
	sscme.sscm_site_id,
	sscme.sscm_vendor_id,
	sscme.scs_client_internal_id,
	mv.vendor_company_name,
	smll.sml_urlkeys as sml_urlkeys,
	smll.sml_dates as sml_dates,
	smll.mb_address1,
	smll.cityname,
	smll.state_abv_name,
	smll.mb_zip_code
FROM
	sitefotos_site_client_mapping_extended_ungrouped AS sscme
LEFT JOIN maptile_vendors AS mv ON
	sscme.sscm_vendor_id = mv.vendor_id
LEFT join maptile_building mb2 on sscme.sscm_site_id = mb2.mb_id
LEFT JOIN (
	SELECT
		sml_building_id,
		sml_vendor_id,
		mb_address1,
		(
		SELECT
			City
		FROM
			maptile_city
		WHERE
			CityId = mb_city) as cityname,
		(
		SELECT
			state_abv_name
		FROM
			maptile_state
		WHERE
			id = mb_state) AS state_abv_name,
		mb_zip_code,
		GROUP_CONCAT(CONCAT('https://sitefotos.com/vpics/guestmap?', sml_urlkey) ORDER BY sml_modified_at) AS sml_urlkeys,
		GROUP_CONCAT(sml_modified_at ORDER BY sml_modified_at) AS sml_dates
	FROM
		sitefotos_map_layers
	left join maptile_building mb on
		sml_building_id = mb_id
	GROUP BY
		sml_building_id,
		sml_vendor_id ) AS smll ON
	sscme.sscm_site_id = smll.sml_building_id
	AND sscme.sscm_vendor_id = smll.sml_vendor_id
WHERE
	sscme.scs_client_vendor_id = 15183
	and sml_urlkeys is not null
	and FIND_IN_SET('Snow Removal', mb2.mb_contract_type) > 0`
    let result = await awaitQuery(sql)
    let data = {
      result: result
    }
    res.render('misc/wells-fargo.ejs', { data: data });
  } catch (ex) {
    console.log(ex)
    next(ex)
  }
}

const wellsFargoLandscapeMaps = async (req, res, next) => {
  try {

    let data = {
      result: null
    }
    res.render('misc/wells-fargo-landscape-maps.ejs', { data: data });
  } catch (ex) {
    console.log(ex)
    next(ex)
  }
}
const timeSyncDelay = async (req, res) => {
  try {

      const testId = Math.floor(Math.random() * 1000000);
      await insertObj('bg_test', { id: testId});
      const insertTime = new Date();


      let found = false;
      let delay = 0;
      let failureCount = 0;
      while (!found) {
          const rows = await awaitQuery('SELECT * FROM bg_test WHERE id = ?', [testId], { useMainPool: false });
          if (rows.length > 0) {
              found = true;
              const readTime = new Date();
              delay = readTime - insertTime;
          } else {
              failureCount++;
              await new Promise((resolve) => setTimeout(resolve, 1));
          }
      }

      res.json({ delay: `${delay} ms`, failureCount });
  } catch (error) {
      console.error('Error during sync delay test:', error);
      res.status(500).send('Error during sync delay test');
  }
};
const backOfficeData = async (req, res, next) => {
  try {

    const { vendorId, internalUid, isHybrid, isClientViewEnabled, restrictedUser, userContactId } = await login(req.cookies.JWT, req.query?.accessCode);

    const vendorPaymentLock = (await awaitQuery(`SELECT vendor_payment_lock FROM maptile_vendors WHERE vendor_id = ?`, [vendorId]))[0].vendor_payment_lock;


    const forms = await awaitSafeQuery(/*SQL*/`SELECT sfcs_owner FROM( SELECT sf_id, sfcs_contractor, sfcs_owner, REPLACE(TRIM(']' FROM TRIM('[' FROM sf_form_site)), '\"', '') as sites FROM sitefotos_forms LEFT JOIN sitefotos_forms_contractor_share ON sf_id = sfcs_form_id and sfcs_contractor = ? WHERE (sf_vendor_id = ? OR sfcs_contractor = ?) AND sf_active = 1) FORMTABLESELECT WHERE sites != '-2' AND sites <> '' AND sfcs_owner IS NOT NULL AND sites IS NOT NULL`, [vendorId, vendorId, vendorId]);


    let vendors = [...new Set(forms.map((form) => parseInt(form.sfcs_owner)).filter((id) => !isNaN(id)))];
    vendors.push(vendorId);


    let hybrid = await awaitSafeQuery(/*SQL*/`SELECT 
      sf_form_sharer_vendor_id
  FROM 
      sitefotos_forms
  WHERE 
      sf_active = 1 AND sf_form_sharer_vendor_id is not null
      AND (sf_vendor_id = ? OR EXISTS (
          SELECT 1
          FROM sitefotos_forms_contractor_share
          WHERE sfcs_form_id = sf_id AND sfcs_contractor = ?
      ));`, [vendorId, vendorId]);

    hybrid = hybrid.map((row) => parseInt(row.sf_form_sharer_vendor_id)).filter((id) => !isNaN(id));
    vendors = Array.from(new Set([...vendors, ...hybrid]));
    if (isClientViewEnabled) {
      const qry = "SELECT DISTINCT (sscm_vendor_id) from sitefotos_site_client_mapping_extended_grouped where scs_client_vendor_id = ?";

      const clientViewVendors = await awaitQuery(qry, [vendorId]);
      const clientViewVendorIds = clientViewVendors.map(vendor => vendor.sscm_vendor_id);
      vendors = [...new Set([...vendors, ...clientViewVendorIds])];

    }

    const vendorsStr = vendors.join(',');

    const servicesPromise = awaitQuery(/*SQL*/`select vs_service_id, vs_service_name, vs_service_status, vs_service_description, vs_service_category, vs_provider, vs_service_options, IF(vs_vendor_id=?, '1', '0') as service_own,vs_service_quickbook_id, vs_service_type, vs_service_type_id, vs_trade_id,vs_equipment_id, vs_provider_id,sst_service_type,sst_trade_id, vs_material_id from vendor_services left join sitefotos_service_types on vs_service_type_id=sst_id where vs_vendor_id in (${vendorsStr}) AND NOT (vs_provider = 'BOSSLMTM' AND vs_service_status != '1')`, [vendorId])


    const materialsPromise = awaitSafeQuery(`select mat_id, mat_material_name, mat_material_unit, mat_material_status, mat_provider, mat_provider_id, IF(mat_vendor_id=?, '1', '0') as material_own from maptile_materials where mat_vendor_id in (${vendorsStr}) and mat_material_status='1'`, [vendorId]);
    const vendorSettingPromise = awaitSafeQuery(/*SQL*/`select * from sitefotos_vendor_settings where svs_vendor_id=?`, [vendorId]);

    const [services, materials, vendorSetting] = await Promise.all([servicesPromise, materialsPromise, vendorSettingPromise]);

    let qry = `Select mb_id, mb_nickname, IF(mb_user_id='${vendorId}', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code,mb_geo,mb_lat,mb_long, (select City from maptile_city where CityId = mb_city) as cityname, (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client) as customername, (select state_abv_name from maptile_state where id = mb_state ) as statename, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_locked,
    mb_zone_id, mb_client,mb_client_secondary,mb_client_third,mb_contract_type,mb_vendor_internal_id,mb_hybrid, mb_id as sscm_grouped_sites, '0' as CLIENTVIEW From maptile_building where (mb_user_id = '${vendorId}' OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('${vendorId}'))) and mb_user_type = '1' ${restrictedUser && userContactId > 0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''} ORDER BY mb_nickname`;

    if (internalUid && isHybrid) {

      const verizon = await awaitSafeQuery(`Select * from verizon_user_configuration where vuc_user_id=?`, [internalUid]);
      if (verizon && verizon[0]) {
        const siteconfig = JSON.parse(unserialize(verizon[0].vuc_data));
        const values = siteconfig.map((row) => row.mb_id).join(',');
        if (values) {
          qry = `Select mb_id, mb_nickname, IF(mb_user_id='${vendorId}', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code,mb_geo,mb_lat,mb_long, (select City from maptile_city where CityId = mb_city) as cityname, (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client) as customername, (select state_abv_name from maptile_state where id = mb_state ) as statename, '1' as mb_locked, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_zone_id, mb_client,mb_contract_type,mb_vendor_internal_id,mb_hybrid, vendor_company_name as mb_contractor_hybrid, mb_id as sscm_grouped_sites, '0' as CLIENTVIEW From maptile_building left join maptile_vendors on mb_user_id = vendor_id where (mb_id IN (${values})) and mb_user_type = '1' ORDER BY mb_nickname`;
        }
      } else if (isHybrid) {

        qry = `Select mb_id, mb_nickname, IF(mb_user_id='${vendorId}', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code,mb_geo,mb_lat,mb_long,mb_vendor_internal_id,( select vendor_company_name from maptile_vendors where vendor_id = mb_user_id) as Service_Provider, (select City from maptile_city where CityId = mb_city) as cityname, (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client) as customername, (select state_abv_name from maptile_state where id = mb_state ) as statename, '1' as mb_locked, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_zone_id, mb_client,mb_contract_type,mb_user_id,mb_vendor_internal_id,mb_hybrid, vendor_company_name as mb_contractor_hybrid, mb_global_strategic_manager, mb_global_senior_manager, mb_global_regional_manager, mb_global_contract_type, mb_id as sscm_grouped_sites,'0' as CLIENTVIEW From maptile_building left join maptile_vendors on mb_user_id = vendor_id where mb_hybrid_vendor_id='${vendorId}' and mb_user_type = '1' and mb_hybrid='Y' ORDER BY mb_nickname`;
      }


    }
    if (isClientViewEnabled) {
      if (internalUid) {
        const uid = internalUid;
        const qry2 = "Select * from sitefotos_subuser_configuration where ssc_user_id=?";
        const res2 = await awaitSafeQuery(qry2, [uid]);

        if (res2 && res2[0]) {
          const values = res2[0]['ssc_data'] ? res2[0]['ssc_data'].join(",") : '';

          if (values) {
            qry = /*SQL*/`SELECT mb_id, mb_nickname, IF(mb_user_id = '${vendorId}', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code, mb_geo, mb_lat, mb_long, ( SELECT City FROM maptile_city WHERE CityId = mb_city) as cityname, ( SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb_client) as customername, ( SELECT state_abv_name FROM maptile_state WHERE id = mb_state) as statename, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, ( select sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_zone_id, mb_client, mb_contract_type, mb_vendor_internal_id, mb_hybrid, null as clientview_contractors, null as clientview_contractor_ids, mb_id as sscm_grouped_sites, '0' as CLIENTVIEW, '1' as mb_locked FROM maptile_building WHERE (mb_user_id = '${vendorId}' OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('${vendorId}'))) AND mb_user_type = '1' UNION SELECT mb.mb_id, scs_client_site_name as mb_nickname, '0' AS mb_own, mb.mb_external_id, mb.mb_external_src, mb.mb_address1, mb.mb_country, mb.mb_min_zoom, '1' as mb_status, mb.mb_zip_code, mb.mb_geo, mb.mb_lat, mb.mb_long, ( SELECT City FROM maptile_city WHERE CityId = mb.mb_city) as cityname, ( SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb.mb_client) as customername, ( SELECT state_abv_name FROM maptile_state WHERE id = mb.mb_state) as statename, mb.mb_notes, mb.mb_maps, mb.mb_ohours, mb.mb_global_customer_location_id, mb.mb_manager, mb.mb_contractor, ( select sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb.mb_zone_id) as mb_zone, mb_zone_id, mb.mb_client, GROUP_CONCAT(IFNULL(mb.mb_contract_type, '') order by mb.mb_id separator ';') as mb_contract_type, scme.scs_client_internal_id as mb_vendor_internal_id, mb.mb_hybrid, GROUP_CONCAT(REPLACE(vendor_company_name, ',', '') ORDER BY mb.mb_id SEPARATOR ',') AS clientview_contractors, GROUP_CONCAT(vendor_id order by mb.mb_id separator ',') as clientview_contractor_ids, GROUP_CONCAT(sscm_site_id order by mb.mb_id separator  ',') AS sscm_grouped_sites, '1' as CLIENTVIEW,  '1' as mb_locked FROM maptile_building mb JOIN sitefotos_site_client_mapping_extended_ungrouped scme ON mb.mb_id = scme.sscm_site_id LEFT join maptile_vendors on mb_user_id = vendor_id where mb_id IN (${values}) GROUP BY sscm_client_site_id ORDER BY mb_nickname`
          }
        }
      } else {
        qry = /*SQL*/`SELECT mb_id, mb_nickname, IF(mb_user_id = '${vendorId}', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code, mb_geo, mb_lat, mb_long, ( SELECT City FROM maptile_city WHERE CityId = mb_city) as cityname, ( SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb_client) as customername, ( SELECT state_abv_name FROM maptile_state WHERE id = mb_state) as statename, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, ( select sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_zone_id, mb_client, mb_contract_type, mb_vendor_internal_id, mb_hybrid, null as clientview_contractors, null as clientview_contractor_ids, mb_id as sscm_grouped_sites, '0' as CLIENTVIEW,'1' as mb_locked FROM maptile_building WHERE (mb_user_id = '${vendorId}' OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('${vendorId}'))) AND mb_user_type = '1' UNION SELECT mb.mb_id, scs_client_site_name as mb_nickname, '0' AS mb_own, mb.mb_external_id, mb.mb_external_src, mb.mb_address1,mb.mb_country, mb.mb_min_zoom, mb.mb_status, mb.mb_zip_code, mb.mb_geo, mb.mb_lat, mb.mb_long, ( SELECT City FROM maptile_city WHERE CityId = mb.mb_city) as cityname, ( SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb.mb_client) as customername, ( SELECT state_abv_name FROM maptile_state WHERE id = mb.mb_state) as statename, mb.mb_notes, mb.mb_maps, mb.mb_ohours, mb.mb_global_customer_location_id, mb.mb_manager, mb.mb_contractor, ( select sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb.mb_zone_id) as mb_zone, mb_zone_id, mb.mb_client, mb.mb_contract_type, scme.scs_client_internal_id as mb_vendor_internal_id, mb.mb_hybrid, GROUP_CONCAT(REPLACE(vendor_company_name, ',', '') ORDER BY mb.mb_id SEPARATOR ',') AS clientview_contractors, GROUP_CONCAT(vendor_id order by mb.mb_id separator ',') as clientview_contractor_ids, GROUP_CONCAT(sscm_site_id separator ',') AS sscm_grouped_sites, '1' as CLIENTVIEW,'1' as mb_locked FROM maptile_building mb JOIN sitefotos_site_client_mapping_extended_ungrouped scme ON mb.mb_id = scme.sscm_site_id LEFT join maptile_vendors on mb_user_id = vendor_id WHERE scme.scs_client_vendor_id = ${vendorId} GROUP BY sscm_client_site_id ORDER BY mb_nickname`;
      }
    }
    let buildings = await awaitSafeQuery(qry);
    if(vendorId == WELLSFARGO_VENDOR_ID) {
      buildings = await wellsfargoServices.fixBuildings(buildings);
    }
    if(vendorId == CHASE_VENDOR_ID) {
      buildings = await chaseServices.fixBuildings(buildings);
    }
    const contacts = await awaitSafeQuery(`Select * from sitefotos_contacts_companybranch_view where sf_contact_vendorid = ?`, [vendorId]);

    let vendorSettings = vendorSetting[0];
    if (vendorSettings === undefined) {
      //Create dummy with default values.
      vendorSettings = {
        svs_distance_unit: 'mi',
        svs_volume_unit: 'gallon(us)',
        svs_vendor_id: vendorId
      };
    }
    res.json({ buildings, contacts, services, materials, internalUid, isHybrid, vendorPaymentLock, vendorSettings: vendorSettings });
  } catch (e) {
    console.log(e);
    next(e);
  }
}




const getVendorAppSettings = async(req, res) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const query = /*SQL*/`SELECT vendor_app_photo_tags_enabled, vendor_app_photo_tags, vendor_map_icons from maptile_vendors where vendor_id=?`
        let result = (await awaitSafeQuery(query,[vendorId]))[0];
        res.json(result);
    } catch (ex) {
        console.error(ex);
        res.status(500);
        //TODO: res.render will fail because no view engine is specified and will crash node deomon, this needs to be changed everywhere in try catch logic
        //TODO: So node error handling needs to be fixed.
        res.json({ error: JSON.stringify(ex) })
    }
}

const setVendorAppSettings = async(req, res) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const data = {
            vendor_app_photo_tags: JSON.stringify(JSON.parse(req.body.tags)),
            vendor_app_photo_tags_enabled: req.body.enabled,
            vendor_map_icons: JSON.stringify(JSON.parse(req.body.mapicons)),
        };
        await updateObj('maptile_vendors', data,
            ['vendor_id'], [vendorId]
        );
        res.json(true);
    } catch (ex) {
        console.error(ex)
        res.status(500);
        res.json({error: ex.message})
    }
}

const setVendorSettings = async(req, res) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const uData = {};
        if (req.body.svs_distance_unit) {
            uData.svs_distance_unit = req.body.svs_distance_unit;
        }
        if (req.body.svs_volume_unit) {
            uData.svs_volume_unit = req.body.svs_volume_unit;
        }
        if (req.body.svs_overtime_hours) {
          uData.svs_overtime_hours = req.body.svs_overtime_hours;
        }
        if (req.body.svs_overtime_rate_multiplier) {
          uData.svs_overtime_rate_multiplier = req.body.svs_overtime_rate_multiplier;
        }
        if (Object.keys(uData).length > 0) {
          const getSettingsID = await awaitSafeQuery('SELECT svs_id FROM sitefotos_vendor_settings WHERE svs_vendor_id = ?', [vendorId]);
          if (getSettingsID.length === 0) {
            uData.svs_vendor_id = vendorId;
            await insertObj('sitefotos_vendor_settings', uData);
          } else {
            await updateObj('sitefotos_vendor_settings', uData, ['svs_id'], [ getSettingsID[0].svs_id ]);
          }
        }
        const settings = await awaitSafeQuery('SELECT * FROM sitefotos_vendor_settings WHERE svs_vendor_id = ?', [vendorId], {useMainPool: true});
        res.json(settings[0]);
    } catch (ex) {
        console.error(ex)
        res.status(400);
        res.json({error: ex.message})
    }
}

const getUserIntegrations = async(req, res, next)=>{
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const results = await awaitSafeQuery(/*SQL*/`Select sfapi_provider from sitefotos_apikeys WHERE sfapi_vendor_id=? and sfapi_active='1'`,[vendorId])
        const workorders = await awaitSafeQuery(/*SQL*/`Select swos_name, swou_username, swou_production, swou_sc_pin_users from sitefotos_work_orders_users left join sitefotos_work_orders_systems on swou_system_id=swos_id where swou_vendor_id=? and swou_active='1'`,[vendorId])
        let data = {
            boxStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'Box'),
            gtStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'GeoTab'),
            fcStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'FleetComplete'),
            cpStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'ClearPathGPS'),
            fsStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'FleetSharp'),
            vnStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'VerizonFleet'),
            azStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'Azuga'),
            qbStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'QuickBooks'),
            usStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'USFleet'),
            ntStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'NexTraq'),
            bossStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'bosslm'),
            bosstmStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'BOSSLMTM'),
            ezStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'EZFleet'),
            ssStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'SSFleet'),
            linxupStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'LINXUP'),
            intouchStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'INTOUCH'),
            rdStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'RDFLEET'),
            vsStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'VSFLEET'),
            rhStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'RHFLEET'),
            osStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'ONESTEP'),
            scStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'ServiceChannel' && obj.swou_username !== null && obj.swou_username !== ''),
            wwStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'WW'),
            twStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'TW'),
            fmpStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'FMPilot'),
            aspireStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'Aspire'),
            fmStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'FMFLEET'),
            accusaltStatus: results[0] == null ? false : results.some(obj => obj.sfapi_provider === 'ACCUSALT'),
            corrigoproStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'Corrigopro' && obj.swou_production === 1),
            command7Status: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'Command7'),
            caseFMSStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'CaseFMS'),
            scPinStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'ServiceChannel' && obj.swou_sc_pin_users === 1),
            emcorStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'EMCOR'),
            wizardStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'WIZARD'),
            smsOneStatus: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'SMSOne'),
            api: workorders[0] == null ? false : workorders.some(obj => obj.swos_name === 'API'),
        }
        res.json(data);
    } catch (ex) {
        next(ex)
    }
}

const getTrades = async(req, res,next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const query = /*SQL*/`SELECT TRIM(TRAILING ')' FROM SUBSTRING(COLUMN_TYPE,6))  as enumVal  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'maptile_building' AND COLUMN_NAME = 'mb_contract_type'`
        let enumVal = (await awaitSafeQuery(query))[0]['enumVal'];
        let result = enumVal.replace(/\'/g, '')
        //sort alphabettically
        result.split(',').sort();
        res.json(result.split(',').sort());

    } catch (ex) {
       next(ex)
    }

}

const weatherWorksRequestCertifiedHistoricalData = async(req, res, next) => {
  try {
    let formData = new FormData();
    let data = req.body;
      formData.append('referrer', 'snowbidder');
      formData.append('name', data.name);
      formData.append('company', data.company);
      formData.append('email', data.email);
      formData.append('phone', data.phonenumber);
      formData.append('locations', data.codes);
      if (data.radioButtons.five){
          formData.append('timescale-5yr', 'on')
      }
      if (data.radioButtons.five){
          formData.append('timescale-10yr', 'on');
      }
      if (data.radioButtons.five){
          formData.append('timescale-15yr', 'on');
      }
      if (data.radioButtons.five){
          formData.append('timescale-custom', 'on');
      }
      // formData.append('locations', data.codes);
    let weatherWorksFormSubmission = await fetch('https://weatherworksinc.com/data-request', {
      method: 'POST',
      body: formData
    });
      //We are getting response in html when it seems to work and in json when error
    // let result = await weatherWorksFormSubmission.text();
    res.json({
      ww: '1'
    });
  } catch (ex) {
    next(ex)
  }

}

const sendProposal = async(req, res) => {
  try {
    // const { vendorId, internalUid } = await login(req.cookies.JWT);
    const vendorId = req.query.vid;
    const submittedFormId = req.query.cid; //cid means, child of parent form

    const query = /*SQL*/`select
                       sfs_building_id as buildingid,
                       sfs_uploader_email as uploaderemail,
                       sfs_form_data_full as fullformdata,
                       mb_id as buildingid,
                       mb_nickname as sitename,
                       mb_address1 as location,
                       mb_vendor_internal_id as storenumber,
                       mb_manager as managerfrombuildingstable,
                       mb_contact_name2 as storemanager,
                       mb_email_address2 as email
                   from sitefotos_forms_submitted
                            inner join maptile_building on sitefotos_forms_submitted.sfs_building_id = maptile_building.mb_id
                   where sfs_id = ? and sfs_vendor_id =?`;
    let queryResult = (await awaitQuery(query,[submittedFormId,vendorId]))[0];

    //Now lets get form data and find which services were completed.
    let formdata = queryResult.fullformdata;
    let data = JSON.parse(formdata).pages[0].elements; //We are assuming this form will have single page.
    // let dataServices = data.elements.filter(a => a.type === "service")
    let storemanager = queryResult.storemanager === null ? "Not found":queryResult.storemanager;
    let placeHolderServices = [
      {
        "api_key": "location",
        "value": queryResult.location
      },
      {
        "api_key": "storenumber",
        "value": queryResult.sitename === null ? "Not found":queryResult.sitename
      },
      {
        "api_key": "storemanager",
        "value": storemanager
      }
    ];
    let textIndex = 0;
    let photoIndex = 0;
    for (const item of data) {
      if (item.type === "service" && item.Completed){
        textIndex += 1;
        let obj = {
          "api_key": `text${textIndex}`,
          "value": `--${item.title}`
        }
        placeHolderServices.push(obj)
      }
      if (item.type === "file" && item.value.length > 0) {
        photoIndex += 1;
        let urlOfImage = item.value[0].lrImageURL;
        let imageResponse = await fetch(urlOfImage);
        let blob = await imageResponse.arrayBuffer();
        let buffer = Buffer.from(blob).toString("base64");
        let obj = {
          "api_key": `Photo${photoIndex}`,
          "document_elements": [
            {
              "type":  "image", "image_base64": buffer, "image_height_rem": 30
            }
          ]
        }
        placeHolderServices.push(obj)
      }
    }
    let esignatureObject = {
      "template_id": "de89c844-33e3-4866-90d2-8505cc5c4b65",
      "metadata": "ID0001",
      "locale": "en",
      "test": "no",
      "signers": [
        {
          "name": storemanager,
          "email": queryResult.email,
          "signing_order": "1",
          "auto_sign": "no",
          "required_identification_methods": [],
          "signature_request_delivery_method": "email",
          "signed_document_delivery_method": "email",
          "embedded_redirect_iframe_only": "no"
        }
      ],
      "placeholder_fields": placeHolderServices,
      "emails": {
        "signature_request_subject": "Please approve services from Hatch Asphalt.",
        "signature_request_text": "Hi __FULL_NAME__, \n\n To review and approve the services please press the button below \n\n Kind Regards",
        "final_contract_subject": "Your document is signed",
        "final_contract_text": "Hi __FULL_NAME__, \n\n Your document is signed.\n\nKind Regards",
        "cc_email_addresses": ["<EMAIL>", "<EMAIL>"],
        "reply_to": "<EMAIL>",
      },
      "custom_branding": {
        "company_name": "Hatch Asphalt",
        "logo_url": "https://img1.wsimg.com/isteam/ip/63b8cabf-e884-4de2-a79a-e23276bb6385/Hatch%20Logo%20Transparent%20Background.png"
      }
    }
    const response = await fetch(`https://esignatures.io/api/contracts?token=4bedbb28-1258-4a6f-a868-b300873e9af5`,{
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(esignatureObject),
    });
    const finalResponse = await response.json();
    res.json({ "resp" : finalResponse, "data": placeHolderServices })
  }
  catch (ex){
    console.error(ex)
    res.json({ error: JSON.stringify(ex) })
  }
}

const mcNeilReport = async(req,res) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        let start = req.body.start;
        let end = req.body.end;
        let data = await awaitQuery(`select 
            sfs.sfs_checkin                      as StartTime,
            sfs.sfs_checkout                     as StopTime,
            (sfs.sfs_checkout - sfs.sfs_checkin) As TimeSpent,
            mb_id                                As SiteID,
            mb_nickname                          As SiteName,
            sfs.sfs_uploader_email               as WorkerEmail,
            sfs.sfs_form_id                      as FormId,
            swsd.swsd_service_name               as ServiceName
        from sitefotos_wp_services_data swsd
              inner join sitefotos_forms_submitted sfs
                         on swsd.swsd_form_submission_reference = sfs.sfs_id
              inner join maptile_building mb on swsd.swsd_site_id = mb.mb_id
        where swsd.swsd_vendor_id =? and swsd.swsd_active = 1 and swsd.swsd_service_status != 'TEMPLOG' and sfs.sfs_checkin >=? and sfs.sfs_checkout <=?
        GROUP by swsd.swsd_id;`, [vendorId,start,end]);
        res.json(data);
    } catch (ex) {
        next(ex)
    }
}

const reportBrokenCameraToGoogleSheet = async (req,res,next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    const messageByUser = req.body.message;
    const cameraId = req.body.camera_id;
    sendToGoogleChat(
      "Broken Camera Reported",
      `Reported by vendor id: ${vendorId} for camera id: ${cameraId}`,
      messageByUser,
      "https://chat.googleapis.com/v1/spaces/AAAAz7F2oUk/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=kRJFwTtDgz3eZh1KgFlY0bTc7Mq13bxUkPaM5RvkFHk%3D"
    );
    res.json("1")
  } catch (ex) {
    next(ex)
  }
}

const getVendorServices = async (req,res,next) => {
//
  try {
    const {vendorId} = await login(req.cookies.JWT);
    const servicesPromise = await awaitSafeQuery(/*SQL*/`select vs_service_id, vs_service_name, vs_service_status, vs_service_description, vs_service_category, vs_provider, vs_service_options, IF(vs_vendor_id=?, '1', '0') as service_own,vs_service_quickbook_id, vs_service_type, vs_equipment_id, vs_provider_id from vendor_services where vs_vendor_id =? AND (NOT (vs_provider <=> 'BOSSLMTM') OR vs_service_status ='1')`, [vendorId, vendorId])
    res.json(servicesPromise);
  } catch (ex) {
    next(ex)
  }
}

const getInactiveMaterials = async (req,res,next) => {
  try {
    const {vendorId} = await login(req.cookies.JWT);
    const inActiveMaterials =
      await awaitSafeQuery(`select mat_id, mat_material_name, mat_material_unit, mat_material_status, mat_provider, mat_provider_id, IF(mat_vendor_id='$vendorid', '1', '0') as material_own from maptile_materials mm where mm.mat_vendor_id = ? and mm.mat_material_status = '0';`,
        [vendorId]);
    res.json(inActiveMaterials);
  } catch (ex) {
    next(ex)
  }
}

const getInactiveServices = async (req,res,next) => {
  try {
    const {vendorId} = await login(req.cookies.JWT);
    const inActiveServices =
      await awaitSafeQuery(`select vs_service_id, vs_service_name, vs_service_status, vs_service_description, vs_service_category, vs_provider, vs_service_options,vs_service_quickbook_id, vs_service_type, vs_service_type_id, vs_trade_id, vs_equipment_id, vs_provider_id, IF(vs_vendor_id=?, '1', '0') as service_own from vendor_services where vs_vendor_id=? and vs_service_status ='0' ;`,
        [vendorId,vendorId]);
    res.json(inActiveServices);
  } catch (ex) {
    next(ex)
  }
}

const getServiceTypes = async (req,res,next) => {
  try {
    const {vendorId} = await login(req.cookies.JWT);
    const serviceTypes =
      await awaitSafeQuery(`select * from sitefotos_service_types sst`,
        [vendorId,vendorId]);
    res.json(serviceTypes);
  } catch (ex) {
    next(ex)
  }
}

const getLiveMapPresets = async (req, res, next) => {
  try {
    const {vendorId, internalUid} = await login(req.cookies.JWT);


    const sql = "SELECT * from sitefotos_livemap_presets where slp_vendor_id=? and slp_user_id=?";
    const data = await awaitSafeQuery(sql, [vendorId, internalUid]);

    res.json(data);
  } catch (error) {
    next(error);
  }
};

//Test method only
const updateBuildingTimezones = async (req, res, next) => {
  try {
    res.json("Background...");
    const buildings = await awaitSafeQuery(`SELECT mb_id, mb_lat, mb_long FROM maptile_building where mb_timezone like 'UTC'`);
    let updatedCount = 0;

    for (let i = 0; i < buildings.length; i++) {
      const b = buildings[i];

      const lat = parseFloat(b.mb_lat);
      const lng = parseFloat(b.mb_long);

      if (!isFinite(lat) || !isFinite(lng)) continue;

      try {
        const tzRaw = find(lat, lng);
        const tz = Array.isArray(tzRaw) ? tzRaw[0] : tzRaw || 'UTC';
        await updateObj('maptile_building', { mb_timezone: tz }, ['mb_id'], [b.mb_id]);
        updatedCount++;

        if (updatedCount % 500 === 0) {
          console.log(`Updated ${updatedCount} of ${buildings.length}`);
        }
      } catch (err) {
        console.warn(`Failed to update mb_id ${b.mb_id}:`, err.message);
      }
    }
  } catch (error) {
    next(error);
  }
};

module.exports = {
  backOfficeData,
  getTrades,
  getVendorAppSettings,
  setVendorAppSettings,
  sendProposal,
  getUserIntegrations,
  weatherWorksRequestCertifiedHistoricalData,
  mcNeilReport,
  getTimeZoneInfo,
  reportBrokenCameraToGoogleSheet,
  getVendorServices,
  timeSyncDelay,
  wellsFargoMaps,
  wellsFargoLandscapeMaps,
  setVendorSettings,
  getInactiveMaterials,
  getInactiveServices,
  getServiceTypes,
  getLiveMapPresets,
  updateBuildingTimezones
}
