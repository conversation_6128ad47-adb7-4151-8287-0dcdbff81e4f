const mysql = require('../utils/db')
const admin = require('../services/admin.services');
const { v4: uuidv4 } = require('uuid');
const {awaitSafeQuery, awaitQuery} = require('../utils/db')
const { adminLogin } = require("../utils/vendor")
const crypto = require('crypto');
const formsService = require('../services/form.services');
const mapService = require('../services/map.services');
const twServices = require('../services/external/truweather');
const adminServices = require('../services/admin.services');
const sitesServices = require('../services/sites.services');
const wellsfargoServices = require('../services/wellsfargo.services');
const cvsServices = require('../services/cvs-services');


const cvsReindex = async (req, res, next) => {
    try {
        cvsServices.populateInitialCache();
        res.status(200).send('Index updated successfully');
    } catch (ex) {
        res.status(500).send('Failed to update index');
    }
}
const getPages = async (req, res, next) => {
    try {
        const pages = await awaitSafeQuery('SELECT supp_id, supp_name FROM sitefotos_user_permissions_pages');
        res.status(200).json(pages);
    } catch (ex) {
        next(ex);
    }
};
const addNewPage = async (req, res, next) => {
    try {
        const { pageName, vendorIds, defaultVisible } = req.body;


        // Create the initial permission object
        const initialPermission = JSON.stringify([{
            Permission: "Visible",
            Description: "Visible",
            Value: defaultVisible
        }]);

        // Handle the vendorIds
        const vendorIdsJSON = vendorIds ? JSON.stringify(vendorIds) : null;

        // Insert the new page into the database
        await awaitSafeQuery(
            'INSERT INTO sitefotos_user_permissions_pages (supp_name, supp_permission_extended, supp_internal_page_vid) VALUES (?, ?, ?)',
            [pageName, initialPermission, vendorIdsJSON]
        );

        res.status(200).send('Page added successfully');
    } catch (ex) {
        next(ex);
    }
};
const getRestrictedPages = async (req, res, next) => {
    try {
        const pages = await awaitSafeQuery('SELECT supp_id, supp_name FROM sitefotos_user_permissions_pages WHERE supp_internal_page_vid IS NOT NULL');
        res.status(200).json(pages);
    } catch (ex) {
        next(ex);
    }
};
const getPagePermissions = async (req, res, next) => {
    try {
        const pageId = req.params.pageId;
        const page = await awaitSafeQuery('SELECT supp_permission_extended FROM sitefotos_user_permissions_pages WHERE supp_id = ?', [pageId]);
        
        if (page.length === 0) {
            res.status(404).send('Page not found');
            return;
        }

        const permissions = JSON.parse(page[0].supp_permission_extended || '[]');
        res.status(200).json(permissions);
    } catch (ex) {
        next(ex);
    }
};

const addUpdatePagePermission = async (req, res, next) => {
    try {
        const { pageId, permissionName, permissionDescription, defaultValue, isNewPermission } = req.body;

        // Fetch existing permissions
        const page = await awaitSafeQuery('SELECT supp_permission_extended FROM sitefotos_user_permissions_pages WHERE supp_id = ?', [pageId]);
        
        if (page.length === 0) {
            res.status(404).send('Page not found');
            return;
        }

        let permissions = JSON.parse(page[0].supp_permission_extended || '[]');

        if (isNewPermission) {
            // Add new permission
            permissions.push({
                Permission: permissionName,
                Description: permissionDescription,
                Value: defaultValue
            });
        } else {
            // Update existing permission
            const permissionIndex = permissions.findIndex(p => p.Permission === permissionName);
            if (permissionIndex !== -1) {
                permissions[permissionIndex].Description = permissionDescription;
                permissions[permissionIndex].Value = defaultValue;
            } else {
                res.status(404).send('Permission not found');
                return;
            }
        }

        // Update the page with new or updated permissions
        await awaitSafeQuery(
            'UPDATE sitefotos_user_permissions_pages SET supp_permission_extended = ?, supp_modified_at = NOW() WHERE supp_id = ?',
            [JSON.stringify(permissions), pageId]
        );

        res.status(200).send(`Permission ${isNewPermission ? 'added' : 'updated'} successfully`);
    } catch (ex) {
        next(ex);
    }
};
const deletePagePermission = async (req, res, next) => {
    try {
        const { pageId, permissionName } = req.body;

        // Fetch existing permissions
        const page = await awaitSafeQuery('SELECT supp_permission_extended FROM sitefotos_user_permissions_pages WHERE supp_id = ?', [pageId]);
        
        if (page.length === 0) {
            res.status(404).send('Page not found');
            return;
        }

        let permissions = JSON.parse(page[0].supp_permission_extended || '[]');

        // Remove the specified permission
        permissions = permissions.filter(p => p.Permission !== permissionName);

        // Update the page with the new permissions list
        await awaitSafeQuery(
            'UPDATE sitefotos_user_permissions_pages SET supp_permission_extended = ?, supp_modified_at = NOW() WHERE supp_id = ?',
            [JSON.stringify(permissions), pageId]
        );

        res.status(200).send('Permission deleted successfully');
    } catch (ex) {
        next(ex);
    }
};

const syncPagePermissions = async (req, res, next) => {
    try {
        const { pageId, overrideVisiblePermission, visiblePermissionValue } = req.body;

        // Fetch the page details
        const page = await awaitSafeQuery('SELECT supp_permission_extended, supp_internal_page_vid FROM sitefotos_user_permissions_pages WHERE supp_id = ?', [pageId]);

        if (page.length === 0) {
            res.status(404).send('Page not found');
            return;
        }

        const pagePermissions = JSON.parse(page[0].supp_permission_extended || '[]');
        const vendorSpecificIds = JSON.parse(page[0].supp_internal_page_vid || '[]');

        // Fetch all roles
        let roles;
        if (vendorSpecificIds.length > 0) {
            roles = await awaitQuery('SELECT supr_id, supr_name, supr_permission_details, supr_vendor_id FROM sitefotos_user_permissions_roles WHERE supr_vendor_id IN (?)', [vendorSpecificIds]);
        } else {
            roles = await awaitSafeQuery('SELECT supr_id, supr_name, supr_permission_details, supr_vendor_id FROM sitefotos_user_permissions_roles');
        }

        // Send response immediately to the frontend
        res.status(200).send('Page permissions syncing in the background');

        // Perform the updates in the background
        setImmediate(async () => {
            try {
                // Prepare the updates
                const updates = roles.map(role => {
                    let rolePermissions = JSON.parse(role.supr_permission_details || '{}');

                    // Filter out permissions that are no longer present in pagePermissions
                    rolePermissions[pageId] = (rolePermissions[pageId] || []).filter(existingPermission =>
                        pagePermissions.some(pagePermission => pagePermission.Permission === existingPermission.Permission)
                    );

                    // Update or add permissions based on the pagePermissions
                    pagePermissions.forEach(permission => {
                        const existingPermission = rolePermissions[pageId]?.find(p => p.Permission === permission.Permission);

                        if (existingPermission) {
                            // Override "Visible" permission if specified, otherwise only update the description
                            if (permission.Permission === 'Visible' && overrideVisiblePermission) {
                                existingPermission.Description = permission.Description;
                                existingPermission.Value = visiblePermissionValue;
                            } else {
                                existingPermission.Description = permission.Description;
                            }
                        } else {
                            // Add new permission
                            rolePermissions[pageId].push(permission);
                        }
                    });

                    return {
                        supr_id: role.supr_id,
                        supr_permission_details: JSON.stringify(rolePermissions)
                    };
                });

               
                const batchSize = 1000;  
                for (let i = 0; i < updates.length; i += batchSize) {
                    const batch = updates.slice(i, i + batchSize);

                    const caseStatements = batch.map(update =>
                        `WHEN supr_id = ${update.supr_id} THEN '${update.supr_permission_details.replace(/'/g, "\\'")}'`
                    ).join(' ');

                    const ids = batch.map(update => update.supr_id).join(',');

                    const bulkUpdateQuery = `
                        UPDATE sitefotos_user_permissions_roles 
                        SET supr_permission_details = CASE ${caseStatements} END 
                        WHERE supr_id IN (${ids});
                    `;

                    await awaitSafeQuery(bulkUpdateQuery);
                }
            } catch (backgroundError) {
                console.error('Error syncing page permissions in the background:', backgroundError);
            }
        });

    } catch (ex) {
        next(ex);
    }
};


const {updateIndexFromGoogleDrive} = require('../services/aichat-services');

const headers = async (req, res, next) => {
    try {
        res.status(200).send(req.headers)
    }
    catch (ex)
    {
        next(ex)
    }
}
const home = async (req, res, next) => {
    try {
        
        const data = {}
        res.render('admin/index.ejs', { data: data });
    } catch (ex) {
        next(ex)
    }

}

const updateIndex = async (req, res, next) => {
    try {
        try {
            await updateIndexFromGoogleDrive('1EtyaeYcoBBAyBTD_6sA-5oA2W3ZXxNwd');
            res.status(200).send('Index updated successfully');
        } catch (ex) {
            res.status(500).send('Failed to update index');
        }
    } catch (ex) {
        next(ex)
    }
}
const addHighresBalance = async (req, res, next) => {
    try {
        const vendorID = req.body.vendorId;
        const highresBalance = req.body.balance;
        try {
            const vendor = await awaitSafeQuery(`select vendor_access_code from maptile_vendors where vendor_id = ?`, [vendorID]);
            if (vendor.length === 0) {
                res.status(404).send('Vendor not found');
                return;
            }
            const accessCode = vendor[0].vendor_access_code;

            let req = await fetch(`https://tiles.sitefotos.com/api2/general/addquota?accesscode=${accessCode}&password=Muj56f44f9&amount=${highresBalance}`)
            let response = await req.json();
            if (response.error === true) {
                res.status(500).send('Failed to update highres balance');
                return;
            }

        } catch (ex) {
            res.status(500).send('Failed to update highres balance');
            return;
        }
        res.status(200).send('Highres balance updated successfully');
    } catch (ex) {
        next(ex)
    }

}

const forceClockOut = async (req, res, next) => {
    try {
        const workerEmail = req.body.workerEmail;
        const vendorID = req.body.vendorID;
        const vendor = await awaitSafeQuery(`SELECT vendor_access_code FROM maptile_vendors WHERE vendor_id=?`, [vendorID]);
        if (vendor.length === 0) {
            res.status(404).send('Vendor not found');
            return;
        }
        const timeLogEntries = await awaitSafeQuery(`select  * from  sitefotos_time_log   WHERE stl_vid = ? AND stl_email = ? AND stl_status='In_Progress' AND stl_bad_data = 'False'
        `, [vendorID, workerEmail]);
        if (timeLogEntries.length === 0) {
            res.status(404).send('Worker not clocked in');
            return;
        }
        for (let timeLogEntry of timeLogEntries) {
            const crew = timeLogEntry.stl_contact_id;
            const lastbReadcrumb = await awaitSafeQuery(`SELECT * FROM sitefotos_breadcrumb_data WHERE sbd_crew=? and sbd_type = 4 and sbd_vendor_id=? ORDER BY sbd_id DESC LIMIT 1`, [crew, vendorID]);
            const lat = lastbReadcrumb[0].sbd_lat;
            const lon = lastbReadcrumb[0].sbd_lng;
           
            const accessCode = vendor[0].vendor_access_code;
            //build app data and send it to php
            const data = {
                accessCode: accessCode,
                email: workerEmail,
                type: '5',
                lat: lat,
                lon: lon,
                'crew[]': crew,
                profileid: '-1',
                siteid: '-1',
                deviceType: 'admin',
                deviceModel: 'admin',
                appVersion: 'admin-backend',
                uuid: uuidv4(),
                dt: Math.floor(new Date().getTime() / 1000),
            }
            const response = await fetch('http://http/vpics/uploadbreadcrumb', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
                body: new URLSearchParams(data),
            });
            const responseText = await response.text();
            console.log(responseText);
        }
        res.status(200).send('Successfully clocked out worker');
    } catch (ex) {
        next(ex)
    }
}
const syncAllStorms = async (req, res, next) => {
    try {
        const vendorID = req.body.vendorId;
        
        try {
            twServices.syncStormsAll(vendorID);
            res.status(200).send('Successfully started sync');
        } catch (ex) {
            res.status(500).send('Failed to sync vendor');
            return;
        }
    } catch (ex) {
        next(ex)
    }
       



}
const lockUnclockVendor = async (req, res, next) => {
    try {
        const vendorID = req.body.vendorId;
        const lockState = req.body.lock == true ? '1' : '0';
        try {
            await awaitSafeQuery(`UPDATE maptile_vendors SET vendor_payment_lock=? WHERE vendor_id=?`, [lockState, vendorID]);
        } catch (ex) {
            res.status(500).send('Failed to lock/unlock vendor');
            return;
        }
        res.status(200).send('Vendor locked/unlocked successfully');
    } catch (ex) {
        next(ex)
    }
}
const getVendorDetails = async (req, res, next) => {
    try {
        
        let vendorEmail = req.body.vendorEmail;
       
        vendorEmail = vendorEmail.toLowerCase();

        const vendorDetails = await awaitSafeQuery(`SELECT vendor_id, vendor_company_name, vendor_access_code_custom_restricted, vendor_access_code FROM maptile_vendors WHERE LOWER(vendor_email) = ?`, [vendorEmail]);
        if(vendorDetails.length === 0) {
            res.status(404).send('Vendor not found');
            return;
        }
        res.status(200).send(vendorDetails[0]);
    }
    catch (ex) {
        next(ex)
    }
}

const getUserDetails = async (req, res, next) => {
    try {
        
        let userEmail = req.body.userEmail;
       
        userEmail = userEmail.toLowerCase();

        const vendorDetails = await awaitSafeQuery(`SELECT vendor_id, vendor_company_name FROM sitefotos_user_permissions_users left join maptile_vendors on supu_vendor_id=vendor_id WHERE LOWER(supu_email) = ?`, [userEmail]);
        if(vendorDetails.length === 0) {
            res.status(404).send('user not found');
            return;
        }
        res.status(200).send(vendorDetails[0]);
    }
    catch (ex) {
        next(ex)
    }
}
const moveSite = async (req, res, next) => {
    try {
        const sourceSiteID = req.body.sourceSiteID;
        const destinationVendorID = req.body.destinationVendorID;
        const mapExistingServicesMaterials = req.body.mapExistingServicesMaterials;
        const copyAttributes = req.body.copyAttributes;
        const archiveSource = req.body.archiveSource;

        const sourceSiteFormIDs = req.body.sourceSiteFormIDs || [];
        const sourceSite = await awaitSafeQuery(`SELECT * FROM maptile_building WHERE mb_id=?`, [sourceSiteID]);
        if(sourceSite.length === 0) {
            res.status(404).send('Source site not found');
            return;
        }
        const destinationVendor = await awaitSafeQuery(`SELECT * FROM maptile_vendors WHERE vendor_id=?`, [destinationVendorID]);
        if(destinationVendor.length === 0) {
            res.status(404).send('Destination vendor not found');
            return;
        }
        if (sourceSite[0].mb_user_id == destinationVendorID) {
            res.status(200).send('Source and Destination site belong to the same vendor');
            return;
        }
        try {
            await sitesServices.moveSite({sourceSiteID: sourceSiteID, destinationVendorID: destinationVendorID, sourceSiteFormIDs, mapExistingServicesMaterials: mapExistingServicesMaterials, deactivateSource: archiveSource, copyExternalID: copyAttributes});
        } catch (ex) {
            console.log(ex)
            res.status(500).send('Failed to copy site');
            return;
        }
        res.status(200).send('Site copied successfully');
    } catch (ex) {
        next(ex)
    }
}
const getSiteForms = async (req, res, next) => {
    try {
        const siteId = req.body.siteId;
        if (!siteId) {
            res.status(400).send('siteId is required');
            return;
        }
        const forms = await awaitSafeQuery(
            `SELECT * FROM sitefotos_forms WHERE JSON_CONTAINS(sf_form_site, ?, '$') and sf_active = '1'`,
            [JSON.stringify(siteId)]
        );
        res.status(200).json(forms);
    } catch (ex) {
        next(ex)
    }
}

const copyForm = async (req, res, next) => {
    try {
        const formID = req.body.formID;
        const vendorID = req.body.destinationVendor;
        const mapExistingServicesMaterials = req.body.mapExistingServicesMaterials;

        const form = await awaitSafeQuery(`SELECT * FROM sitefotos_forms WHERE sf_id=?`, [formID]);
        if(form.length === 0) {
            res.status(404).send('Form not found');
            return;
        }
        const vendor = await awaitSafeQuery(`SELECT * FROM maptile_vendors WHERE vendor_id=?`, [vendorID]);
        if(vendor.length === 0) {
            res.status(404).send('Vendor not found');
            return;
        }
        try {
            await formsService.copyForms({formIDs: [formID], vendorID: vendorID, mapExistingServicesMaterials: mapExistingServicesMaterials});
        } catch (ex) {
            console.log(ex)
            res.status(500).send('Failed to copy form');
            return;
        }
        res.status(200).send('Form copied successfully');

    } catch (ex) {
        next(ex)
    }


}
const getClientViewVendors = async (req, res, next) => {
    try {
        const vendors = await awaitSafeQuery(`SELECT vendor_id, vendor_company_name FROM maptile_vendors WHERE vendor_client_view_enabled=?`, ['Y']);
        res.status(200).send(vendors);
    } catch (ex) {
        next(ex)
    }
}

const getHybridVendors = async (req, res, next) => {
    try {
        const vendors = await awaitSafeQuery(`SELECT vendor_id, vendor_company_name FROM maptile_vendors WHERE vendor_hybrid=?`, ['Y']);
        res.status(200).send(vendors);
    } catch (ex) {
        next(ex)
    }
}

const getClientViewSites = async (req, res, next) => {
    try {
        const vendorID = req.params.vendorId;
        const sites = await mysql.awaitQuery(`select scs_client_internal_id from sitefotos_site_client_mapping_extended_grouped where scs_client_vendor_id=?`,[vendorID]);
        //send it back as simple array of values
        res.status(200).send(sites.map(x => x.scs_client_internal_id));
        
    } catch (ex) {
        next(ex)
    }
}

const getHybridSites = async (req, res, next) => {
    try {
        const vendorID = req.params.vendorId;
        const sites = await mysql.awaitQuery(`select mb_vendor_internal_id from maptile_building where mb_hybrid_vendor_id=? group by mb_vendor_internal_id`,[vendorID]);
        //send it back as simple array of values
        res.status(200).send(sites.map(x => x.mb_vendor_internal_id));
        
    } catch (ex) {
        next(ex)
    }
}
const addHybridSite = async (req, res, next) => {
    try {
        const vendorID = req.body.vendorID;
        const siteID = req.body.siteID;
        let hybridSite = req.body.hybridSite.trim();

        const siteData = await mysql.awaitQuery('SELECT * from maptile_building where mb_id=?', [siteID]);
        if(siteData.length === 0) {
            res.status(404).send('Site not found');
            return;
        }
        try {
            await mysql.awaitQuery(`update maptile_building set mb_min_zoom='1', mb_hybrid='Y', mb_hybrid_vendor_id=?, mb_vendor_internal_id=? where mb_id=?`, [vendorID, hybridSite, siteID], {useMainPool: true});
            await mysql.awaitQuery(`INSERT INTO maptile_building_share (mbs_user,mbs_owner,mbs_building) VALUES(?,?,?)`, [vendorID, siteData[0].mb_user_id, siteID], {useMainPool: true});
        } catch (ex) {
            console.log(ex)
            res.status(500).send('Failed to add Hybrid site');
            return;
        }
        res.status(200).send('Hybrid site added successfully');


    } catch (ex) {
        next(ex)
    }
}
const addClientViewSite = async (req, res, next) => {
    try {
        const vendorID = req.body.vendorID;
        const siteID = req.body.siteID;
        let clientViewSite = req.body.clientViewSite.trim();

        const siteData = await mysql.awaitQuery('SELECT * from maptile_building where mb_id=?', [siteID]);
        if(siteData.length === 0) {
            res.status(404).send('Site not found');
            return;
        }
        try {
            //await mysql.awaitQuery(`call InsertOrUpdateClientSiteData(?,?,?,null)`, [siteID, vendorID, clientViewSite], {useMainPool: true});
            await adminServices.rollupClientSite(siteID, vendorID, clientViewSite, null);
        } catch (ex) {
            console.log(ex)
            res.status(500).send('Failed to add client view site');
            return;
        }
        res.status(200).send('Client view site added successfully');


    } catch (ex) {
        next(ex)
    }
}
const fetchClientViewSiteMetadata = async (req, res, next) => {
    try {
        const siteID = req.params.internal_id;
        const vendorID = req.params.client_vendor_id;
        const siteData = await mysql.awaitQuery(`SELECT scs_id  
        FROM sitefotos_client_sites 
        WHERE scs_client_internal_id = ? AND scs_client_vendor_id = ?`, [siteID, vendorID]);
        if(siteData.length === 0) {
            res.status(404).send('Site not found');
            return;
        }
        const metadata = await mysql.awaitQuery(`SELECT *FROM sitefotos_client_sites_data WHERE scsd_client_site_id = ?`, [siteData[0].scs_id]);
        if(metadata.length === 0) {
            res.status(404).send('Site metadata not found');
            return;
        }
        res.status(200).send(metadata[0]);
    } catch (ex) {
        next(ex)
    }
}
const updateClientViewSiteMetadata = async (req, res, next) => {
    try {
        const { internalID, clientVendorID, region, propertyManager, seniorPropertyManager, regionalManager, locationType, leasedOwned, contractType, landscapeSeason } = req.body;

       
        const parameters = [
            region || null,
            propertyManager || null,
            seniorPropertyManager || null,
            regionalManager || null,
            locationType || null,
            leasedOwned !== undefined ? leasedOwned : null, 
            contractType || null,
            landscapeSeason || null
        ];

        try {
            await adminServices.addClientSiteMetaData(internalID, clientVendorID, parameters);
            res.status(200).send('Site metadata updated successfully');
        } catch (ex) {
            console.error('Error updating site metadata:', ex);
            res.status(500).send('Failed to update site metadata');
        }
    } catch (ex) {
        console.error('Server error:', ex);
        next(ex); 
    }
};

const lockForm = async (req, res, next) => {
    try {
        const formID = req.body.formID;
        const lockState = req.body.lockState == true ? '1' : '0';
        try {
            await updateObj('sitefotos_forms', {sf_form_locked: lockState}, ['sf_id'], [formID]);
        } catch (ex) {
            res.status(500).send('Failed to lock/unlock form');
            return;
        }
        res.status(200).send('Form locked/unlocked successfully');
    } catch (ex) {
        next(ex)
    }
}

const copySiteResources = async (req, res, next) => {
    try {
        
        const source = req.body.sourceSite;
        const destination = req.body.destinationSite;
        const copyAttributes = req.body.copyAttributes;
        const archiveSource = req.body.archiveSource;
        const sourceSite = await awaitSafeQuery(`SELECT * FROM maptile_building WHERE mb_id=?`, [source]);
        const destinationSite = await awaitSafeQuery(`SELECT * FROM maptile_building WHERE mb_id=?`, [destination]);
        if(sourceSite.length === 0 || destinationSite.length === 0) {
            res.status(404).send('Source or Destination site not found');
            return;
        }
        if(sourceSite[0].mb_user_id != destinationSite[0].mb_user_id) {
            res.status(403).send('Source and Destination site do not belong to the same user');
            return;
        }
       
        try {
            await mysql.awaitQuery(`call copy_site_resources(?,?,?)`, [source, destination, copyAttributes], {useMainPool: true});
        } catch (ex) {
            console.log(ex)
            res.status(500).send('Failed to copy site resources');
            return;
        }
        
        if(archiveSource) {
            try {
                await awaitSafeQuery(`update maptile_building set mb_status=? where mb_id=?`, ['0', source]);
            } catch (ex) {
                res.status(500).send('Resources Copied Successfully, Failed to archive source site');
                return;
            }
        }
        res.status(200).send('Site resources copied successfully');
    }
    catch (ex) {
        next(ex)
    }
}
const changeVendorEmail = async (req, res, next) => {
    try {
        const { currentEmail, newEmail } = req.body;
        
        if (!currentEmail || !newEmail) {
            return res.status(400).json({ message: 'Both emails are required' });
        }
        
        // Check if new email exists
        const existingVendor = await mysql.awaitSafeQuery(`SELECT * FROM maptile_vendors WHERE LOWER(vendor_email) = ?`, [newEmail.toLowerCase()]);
        if (existingVendor && existingVendor.length > 0) {
            return res.status(400).json({ message: 'New email already in use' });
        }

        // Update vendor email
        try {
            await mysql.updateObj('maptile_vendors', { vendor_email: newEmail }, ['vendor_email'], [currentEmail]);
        } catch (error) {
            console.log(error)
            return res.status(500).json({ message: 'Failed to change email, check if there is a subuser with this email' });
        }
        res.status(200).json({ message: 'Email changed successfully' });
    } catch (error) {
        console.log(error)
       next(error)
    }
};

const changeVendorPassword = async (req, res, next) => {
    try {
        
        let vendorEmail = req.body.vendorEmail;
        let newPassword = req.body.newPassword;

        
        vendorEmail = vendorEmail.toLowerCase();
        
        const hashedPassword = await crypto.createHash('md5').update(newPassword).digest('hex')
        try {
        await awaitSafeQuery(`UPDATE maptile_vendors SET vendor_password = ? WHERE LOWER(vendor_email) = ?`, [hashedPassword, vendorEmail]);
        } catch (ex) {
            res.status(500).send('Failed to update password');
            return;
        }
        res.status(200).send('Password changed successfully');
    }
    catch (ex) {
        next(ex)
    }
}
const changeUserPassword = async (req, res, next) => {
    try {
        
        let userEmail = req.body.userEmail;
        let newPassword = req.body.newPassword;

        
        userEmail = userEmail.toLowerCase();
        
        const hashedPassword = await crypto.createHash('sha1').update(newPassword).digest('hex')
        try {
        await awaitSafeQuery(`UPDATE sitefotos_user_permissions_users SET supu_password = ? WHERE LOWER(supu_email) = ?`, [hashedPassword, userEmail]);
        } catch (ex) {
            res.status(500).send('Failed to update password');
            return;
        }
        res.status(200).send('Password changed successfully');
    }
    catch (ex) {
        next(ex)
    }
}
const deactivateVendor = async (req, res, next) => {
    try {
        ;
        let vendorEmail = req.body.vendorEmail;
        
        
        vendorEmail = vendorEmail.toLowerCase();
        let newEmail = vendorEmail;
        if (newEmail.includes('@')) {
            newEmail = vendorEmail.replace('@', '__@');
        }
       
        try {
        
        await awaitSafeQuery(`UPDATE maptile_vendors SET vendor_email = ? WHERE LOWER(vendor_email) = ?`, [newEmail, vendorEmail]);
        } catch (ex) {
            res.status(500).send('Failed to deactivate vendor');
            return;
        }

        res.status(200).send({newEmail});
    } catch (ex) {
        next(ex);
    }
}

const deactivateUser = async (req, res, next) => {
    try {
        ;
        let userEmail = req.body.userEmail;
        
        
        userEmail = userEmail.toLowerCase();
        let newEmail = userEmail;
        if (newEmail.includes('@')) {
            newEmail = userEmail.replace('@', '__@');
        }
       
        try {
        
        await awaitSafeQuery(`UPDATE sitefotos_user_permissions_users SET supu_email = ? WHERE LOWER(supu_email) = ?`, [newEmail, userEmail]);
        } catch (ex) {
            res.status(500).send('Failed to deactivate user');
            return;
        }

        res.status(200).send({newEmail});
    } catch (ex) {
        next(ex);
    }
}
const mapMeasurements = async (req, res, next) => {
    try {
        const mapIds = req.body.mapIds;
        let mapLayers = await awaitSafeQuery(/*SQL*/`SELECT sml_id, sml_city, sml_state, sml_address1, sml_zip_code, sml_layer_name,sml_urlkey, sml_measurements FROM sitefotos_map_layers WHERE sml_id IN (${mapIds})`);
        res.setHeader('Content-Type', 'text/csv');
        let csvRows = await mapService.getCsvMapMeasurements(mapLayers);
        const csvString = csvRows.map(row => row.join(',')).join('\n');
        res.write(csvString);

        res.status(200).end();
    } catch (ex) {
        next(ex)
    }
}
const getVendorPassword = async (req, res, next) => {
    try {
        
        let vendorEmail = req.body.vendorEmail;
        vendorEmail = vendorEmail.toLowerCase();
        const vendorDetails = await awaitSafeQuery(`SELECT vendor_password FROM maptile_vendors WHERE LOWER(vendor_email) = ?`, [vendorEmail]);
        if(vendorDetails.length === 0) {
            res.status(404).send('Vendor not found');
            return;
        }
        res.status(200).send({vendor_password: vendorDetails[0].vendor_password});
    } catch(ex) {
        next(ex)   
    }

}
const getUserPassword = async (req, res, next) => {
    try {
        
        let userEmail = req.body.userEmail;
        userEmail = userEmail.toLowerCase();
        const vendorDetails = await awaitSafeQuery(`SELECT supu_password FROM sitefotos_user_permissions_users WHERE LOWER(supu_email) = ?`, [userEmail]);
        if(vendorDetails.length === 0) {
            res.status(404).send('User not found');
            return;
        }
        res.status(200).send({supu_password: vendorDetails[0].supu_password});
    } catch(ex) {
        next(ex)   
    }

}
const changeVendorShortCode = async (req, res, next) => {
    try {
        
        let vendorEmail = req.body.vendorEmail;
        let newShortCode = req.body.newShortCode;
        vendorEmail = vendorEmail.toLowerCase();
        try {
        await awaitSafeQuery(`UPDATE maptile_vendors SET vendor_access_code_custom_restricted = ? WHERE LOWER(vendor_email) = ?`, [newShortCode, vendorEmail]);
        } catch (ex) {
            res.status(500).send('Failed to update short code');
            return;
        }
        res.status(200).send('Short code changed successfully');
    } catch (ex) {
        next(ex)
    }
}

const toggleVendorAppRadius = async (req, res, next) => {
    try {
        
        let vendorEmail = req.body.vendorEmail;
        let radius = req.body.radiusVisibility == true ? 'Y' : 'N';
        
        vendorEmail = vendorEmail.toLowerCase();
        try {
        await awaitSafeQuery(`UPDATE maptile_vendors SET vendor_restrict_sites_radius = ? WHERE LOWER(vendor_email) = ?`, [radius, vendorEmail]);
        } catch (ex) {
            res.status(500).send('Failed to update app radius');
            return;
        }
        res.status(200).send('App radius changed successfully');
    } catch (ex) {
        next(ex)
    }
}
//test function ignore
const clockOut = async (req, res, next) => {
    try {
        let data = {
            accessCode: '4110a1994471c595f7583ef1b74ba4cb',
            email: '<EMAIL>',
            type: '5',
            lat: '40.154228',
            lon: '-75.3707198',
            crew: [30983,30953,30746,30314,30636,30649,30282,30284,30283,30639,30635,30278,30281,30566,14195,27770,21829,14149,15849,27768,28639,27767,21814,15830,21815,14113,14735,21826,28668,21822,27766,27765,27764,8396,23164,8400,21850,13809,13367,8259,8258,23339,21837,8196,14847,13822,23165,27763,27761,15884,14798,14733,27759,13403,14237,8195,21854,13334,25729,29365,13824,28560,13333,14853,14226,14175,13955,8391,21798,14854,14305,21830,14189,8316,14194,22473,14866,22422,14889,14223,27758,28702,28049,14754,30027,15841,15819,27757,21843,15829,14235,22428,15840,21805,30085,23025,14241,8402,22424,27756,22950,14833,15337,14757,22289,13816,8283,27755,14049,30023,14198,14197,14112,14716,8236,13366,30021,14867,14179,15825,14116,28170,14539,13829,20018,15694,8315,8403,27754,28080,28735,25582,8398,13954,14165,22941,25513,27753,8317,8308,22909,21847,14859,14850,14109,13375,14864,13337,22283,21855,14186,13952,13819,13374,13350,8399,8252,21795,14188,21794,28759,14177,13364,21799,15738,13817,22940,14225,15827,15826,21853,15342,27750,23169,21851,14540,13362,21842,14117,14766,14270,13340,13336,15700,21873,21838,13338,14221,30038,14862,14196,21825,15846,14856,14057,15696,14835,14125,21848,13918,14836,28054,27747,15334,14758,13830,13342,22356,15344,14219,14890,14805,13372,8756,15834,13373,13344,21841,493,14717,28709,30146,21834,13832,8320,21849,21806,15832,15341,14088,30026,14192,13343,21821,27746,21818,8289,28734,22938,14110,8307,8392,27779,27745,14858,13345,8769,8404,14755,21827,14860,14846,14169,14220,8288,23110,23180,8753,14183,14875,27744,21844,14705,15851,14806,14837,21817,14095,30025,15338,21846,14224,13369,28048,21802,21858,13828,15848,14788,13370,28169,8406,27743,8752,14865,14710,15828,15823,27742,21832,14178,14410,8282,14756,14180,21856,15687,14181,8197,14239,7942,8401,8287,15312,14636,15835,13353,8390,28168,28062,14753,14154,14888,21831,21852,14752,14887,14751,22908,14868,21824,14187,21813,21774,15313,14199,8318,27741,13368,14193,14190,21828,14861,14363,13332,8286,14191,30024,15824,21839,8319,14852,8256,8397,27739,27738,30155,13376,30022,8257,21836,13339,21777,14240,14236,13351,27780,14157,15340,14230,13983,14863,14746,24239,14052,14051,14050,23128,21835,14047,21845,13371,8255,13807,8405,14115,13812,8285,14155,27736,14803,14063,21797,30032,30203,28551,14130,13349,15346,14869,8314,27733,22911,22910,22474,21859,14851,14759,14704,27731,15333,8253,13341,15347,15345,13335,22421,21803,14064,4379,14174,21807,8254,14834,15339,27730,14719,13363,15822,14238,15695,27729,13833,14184,21823,27728,21809,14087,27726,21857,15343,23441,21833,13346,8382,22460,14142,21810,13365,22770,21816,15699,8284,15831,21819,14703,14048,14185,27723,14857,22282,14409,28641,23442,27771],
            profileid: '-1',
            deviceType: 'admin-backend',
            sbd_devicemodel: 'admin-backend',
            appVersion: 'admin-backend',
            uuid: uuidv4(),
            dt: Math.floor(new Date().getTime() / 1000),
        }
        const response = await fetch('http://http/vpics/uploadbreadcrumb', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded',},
            body: new URLSearchParams(data),
        });
        console.log(await response.text());
    } catch (ex) {
        next(ex)
    }
}


const addPage = async (req, res, next) => {
    if(!req.query.password || !req.query.name || !req.query.id || !req.query.json || !req.query.vid){
        res.status(500).send("required parameters missing");
        return;
    }
    if(req.query.password !== 'Fo4djvn234Sdv') {
        res.status(403).send("Authentication Failure")
        return;
    }
    try {
        await admin.addPage(req.query.id, req.query.name, req.query.json, req.query.vid)
        res.status(200).send("Successfuly added Page")
    }
    catch(ex)
    {
        next(ex)
    }
}
const getPageVendorIds = async (req, res, next) => {
    try {
        const pageId = req.params.pageId;
        const page = await awaitSafeQuery('SELECT supp_internal_page_vid FROM sitefotos_user_permissions_pages WHERE supp_id = ?', [pageId]);

        if (page.length === 0) {
            res.status(404).send('Page not found');
            return;
        }

        const vendorIds = JSON.parse(page[0].supp_internal_page_vid || '[]');
        res.status(200).json(vendorIds);
    } catch (ex) {
        next(ex);
    }
};
const updateVendorAccess = async (req, res, next) => {
    try {
        const { pageId, vendorId, action } = req.body;
        console.log(req.body);

        // Fetch current vendor IDs and page details
        const page = await awaitSafeQuery('SELECT supp_internal_page_vid, supp_permission_extended FROM sitefotos_user_permissions_pages WHERE supp_id = ?', [pageId]);
        if (page.length === 0) {
            res.status(404).send('Page not found');
            return;
        }

        let vendorIds = JSON.parse(page[0].supp_internal_page_vid || '[]');
        const pagePermissions = JSON.parse(page[0].supp_permission_extended || '[]');

        if (action === 'add') {
            if (!vendorIds.includes(vendorId)) {
                vendorIds.push(vendorId);
                // Add the page to all roles associated with this vendor
                await addPageToVendorRoles(pageId, vendorId, pagePermissions);
            }
        } else if (action === 'remove') {
            vendorIds = vendorIds.filter(id => id !== vendorId);
            
            // Remove the page from all roles associated with this vendor
            await removePageFromVendorRoles(pageId, vendorId);
        } else {
            res.status(400).send('Invalid action');
            return;
        }

        // Update the page with new vendor IDs
        await awaitSafeQuery(
            'UPDATE sitefotos_user_permissions_pages SET supp_internal_page_vid = ? WHERE supp_id = ?',
            [JSON.stringify(vendorIds), pageId]
        );

        res.status(200).send(`Vendor access ${action}ed successfully`);
    } catch (ex) {
        next(ex);
    }
};

const removePageFromVendorRoles = async (pageId, vendorId) => {
    try {
        // Fetch all roles for the vendor
        const roles = await awaitSafeQuery('SELECT supr_id, supr_permission_details FROM sitefotos_user_permissions_roles WHERE supr_vendor_id = ?', [vendorId]);

        for (const role of roles) {
            let permissionDetails = JSON.parse(role.supr_permission_details || '{}');

            // Remove the page from the role's permissions
            if (permissionDetails[pageId]) {
                delete permissionDetails[pageId];

                // Update the role with the new permissions
                await awaitSafeQuery(
                    'UPDATE sitefotos_user_permissions_roles SET supr_permission_details = ? WHERE supr_id = ?',
                    [JSON.stringify(permissionDetails), role.supr_id]
                );
            }
        }
    } catch (error) {
        console.error('Error removing page from vendor roles:', error);
        throw error;
    }
};
const addPageToVendorRoles = async (pageId, vendorId, pagePermissions) => {
    try {
        // Fetch all roles for the vendor
        const roles = await awaitSafeQuery('SELECT supr_id, supr_permission_details FROM sitefotos_user_permissions_roles WHERE supr_vendor_id = ?', [vendorId]);

        for (const role of roles) {
            let permissionDetails = JSON.parse(role.supr_permission_details || '{}');

            // Add the page to the role's permissions
            if (!permissionDetails[pageId]) {
                permissionDetails[pageId] = pagePermissions;

                // Update the role with the new permissions
                await awaitSafeQuery(
                    'UPDATE sitefotos_user_permissions_roles SET supr_permission_details = ? WHERE supr_id = ?',
                    [JSON.stringify(permissionDetails), role.supr_id]
                );
            }
        }
    } catch (error) {
        console.error('Error adding page to vendor roles:', error);
        throw error;
    }
};
const baseURL = async (req,res,next) => {
    try {
        const baseURL = process.env.BASE_URL || 'http://localhost'
        res.status(200).send(baseURL)
    }
    catch (ex)
    {
        next (ex)
    }
}
const addNewPagesToRoles = async () => {
    try {
        // Define the new pages and their default permissions
        const newPages = [
            { pageId: 56, permissions: '[{"Permission":"Visible","Description":"Visible","Value":true},{"Permission":"Add","Description":"Create new contractor(s)","Value":true},{"Permission":"Delete","Description":"Delete contractor(s)","Value":true},{"Permission":"Edit","Description":"Edit existing contractor(s)","Value":true}]' },
            { pageId: 57, permissions: '[{"Permission":"Visible","Description":"Visible","Value":true},{"Permission":"Add","Description":"Create new client(s)","Value":true},{"Permission":"Delete","Description":"Delete client(s)","Value":true},{"Permission":"Edit","Description":"Edit existing client(s)","Value":true}]' },
            { pageId: 58, permissions: '[{"Permission":"Visible","Description":"Visible","Value":true},{"Permission":"Add","Description":"Create new activity(ies)","Value":true},{"Permission":"Delete","Description":"Delete activity(ies)","Value":true},{"Permission":"Edit","Description":"Edit existing activity(ies)","Value":true}]' },
            { pageId: 59, permissions: '[{"Permission":"Visible","Description":"Visible","Value":true},{"Permission":"Add","Description":"Create new company(ies)","Value":true},{"Permission":"Delete","Description":"Delete company(ies)","Value":true},{"Permission":"Edit","Description":"Edit existing company(ies)","Value":true}]' }
        ];

        // Fetch all roles that have permissions for pageId 5 using LIKE
        const roles = await awaitSafeQuery("SELECT supr_id, supr_permission_details FROM sitefotos_user_permissions_roles WHERE supr_permission_details LIKE '%\"5\":%' and supr_id>11501");

        if (roles.length === 0) {
            console.log('No roles found with pageId 5');
            return;
        }

        // Iterate over each role and add the new pages with mirrored permissions
        for (const role of roles) {
            let rolePermissions = JSON.parse(role.supr_permission_details || '{}');

            // Get permissions for pageId 5
            const page5Permissions = rolePermissions[5] || [];
            if(page5Permissions.length === 0)
            {
                console.log('No permissions found for pageId 5');
                continue;
            }
            // Map the permissions from pageId 5 to the new pages
            newPages.forEach(newPage => {
                if (!rolePermissions[newPage.pageId]) {
                    const newPagePermissionsArray = JSON.parse(newPage.permissions); // Parse the permissions JSON string

                    rolePermissions[newPage.pageId] = page5Permissions.map(permission => {
                        const newPagePermission = newPagePermissionsArray.find(p => p.Permission === permission.Permission);
                        return {
                            ...permission,
                            Description: newPagePermission ? newPagePermission.Description : permission.Description
                        };
                    });
                }
            });
        
            // Update the role's permissions in the database
            const updatedPermissions = JSON.stringify(rolePermissions);
            await awaitSafeQuery('UPDATE sitefotos_user_permissions_roles SET supr_permission_details = ? WHERE supr_id = ?', [updatedPermissions, role.supr_id]);
        }

        console.log('New pages added to applicable roles successfully.');

    } catch (error) {
        console.error('Error adding new pages to roles:', error);
    } finally {
        console.log('Script execution completed.');
    }
};
const changeVendorBillingEmail = async (req, res, next) => {
    try {
        const { vendorId, newEmail } = req.body;

        const vendor = await awaitSafeQuery('SELECT vendor_id FROM maptile_vendors WHERE vendor_id = ?', [vendorId]);
        if(vendor.length === 0)
        {
            res.status(404).send({message: 'Vendor not found'});
            return;
        }
        const chargifyData = await awaitSafeQuery('SELECT mc_sub, mc_cus, mc_pay FROM maptile_chargify WHERE mc_vendor = ?', [vendorId]);
        if(chargifyData.length === 0)
        {
            res.status(404).send({message: 'Chargify status not found'});
            return;
        }
        const chargifySub = chargifyData[0].mc_sub;
        const chargifyCus = chargifyData[0].mc_cus;
        const chargifyPay = chargifyData[0].mc_pay;

      
        const chargifyDomain = process.env.CHARGIFY_SUB_DOMAIN;
        const chargifyApiKey = process.env.CHARGIFY_API_KEY;
        
     
        try {
            const response = await fetch(`https://${chargifyDomain}.chargify.com/customers/${chargifyCus}.json`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Basic ' + btoa(`${chargifyApiKey}:x`),
                },
                body: JSON.stringify({
                    customer: {
                        email: newEmail
                    }
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                console.error('Chargify API error:', errorData);
                res.status(500).send({message: 'Failed to update billing email in payment system'});
                return;
            }
            
          
            res.status(200).send({message: 'Billing email updated successfully'});
            
        } catch (error) {
            console.error('Error updating billing email in Chargify:', error);
            res.status(500).send({message: 'Failed to update billing email'});
        }
    } catch (error) {
        next(error);
    }
};

const getVendorSettings = async (req, res, next) => {
    try {
        const { vendorId } = req.body;
        if (!vendorId) {
            return res.status(400).send('Vendor ID is required');
        }

        const vendorDetails = await awaitSafeQuery(`SELECT vendor_payment_lock, vendor_app_lock, vendor_restrict_sites_radius FROM maptile_vendors WHERE vendor_id = ?`, [vendorId]);
        if (vendorDetails.length === 0) {
            return res.status(404).send('Vendor not found');
        }

        const chargifyDetails = await awaitSafeQuery(`SELECT mc_type FROM maptile_chargify WHERE mc_vendor = ?`, [vendorId]);
        
        // Assume default values if not found
        let noCreditCard = false;
        if (chargifyDetails.length > 0) {
            noCreditCard = chargifyDetails[0].mc_type === '2';
        }

        res.status(200).json({
            paymentLock: vendorDetails[0].vendor_payment_lock,
            appLock: vendorDetails[0].vendor_app_lock,
            radiusRestriction: vendorDetails[0].vendor_restrict_sites_radius === 'Y',
            noCreditCard: noCreditCard
        });

    } catch (ex) {
        next(ex);
    }
};

const updateVendorSettings = async (req, res, next) => {
    try {
        const { vendorId, paymentLock, appLock, radiusRestriction, noCreditCard } = req.body;

        if (!vendorId) {
            return res.status(400).send('Vendor ID is required');
        }

        
        await awaitSafeQuery(
            `UPDATE maptile_vendors SET vendor_payment_lock = ?, vendor_app_lock = ?, vendor_restrict_sites_radius = ? WHERE vendor_id = ?`,
            [paymentLock, appLock, radiusRestriction ? 'Y' : 'N', vendorId]
        );

        
        const mcType = noCreditCard ? '2' : '1';
        
        const chargifyRecord = await awaitSafeQuery('SELECT mc_id FROM maptile_chargify WHERE mc_vendor = ?', [vendorId]);

        if (chargifyRecord.length > 0) {
            await awaitSafeQuery(
                `UPDATE maptile_chargify SET mc_type = ? WHERE mc_vendor = ?`,
                [mcType, vendorId]
            );
        } 

        res.status(200).send('Vendor settings updated successfully');

    } catch (ex) {
        next(ex);
    }
};
//addNewPagesToRoles();
module.exports = {
    addPage,
    baseURL,
    headers,
    home,
    getVendorDetails,
    changeVendorPassword,
    deactivateVendor,
    getVendorPassword,
    changeVendorShortCode,
    toggleVendorAppRadius,
    getUserDetails,
    changeUserPassword,
    deactivateUser,
    getUserPassword,
    copySiteResources,
    moveSite,
    getSiteForms,
    copyForm,
    lockForm,
    getClientViewVendors,
    getClientViewSites,
    addClientViewSite,
    updateClientViewSiteMetadata,
    fetchClientViewSiteMetadata,
    addHighresBalance,
    lockUnclockVendor,
    mapMeasurements,
    forceClockOut,
    syncAllStorms,
    getHybridVendors,
    getHybridSites,
    addHybridSite,
    updateIndex,
    getPages,
    getPagePermissions,
    addUpdatePagePermission,
    deletePagePermission,
    syncPagePermissions,
    addNewPage,
    getRestrictedPages,
    getPageVendorIds,
    updateVendorAccess,
    changeVendorEmail,
    changeVendorBillingEmail,
    cvsReindex,
    getVendorSettings,
    updateVendorSettings
}