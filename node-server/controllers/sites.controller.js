const Sites = require('../services/sites.services');
const Issues = require('../services/issues.services');
const { awaitQuery, updateObj, awaitSafeQuery, awaitCachedQuery,insertObj,getDirectConnection } = require('../utils/db')
const { login, validateVendorAccessCode, UserPrivilege} = require('../utils/vendor');
const { serialize, unserialize } = require('php-serialize');
const { uniqBy, chain, update } = require('lodash')
const { redisClient } = require('../utils/redis-client.js')
const Form = require('../services/form.services');
const common = require('../utils/common.js');
const fleetdb = require('../utils/fleetdb.js');
const turf = require('@turf/turf')
const { updateVendorSitesChargify } = require('../utils/sites')

const createWorkorderIssue = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        let issueId = req.params.issue_id;
        let data = req.body;
        let result = await Issues.createWorkorderIssue(vendorId, siteId, issueId, data);
        res.json(result);
    } catch (ex) {
        next(ex);
    }

}


const updateCpsSite = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        if(!vendorId) return res.sendStatus(401);
        if(vendorId != 17758) return res.sendStatus(401);
        let cpsId = req.params.cps_id;
        let data = req.body
        if(data.site_name)
            await updateObj('sitefotos_client_sites', {scs_client_site_name: data.site_name}, ['scs_id'], [cpsId]);
        if(data.bm)
            await updateObj('sitefotos_client_sites_data', {scsd_property_manager: data.bm}, ['scsd_client_site_id'], [cpsId]);
        if(data.afm)
            await updateObj('sitefotos_client_sites_data', {scsd_senior_property_manager: data.afm}, ['scsd_client_site_id'], [cpsId]);
        if(data.network)
            await updateObj('sitefotos_client_sites_data', {scsd_location_type: data.network}, ['scsd_client_site_id'], [cpsId]);
        if(data.zone)
            await updateObj('sitefotos_client_sites_data', {scsd_zone: data.zone}, ['scsd_client_site_id'], [cpsId]);

        res.json(1);
    } catch (ex) {
        next(ex);
    }
}

const getCpsSites = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        if(!vendorId) return res.sendStatus(401);
        if(vendorId != 17758) return res.sendStatus(401);
        let data = await awaitQuery(`select sscm_client_site_id as sitefotos_id, scs_client_internal_id as cps_id, scs_client_site_name as site_name, scsd_property_manager as bm, scsd_senior_property_manager as afm, scsd_location_type as network, scsd_zone as zone from sitefotos_site_client_mapping_extended_grouped  left join sitefotos_client_sites_data on sscm_client_site_id=scsd_client_site_id where scs_client_vendor_id =17758`);
        res.json(data);

    } catch (ex) {
        next(ex);
    }
}

//issues related functions are put here, maybe we can move them to a separate file later
const getIssues = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const result = await Issues.getIssues(vendorId);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const getIssuesSite = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        const result = await Issues.getIssuesSite(vendorId, siteId);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const getIssuesDashboard = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const result = await Issues.getIssuesDashboard(vendorId);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const groupIssues = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        let data = req.body;
        const result = await Issues.groupIssues(vendorId, siteId, data);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const ungroupIssues = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        let groupId = req.params.group_id;
        const result = await Issues.ungroupIssues(vendorId, siteId, groupId);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const removeIssueFromGroup = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let issueId = req.params.issue_id;
        let groupId = req.params.group_id;
        let siteId = req.params.site_id;
        const result = await Issues.removeIssueFromGroup(vendorId, siteId, groupId, issueId);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const addIssuesToGroup = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        let groupId = req.params.group_id;
        let issueIds = req.body.issueIds;
        const result = await Issues.addIssuesToGroup(vendorId, siteId, groupId, issueIds);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}
const updateIssue = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        let issueId = req.params.issue_id;
        let data = req.body;
        const result = await Issues.updateIssue(vendorId, siteId, issueId, data);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

const updateGroup = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        let siteId = req.params.site_id;
        let groupId = req.params.group_id;
        let data = req.body;
        const result = await Issues.updateGroup(vendorId, siteId, groupId, data);
        res.json(result);
    } catch (ex) {
        next(ex);
    }
}

// vpics/getbuildings
const getBuildings = async (req, res, next) => {
    try {
        const accessCode = req.body.accessCode;



        const token = req.cookies.JWT;
        const { vendorId, internalUid, isClientViewEnabled, restrictedUser, userContactId } = await login(token);
        if (!vendorId) {
            return res.sendStatus(401);
        }
        const result = {
            buildings: []
        };
        const rows = await awaitSafeQuery(
            `SELECT mb_id, mb_nickname, mb_address1, mb_zip_code, mb_geo, mb_lat, mb_long, (SELECT City FROM maptile_city WHERE CityId = mb_city) AS cityname, (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) AS statename FROM maptile_building WHERE mb_user_id = ? AND mb_user_type = '1' AND mb_status = '1' ${restrictedUser && userContactId>0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''} ORDER BY mb_nickname`,
            [vendorId]
        );
        result.buildings = rows;
        res.json(result);
    } catch (err) {
        next(err);
    }
};

const getClientModelData = async (req, res, next) => {
    try {
        let { vendorId, internalUid } = await login(req.cookies.JWT);
        let result = await awaitQuery(`SELECT mb_id, mb_nickname, IF(mb_user_id = '?', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code, mb_geo, mb_lat, mb_long, (SELECT City FROM maptile_city WHERE CityId = mb_city) as cityname, (SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb_client) as customername, (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) as statename, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_client, mb_contract_type, mb_vendor_internal_id, mb_hybrid, mb_id as sscm_grouped_sites, '0' as CLIENTVIEW FROM maptile_building WHERE (mb_user_id = '?' OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) AND mb_user_type = '1' UNION SELECT mb.mb_id, scs_client_site_name as mb_nickname, '0' AS mb_own, mb.mb_external_id, mb.mb_external_src, mb.mb_address1, mb.mb_country, mb.mb_min_zoom, mb.mb_status, mb.mb_zip_code, mb.mb_geo, mb.mb_lat, mb.mb_long, (SELECT City FROM maptile_city WHERE CityId = mb.mb_city) as cityname, (SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb.mb_client) as customername, (SELECT state_abv_name FROM maptile_state WHERE id = mb.mb_state) as statename, mb.mb_notes, mb.mb_maps, mb.mb_ohours, mb.mb_global_customer_location_id, mb.mb_manager, mb.mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb.mb_zone_id) as mb_zone, mb.mb_client, mb.mb_contract_type, scme.scs_client_internal_id as mb_vendor_internal_id, mb.mb_hybrid, scme.sscm_grouped_sites as sscm_grouped_sites, '1' as CLIENTVIEW FROM maptile_building mb JOIN sitefotos_site_client_mapping_extended_grouped scme ON mb.mb_id = scme.sscm_site_id WHERE scme.scs_client_vendor_id = ? ORDER BY mb_nickname`,[vendorId, vendorId, vendorId, vendorId]);
        let buildings = await awaitSafeQuery(`select * from sitefotos_subuser_configuration where ssc_user_id=?`,[internalUid])

        let buildingIds = buildings.map((item)=>item.ssc_data)[0]
        let result2;
        if(buildingIds.length > 0)
         result2 = await awaitQuery(`SELECT * from(SELECT mb_id, mb_nickname, IF(mb_user_id = '?', '1', '0') AS mb_own, mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code, mb_geo, mb_lat, mb_long, (SELECT City FROM maptile_city WHERE CityId = mb_city) as cityname, (SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb_client) as customername, (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) as statename, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_client, mb_contract_type, mb_vendor_internal_id, mb_hybrid, mb_id as sscm_grouped_sites, '0' as CLIENTVIEW FROM maptile_building WHERE (mb_user_id = '?' OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) AND mb_user_type = '1' UNION SELECT mb.mb_id, scs_client_site_name as mb_nickname, '0' AS mb_own, mb.mb_external_id, mb.mb_external_src, mb.mb_address1, mb.mb_country, mb.mb_min_zoom, mb.mb_status, mb.mb_zip_code, mb.mb_geo, mb.mb_lat, mb.mb_long, (SELECT City FROM maptile_city WHERE CityId = mb.mb_city) as cityname, (SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb.mb_client) as customername, (SELECT state_abv_name FROM maptile_state WHERE id = mb.mb_state) as statename, mb.mb_notes, mb.mb_maps, mb.mb_ohours, mb.mb_global_customer_location_id, mb.mb_manager, mb.mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb.mb_zone_id) as mb_zone, mb.mb_client, mb.mb_contract_type, scme.scs_client_internal_id as mb_vendor_internal_id, mb.mb_hybrid, scme.sscm_grouped_sites as sscm_grouped_sites, '1' as CLIENTVIEW FROM maptile_building mb JOIN sitefotos_site_client_mapping_extended_grouped scme ON mb.mb_id = scme.sscm_site_id WHERE scme.scs_client_vendor_id = ? ORDER BY mb_nickname) AS derived_table where mb_id IN (${buildingIds})`,[vendorId, vendorId, vendorId, vendorId]);
        else
        result2 = [];

        res.json({all: result, user: result2});
    } catch(ex) {
        next(ex);
    }

}

const getClientModelSiteMetadata = async (req, res, next) => {
    try {
        let { vendorId, internalUid } = await login(req.cookies.JWT);

        let buildingIds = [];
        if (internalUid) {
            let buildings = await awaitSafeQuery(`select ssc_data from sitefotos_subuser_configuration where ssc_user_id=?`, [internalUid])
            buildingIds = buildings.map((item) => item.ssc_data)[0];

        }
        else {
            let buildings = await awaitSafeQuery(`SELECT sscm_site_id FROM sitefotos_site_client_mapping_extended_ungrouped WHERE scs_client_vendor_id = ?`, [vendorId])
            buildingIds = buildings.map((item) => item.sscm_site_id);
        }
        if(buildingIds.length == 0) return res.json([]);
        let internalIds = await awaitSafeQuery(`select scs_client_internal_id, scsd_region, scsd_property_manager, scsd_senior_property_manager, scsd_regional_manager, scsd_location_type, scsd_leased_owned, scsd_contract_type,scsd_landscape_season from sitefotos_site_client_mapping_extended_ungrouped left join sitefotos_client_sites_data on sscm_client_site_id = scsd_client_site_id where sscm_site_id in (${buildingIds})  group by scs_client_internal_id`, []);

        return res.json(internalIds);

    } catch (ex) {
        next(ex);
    }
}

const saveClientModelData = async (req, res) => {
    try {
        const {internalUid} = await login(req.cookies.JWT);
        let buildingIds = [];


        const validInternalData = req.body.filter(item => item.internal);
        const nullInternalData = req.body.filter(item => !item.internal);


        if (validInternalData.length > 0) {
            const results = await awaitQuery(`select sscm_site_id from sitefotos_site_client_mapping_extended_ungrouped where scs_client_internal_id in (?)`, [validInternalData.map(a => a.internal)]);
            buildingIds = results.map(item => item.sscm_site_id.toString());
        }


        nullInternalData.forEach(item => buildingIds.push(item.mb_id.toString()));

        const obj = { ssc_data: JSON.stringify(buildingIds) };
        await updateObj('sitefotos_subuser_configuration', obj, ['ssc_user_id'], [internalUid]);
        res.json("1");
    } catch (ex) {
        console.error(ex);
        res.status(500).send(ex.message);
    }
};


const getBuildingsOnlySiteLeads = async (req, res, next) => {
    try {
        const accessCode = req.body.accessCode;



        const token = req.cookies.JWT;
        const { vendorId } = await login(token, accessCode);
        if (!vendorId) {
            return res.sendStatus(401);
        }
        const result = {
            buildings: []
        };
        const rows = await awaitSafeQuery(
          "SELECT mb_id, mb_nickname, mb_address1, mb_zip_code, mb_geo, mb_lat, mb_long, (SELECT City FROM maptile_city WHERE CityId = mb_city) AS cityname, (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) AS statename FROM maptile_building WHERE mb_user_id = ? AND mb_user_type = '1' AND mb_status = '3' ORDER BY mb_nickname",
          [vendorId]
        );
        result.buildings = rows;
        res.json(result);
    } catch (err) {
        next(err);
    }
};
const updateSiteMaps = async (req,res,next) => {
    try {
        const { vendorId, internalUid,isClientViewEnabled } = await login(req.cookies.JWT);
        let id = req.params.site_id;
        let { maps } = req.body;
        let data = {
            mb_maps: JSON.stringify(maps)
        }
        await updateObj('maptile_building', data, ['mb_id'], [id]);
        return res.json("1");
    } catch (ex) {
        next(ex);
    }
}

const getSitesContractors = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        let query = /*SQL*/`SELECT mb_id as SITEID, mb_nickname as SITENAME, mb_address1 as ADDRESS1, mb_city as CITYID, ( select City from maptile_city where CityId = mb_city ) as CITYNAME, mb_state as STATEID, mb_status as STATUS, ( select state_abv_name from maptile_state where id = mb_state ) as STATENAME, mb_zip_code as ZIPCODE, mb_client as CLIENTID, ( select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client ) as site_contact_primary, mb_manager as MANAGER, mb_manager_secondary as MANAGER2, mb_manager_third as MANAGER3, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager ) as primary_manager, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_secondary ) as secondary_manager, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_third ) as third_manager, REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '') as contractors , (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as ZONED, mb_contract_type as TRADES, mb_hybrid as HYBRID, mb_external_src as EXTERNALPROVIDER, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id) as QUICKBOOKSCUSTOMER, mb_user_id, mb_external_id as EXTERNALPROVIDERID, mb_vendor_internal_id as INTERNALID, IF(mb_user_id = '?', '1', '0') AS OWN, '0' as CLIENTVIEW from maptile_building where (mb_user_id = ? or JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) and mb_user_type = '1'  AND mb_status IN ('1', '2')`;
        let sites = await awaitQuery(query, [vendorId, vendorId, vendorId]);
        let forms = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM sf_form_site)), '\"', '') as sites, sf_form_name, sf_id,sf_vendor_id from sitefotos_forms where  sf_vendor_id = ? AND sf_active = '1' AND sf_form_workticket=0 and sf_form_workticket_fiwo=0 and sf_form_provider is null`, [vendorId]);
        let contractors = await awaitSafeQuery(/*SQL*/`select sf_contact_id, sf_contact_fname, sf_contact_lname, sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_vendorid = ? and sf_contact_type = 'Contractor' `, [vendorId]);
        sites = sites.map(x => {
            x.TRADES = x.TRADES ? x.TRADES.split(',') : [];
            if (x.contractors != null) {
                let con = x.contractors.split(',');
                for (let i = 0; i < con.length; i++) {
                    let target = contractors.find(y => y.sf_contact_id == con[i])
                    if (!target) continue;
                    if (!x.CONTRACTORIDS) {
                        x.CONTRACTORIDS = [];
                        x.CONTRACTORNAMES = [];
                    }
                    x.CONTRACTORIDS.push(target.sf_contact_id);
                    if (target.sf_contact_company_name)
                        x.CONTRACTORNAMES.push(target.sf_contact_company_name);
                    else
                        x.CONTRACTORNAMES.push(target.sf_contact_fname + ' ' + target.sf_contact_lname);
                }
            }
            if (!x.CONTRACTORIDS) {
                x.CONTRACTORIDS = [];
                x.CONTRACTORNAMES = [];
            }
            if (!x.ROUTEIDS) {
                x.ROUTEIDS = [];
                x.ROUTENAMES = [];
            }
            if (!x.FORMIDS) {
                x.FORMIDS = [];
                x.FORMNAMES = [];
            }
            return x;
        });
        res.json(sites);
    } catch (ex) {
        next(ex);
    }
};


const getSitesTabulator = async (req, res, next) => {
    try {
        const { vendorId, internalUid, isClientViewEnabled, restrictedUser, userContactId } = await login(req.cookies.JWT);
        let { filter = [], page = 1, size = 100, sort = [] } = req.body;

        let cacheBust = req.body.cacheBust ?? false;

        let id = `${vendorId}-${internalUid}`;
        const cache = await redisClient.get(`GET_SITES_TABULATOR:${id}`);
        let sites = [];
        if (cache && !cacheBust)
            sites = JSON.parse(cache);
        else {
            if (isClientViewEnabled == false) {

                let query = /*SQL*/`
    SELECT 
        mb_id as SITEID, 
        mb_nickname as SITENAME, 
        mb_address1 as ADDRESS1, 
        mb_city as CITYID,
        (select City from maptile_city where CityId = mb_city) as CITYNAME, 
        mb_state as STATEID,
        mb_status as STATUS,
        UNIX_TIMESTAMP(mb_added_date) as ADDEDDATE, 
        (select state_abv_name from maptile_state where id = mb_state) as STATENAME, 
        mb_zip_code as ZIPCODE,
        IF(mb_user_id = ?, mb_client, NULL) as CLIENTID,
        IF(mb_user_id = ?, (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client), NULL) as site_contact_primary,
        IF(mb_user_id = ?, mb_manager, NULL) as MANAGER,
        IF(mb_user_id = ?, mb_manager_secondary, NULL) as MANAGER2,
        IF(mb_user_id = ?, mb_manager_third, NULL) as MANAGER3,
        IF(mb_user_id = ?, (select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager), NULL) as primary_manager,
        IF(mb_user_id = ?, (select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_secondary), NULL) as secondary_manager,
        IF(mb_user_id = ?, (select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_third), NULL) as third_manager,
        IF(mb_user_id = ?, REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', ''), NULL) as contractors,
        (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as ZONED,
        mb_contract_type as TRADES,
        mb_hybrid as HYBRID,
        IF(mb_user_id = ?, mb_external_src, NULL) as EXTERNALPROVIDER,
        IF(mb_user_id = ?, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id), NULL) as QUICKBOOKSCUSTOMER,
        mb_user_id,
        IF(mb_user_id = ?, mb_external_id, NULL) as EXTERNALPROVIDERID,
        mb_vendor_internal_id as INTERNALID,
        IF(mb_user_id = ?, 'Internal', (select vendor_company_name from maptile_vendors where vendor_id = mb_user_id)) AS OWN,
        '0' as CLIENTVIEW, mb_locked as LOCKED
    from maptile_building 
    where (mb_user_id = ? or JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) 
    and mb_user_type = '1'
    AND mb_status IN ('1', '2')
    ${restrictedUser && userContactId>0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''}`;

sites = await awaitQuery(query, [
    vendorId, // For CLIENTID
    vendorId, // For site_contact_primary
    vendorId, // For MANAGER
    vendorId, // For MANAGER2
    vendorId, // For MANAGER3
    vendorId, // For primary_manager
    vendorId, // For secondary_manager
    vendorId, // For third_manager
    vendorId, // For contractors
    vendorId, // For EXTERNALPROVIDER
    vendorId, // For QUICKBOOKSCUSTOMER
    vendorId, // For EXTERNALPROVIDERID
    vendorId, // For OWN
    vendorId, // For where clause mb_user_id = ?
    vendorId  // For JSON_CONTAINS
]);

                //sites = await awaitQuery(query, [vendorId, vendorId, vendorId]);
            } else {
                let unionQuery = /*SQL*/` UNION SELECT mb_id as SITEID, mb_nickname as SITENAME, UNIX_TIMESTAMP(mb_added_date) as ADDEDDATE,  mb_address1 as ADDRESS1, mb_city as CITYID, ( select City from maptile_city where CityId = mb_city ) as CITYNAME, mb_status as STATUS, mb_state as STATEID, ( select state_abv_name from maptile_state where id = mb_state ) as STATENAME, mb_zip_code as ZIPCODE, mb_client as CLIENTID, ( select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client ) as site_contact_primary, mb_manager as MANAGER, mb_manager_secondary as MANAGER2, mb_manager_third as MANAGER3, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager ) as primary_manager, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_secondary ) as secondary_manager, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_third ) as third_manager, REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '') as contractors , (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as ZONED, mb_contract_type as TRADES, mb_hybrid as HYBRID, mb_external_src as EXTERNALPROVIDER, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id) as QUICKBOOKSCUSTOMER, mb_user_id, mb_external_id as EXTERNALPROVIDERID, mb_vendor_internal_id as INTERNALID, 'External' AS OWN, '1' as CLIENTVIEW, '1' as LOCKED from maptile_building JOIN sitefotos_site_client_mapping_extended_grouped ON mb_id = sscm_site_id WHERE scs_client_vendor_id = ?`;
                let query = /*SQL*/`SELECT mb_id as SITEID, mb_nickname as SITENAME,UNIX_TIMESTAMP(mb_added_date) as ADDEDDATE,  mb_address1 as ADDRESS1, mb_city as CITYID, ( select City from maptile_city where CityId = mb_city ) as CITYNAME, mb_status as STATUS, mb_state as STATEID, ( select state_abv_name from maptile_state where id = mb_state ) as STATENAME, mb_zip_code as ZIPCODE, mb_client as CLIENTID, ( select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client ) as site_contact_primary, mb_manager as MANAGER, mb_manager_secondary as MANAGER2, mb_manager_third as MANAGER3, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager ) as primary_manager, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_secondary ) as secondary_manager, ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_manager_third ) as third_manager, REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '') as contractors , (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as ZONED, mb_contract_type as TRADES, mb_hybrid as HYBRID, mb_external_src as EXTERNALPROVIDER, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id) as QUICKBOOKSCUSTOMER, mb_user_id, mb_external_id as EXTERNALPROVIDERID, mb_vendor_internal_id as INTERNALID, IF(mb_user_id = '?', 'Internal', 'External') AS OWN, '0' as CLIENTVIEW, '1' as LOCKED from maptile_building where (mb_user_id = ? or JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) and mb_user_type = '1' and mb_status = '1'` + unionQuery;
                sites = await awaitQuery(query, [vendorId, vendorId, vendorId, vendorId]);
            }

            let forms = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM sf_form_site)), '\"', '') as sites, sf_form_name, sf_id,sf_vendor_id from sitefotos_forms where  sf_vendor_id = ? AND sf_active = '1' AND sf_form_workticket=0 and sf_form_workticket_fiwo=0 and sf_form_provider is null`, [vendorId]);

            let routes = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM r_sites)), '\"', '') as sites, r_id, r_name from sitefotos_routes_2 where r_vid = ? and r_active = '1' AND r_status = '1' and r_sites <> '' and r_sites is not null`, [vendorId]);

            let contractors = await awaitSafeQuery(/*SQL*/`select sf_contact_id, sf_contact_fname, sf_contact_lname, sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_vendorid = ? and sf_contact_type = 'Contractor' `, [vendorId]);



            const siteMap = new Map(sites.map(site => [site.SITEID, site]));
            for (let form of forms) {

                if (form.sites == null || form.sites === '') continue;

                let formSites = form.sites.split(',');
                if (formSites.includes('-1')) {
                    const formOwner = form.sf_vendor_id;
                    formSites = sites.filter(site => site.mb_user_id == formOwner).map(site => site.SITEID);
                }

                for (let siteId of formSites) {
                    let target = siteMap.get(parseInt(siteId));
                    if (!target) continue;

                    // Initialize FORMIDS and FORMNAMES if they don't exis
                    if (!target.FORMIDS) {
                        target.FORMIDS = [];
                        target.FORMNAMES = [];
                    }

                    // Add form IDs and names
                    target.FORMIDS.push(form.sf_id);
                    target.FORMNAMES.push(form.sf_form_name);
                }
            }






            for (let route of routes) {
                if (route.sites == null) continue;
                if (route.sites == '') continue;
                let bids = route.sites.split(',');
                let routeSites = bids;
                for (let site of routeSites) {
                    let target = sites.find(x => x.SITEID == site)
                    if (!target) continue;
                    if (!target.ROUTEIDS) {
                        target.ROUTEIDS = [];
                        target.ROUTENAMES = [];
                    }
                    target.ROUTEIDS.push(route.r_id);
                    target.ROUTENAMES.push(route.r_name);
                }
            }


            sites = sites.map(x => {
                x.TRADES = x.TRADES ? x.TRADES.split(',') : [];
                if (x.contractors != null) {
                    let con = x.contractors.split(',');
                    for (let i = 0; i < con.length; i++) {
                        let target = contractors.find(y => y.sf_contact_id == con[i])
                        if (!target) continue;
                        if (!x.CONTRACTORIDS) {
                            x.CONTRACTORIDS = [];
                            x.CONTRACTORNAMES = [];
                        }
                        x.CONTRACTORIDS.push(target.sf_contact_id);
                        if (target.sf_contact_company_name)
                            x.CONTRACTORNAMES.push(target.sf_contact_company_name);
                        else
                            x.CONTRACTORNAMES.push(target.sf_contact_fname + ' ' + target.sf_contact_lname);
                    }
                }
                if (!x.CONTRACTORIDS) {
                    x.CONTRACTORIDS = [];
                    x.CONTRACTORNAMES = [];
                }
                if (!x.ROUTEIDS) {
                    x.ROUTEIDS = [];
                    x.ROUTENAMES = [];
                }
                if (!x.FORMIDS) {
                    x.FORMIDS = [];
                    x.FORMNAMES = [];
                }
                return x;
            });

            await redisClient.set(`GET_SITES_TABULATOR:${id}`, JSON.stringify(sites), {EX: 60 * 60});
        }


        let uniqueTrades = chain(sites).map('TRADES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        });
        let uniqueStates = uniqBy(sites, 'STATENAME').filter(a => a.STATENAME).sort(function (a, b) {
            return a.STATENAME.localeCompare(b.STATENAME)
        }).map(a => { return a.STATENAME });

        let uniqueRoutes = chain(sites).map('ROUTENAMES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        });
        let uniqueContractors = chain(sites).map('CONTRACTORNAMES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        });
        let uniqueSystems = uniqBy(sites, 'EXTERNALPROVIDER').filter(a => a.EXTERNALPROVIDER).sort(function (a, b) {
            return a.EXTERNALPROVIDER.localeCompare(b.EXTERNALPROVIDER)
        }).map(a => { return a.EXTERNALPROVIDER });
        let uniqueZones = uniqBy(sites, 'ZONED').filter(a => a.ZONED).sort(function (a, b) {
            return a.ZONED.localeCompare(b.ZONED)
        }).map(a => { return a.ZONED });
        let uniqueClients = uniqBy(sites, 'site_contact_primary').filter(a => a.site_contact_primary).sort(function (a, b) {
            return a.site_contact_primary.localeCompare(b.site_contact_primary)
        }).map(a => { return a.site_contact_primary });
        let uniqueManagers = uniqBy(sites, 'primary_manager').filter(a => a.primary_manager).sort(function (a, b) {
            return a.primary_manager.localeCompare(b.primary_manager)
        }).map(a => { return a.primary_manager });
        let uniqueSecondaryManagers = uniqBy(sites, 'secondary_manager').filter(a => a.secondary_manager).sort(function (a, b) {
            return a.secondary_manager.localeCompare(b.secondary_manager)
        }).map(a => { return a.secondary_manager });
        let uniqueThirdManagers = uniqBy(sites, 'third_manager').filter(a => a.third_manager).sort(function (a, b) {
            return a.third_manager.localeCompare(b.third_manager)
        }).map(a => { return a.third_manager });
        let uniqueForms = chain(sites).map('FORMNAMES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        }).map(a => { return a });
        let uniqueSources = uniqBy(sites, 'OWN').filter(a => a.OWN).sort(function (a, b) {
            return a.OWN.localeCompare(b.OWN)
        }).map(a => { return a.OWN });
        let uniqueStatus = { "1": "ACTIVE", "2": "INACTIVE" }

        if (filter.length > 0) {
            for (let f of filter) {
                //if f.value is empty array or empty value then skip
                if (!f.value || (Array.isArray(f.value) && f.value.length == 0)) continue;
                switch (f.field) {
                    case 'SITEID':
                        sites = sites.filter(item => item.SITEID ? item.SITEID.toString().includes(f.value.toString()) : false);
                        break;
                    case 'STATENAME':
                        sites = sites.filter(item => item.STATENAME ? f.value.includes(item.STATENAME) : false);
                        break;
                    case 'FORMNAMES':
                        sites = sites.filter(item => item.FORMNAMES ? f.value.every(r => item.FORMNAMES.includes(r)) : false);
                        break;
                    case 'ZONED':
                        sites = sites.filter(item => item.ZONED ? f.value.includes(item.ZONED) : false);
                        break;
                    case "EXTERNALPROVIDER":
                        sites = sites.filter(item => item.EXTERNALPROVIDER ? f.value.includes(item.EXTERNALPROVIDER) : false);
                        break;
                    case "ADDRESS1":
                        sites = sites.filter(item => item.ADDRESS1 ? item.ADDRESS1.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "SITENAME":
                        sites = sites.filter(item => item.SITENAME ? item.SITENAME.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "ZIPCODE":
                        sites = sites.filter(item => item.ZIPCODE ? item.ZIPCODE.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "CITYNAME":
                        sites = sites.filter(item => item.CITYNAME ? item.CITYNAME.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "CONTRACTORNAMES":
                        Array.isArray(f.value) ? f.value : f.value = [f.value];
                        sites = sites.filter(item => item.CONTRACTORNAMES ? f.value.some(r => item.CONTRACTORNAMES.includes(r)) : false);
                        break;
                    case "TRADES":
                        Array.isArray(f.value) ? f.value : f.value = [f.value];
                        sites = sites.filter(item => item.TRADES ? item.TRADES.some(r => f.value.includes(r)) : false);
                        break;
                    case "ROUTENAMES":
                        Array.isArray(f.value) ? f.value : f.value = [f.value];
                        sites = sites.filter(item => item.ROUTENAMES ? f.value.some(r => item.ROUTENAMES.includes(r)) : false);
                        break;
                    case "EXTERNALPROVIDERID":
                        sites = sites.filter(item => item.EXTERNALPROVIDERID ? item.EXTERNALPROVIDERID.toString().includes(f.value.toString()) : false);
                        break;
                    case "QUICKBOOKSCUSTOMER":
                        sites = sites.filter(item => item.QUICKBOOKSCUSTOMER ? item.QUICKBOOKSCUSTOMER.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "OWN":
                        sites = sites.filter(item => item.OWN ? f.value.includes(item.OWN) : false);
                        break;
                    case "STATUS":
                        sites = sites.filter(item => item.STATUS ? f.value.includes(item.STATUS) : false);
                        break;
                    case "site_contact_primary":
                        sites = sites.filter(item => item.site_contact_primary ? f.value.includes(item.site_contact_primary) : false);
                        break;
                    case "primary_manager":
                        sites = sites.filter(item => item.primary_manager ? f.value.includes(item.primary_manager) : false);
                        break;
                    case "secondary_manager":
                        sites = sites.filter(item => item.secondary_manager ? f.value.includes(item.secondary_manager) : false);
                        break
                    case "third_manager":
                        sites = sites.filter(item => item.third_manager ? f.value.includes(item.third_manager) : false);
                        break
                    case 'ADDEDDATE':
                        {
                            let start = new Date(f.value.start).getTime() / 1000;
                            let end = new Date(f.value.end).getTime() / 1000;
                            sites = sites.filter(item => item.ADDEDDATE ? (item.ADDEDDATE >= start && item.ADDEDDATE <= end) : false);
                        }
                        break;

                }
            }
        }
        if (sort.length > 0) {
            sites.sort((a, b) => {
                for (let s of sort) {
                    if (a[s.field] > b[s.field]) {
                        return s.dir.toUpperCase() === 'ASC' ? 1 : -1;
                    } else if (a[s.field] < b[s.field]) {
                        return s.dir.toUpperCase() === 'ASC' ? -1 : 1;
                    }
                }
                return 0;
            });
        }
        const total = sites.length;
        if (size == true) size = total;
        const paginatedData = sites.slice((page - 1) * size, page * size);

        res.json({
            data: paginatedData,
            last_page: Math.ceil(total / size),
            last_row: total,
            trades: uniqueTrades,
            states: uniqueStates,
            routes: uniqueRoutes,
            contractors: uniqueContractors,
            systems: uniqueSystems,
            zones: uniqueZones,
            clients: uniqueClients,
            managers: uniqueManagers,
            secondaryManager: uniqueSecondaryManagers,
            thirdManager: uniqueThirdManagers,
            forms: uniqueForms,
            sources: uniqueSources,
            statuses: uniqueStatus
        });

    } catch (ex) {
        next(ex);
    }
}

const getSiteLeadsTabulator = async (req,res,next) => {
    try {
        const { vendorId, internalUid,restrictedUser, userContactId  } = await login(req.cookies.JWT);

        await initUserSiteLeadStatus(vendorId);

        const { filter = [], page = 1, size = 100, sort = [] } = req.body;

        let cacheBust = req.body.cacheBust ?? false;

        let id = `${vendorId}-${internalUid}`;

        const cache = await redisClient.get(`GET_SITE_LEADS_TABULATOR:${id}`);
        let sites = [];
        if (cache && !cacheBust)
            sites = JSON.parse(cache);
        else {

            let query = /*SQL*/`SELECT
                mb_notes as SITENOTES, mb_building_status as SITELEADSTATUS,
                mb_id as SITEID, mb_nickname as SITENAME, mb_address1 as ADDRESS1, mb_city as CITYID,
                ( select City from maptile_city where CityId = mb_city ) as CITYNAME,
                mb_state as STATEID, ( select state_abv_name from maptile_state where id = mb_state ) as STATENAME,
                mb_zip_code as ZIPCODE,
                mb_client as CLIENTID,
                mb_client_secondary as CLIENTID2,
                mb_client_third as CLIENTID3,
                ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_client ) as site_contact_primary,
                ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_client_secondary ) as site_contact_secondary,
                ( select concat(sf_contact_fname, " ", sf_contact_lname) from sitefotos_contacts where sf_contact_id = mb_client_third ) as site_contact_third, (select ssst_source from sitefotos_site_source_types where ssst_id = mb_source) as SITELEADSOURCE,
                REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '') as contractors , (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as ZONED, mb_contract_type as TRADES, mb_hybrid as HYBRID, mb_external_src as EXTERNALPROVIDER, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id) as QUICKBOOKSCUSTOMER, mb_user_id, mb_external_id as EXTERNALPROVIDERID, mb_vendor_internal_id as INTERNALID, IF(mb_user_id = '?', '1', '0') AS OWN from maptile_building where (mb_user_id = ? or JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) and mb_user_type = '1' and mb_status = '3' ${restrictedUser && userContactId>0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''}`;

            sites = await awaitQuery(query, [vendorId, vendorId, vendorId]);

            let forms = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM sf_form_site)), '\"', '') as sites, sf_form_name, sf_id,sf_vendor_id from sitefotos_forms where  sf_vendor_id = ? AND sf_active = '1' AND sf_form_workticket=0 and sf_form_workticket_fiwo=0 and sf_form_provider is null`, [vendorId]);

            let routes = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM r_sites)), '\"', '') as sites, r_id, r_name from sitefotos_routes_2 where r_vid = ? and r_active = '1' AND r_status = '1' and r_sites <> '' and r_sites is not null`, [vendorId]);

            let contractors = await awaitSafeQuery(/*SQL*/`select sf_contact_id, sf_contact_fname, sf_contact_lname, sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_vendorid = ? and sf_contact_type = 'Contractor' `, [vendorId]);




            sites = sites.map(x => {
                x.TRADES = x.TRADES ? x.TRADES.split(',') : [];

                return x;
            });
            await redisClient.set(`GET_SITE_LEADS_TABULATOR:${id}`, JSON.stringify(sites), {EX: 60 * 60});
        }
        //NOTE: This can be optimised by running a single for loop in site and extracting all unique values and insert them already sorted in the arrays of their own.
        let uniqueTrades = chain(sites).map('TRADES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        });
        let uniqueStates = uniqBy(sites, 'STATENAME').filter(a => a.STATENAME).sort(function (a, b) {
            return a.STATENAME.localeCompare(b.STATENAME)
        }).map(a => { return a.STATENAME });



        let uniqueSystems = uniqBy(sites, 'EXTERNALPROVIDER').filter(a => a.EXTERNALPROVIDER).sort(function (a, b) {
            return a.EXTERNALPROVIDER.localeCompare(b.EXTERNALPROVIDER)
        }).map(a => { return a.EXTERNALPROVIDER });
        let uniqueZones = uniqBy(sites, 'ZONED').filter(a => a.ZONED).sort(function (a, b) {
            return a.ZONED.localeCompare(b.ZONED)
        }).map(a => { return a.ZONED });
        let uniqueClients = uniqBy(sites, 'site_contact_primary').filter(a => a.site_contact_primary).sort(function (a, b) {
            return a.site_contact_primary.localeCompare(b.site_contact_primary)
        }).map(a => { return a.site_contact_primary });

        let uniqueSecondaryClients = uniqBy(sites, 'site_contact_secondary').filter(a => a.site_contact_secondary).sort(function (a, b) {
            return a.site_contact_secondary.localeCompare(b.site_contact_secondary)
        }).map(a => { return a.site_contact_secondary });

        let uniqueThirdClients = uniqBy(sites, 'site_contact_third').filter(a => a.site_contact_third).sort(function (a, b) {
            return a.site_contact_third.localeCompare(b.site_contact_third)
        }).map(a => { return a.site_contact_third });


        let uniqueSources = uniqBy(sites, 'SITELEADSOURCE').filter(a => a.SITELEADSOURCE).map(a => { return a.SITELEADSOURCE });

        let uniqueStatus = chain(sites).map('SITELEADSTATUS').flatten().uniq().value();
        let sql = /*SQL*/`select * from sitefotos_site_building_statuses where ssbs_vendor_id = ? and (ssbs_id in (${uniqueStatus.map(a => '?').join(',')}) or ssbs_active = 1)`;
        if (uniqueStatus.length == 0) sql = /*SQL*/`select * from sitefotos_site_building_statuses where ssbs_vendor_id = ? and ssbs_active = 1`;
        const allStatus = await awaitSafeQuery(sql, [vendorId, ...uniqueStatus]);

        if (filter.length > 0) {
            for (let f of filter) {
                //if f.value is empty array or empty value then skip
                if (!f.value || (Array.isArray(f.value) && f.value.length == 0)) continue;
                switch (f.field) {
                    case 'SITEID':
                        sites = sites.filter(item => item.SITEID ? item.SITEID.toString().includes(f.value.toString()) : false);
                        break;
                    case 'STATENAME':
                        sites = sites.filter(item => item.STATENAME ? f.value.includes(item.STATENAME) : false);
                        break;
                    case 'ZONED':
                        sites = sites.filter(item => item.ZONED ? f.value.includes(item.ZONED) : false);
                        break;
                    case "EXTERNALPROVIDER":
                        sites = sites.filter(item => item.EXTERNALPROVIDER ? f.value.includes(item.EXTERNALPROVIDER) : false);
                        break;
                    case "ADDRESS1":
                        sites = sites.filter(item => item.ADDRESS1 ? item.ADDRESS1.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "SITENAME":
                        sites = sites.filter(item => item.SITENAME ? item.SITENAME.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "SITELEADSOURCE":
                        sites = sites.filter(item => item.SITELEADSOURCE ? f.value.includes(item.SITELEADSOURCE) : false);
                        break;
                    case "ZIPCODE":
                        sites = sites.filter(item => item.ZIPCODE ? item.ZIPCODE.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "CITYNAME":
                        sites = sites.filter(item => item.CITYNAME ? item.CITYNAME.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "site_contact_primary":
                        sites = sites.filter(item => item.site_contact_primary ? f.value.includes(item.site_contact_primary) : false);
                        break;
                    case "CONTRACTORNAMES":
                        Array.isArray(f.value) ? f.value : f.value = [f.value];
                        sites = sites.filter(item => item.CONTRACTORNAMES ? f.value.some(r => item.CONTRACTORNAMES.includes(r)) : false);
                        break;
                    case "TRADES":
                        Array.isArray(f.value) ? f.value : f.value = [f.value];
                        sites = sites.filter(item => item.TRADES ? item.TRADES.some(r => f.value.includes(r)) : false);
                        break;
                    case "ROUTENAMES":
                        Array.isArray(f.value) ? f.value : f.value = [f.value];
                        sites = sites.filter(item => item.ROUTENAMES ? f.value.some(r => item.ROUTENAMES.includes(r)) : false);
                        break;
                    case "EXTERNALPROVIDERID":
                        sites = sites.filter(item => item.EXTERNALPROVIDERID ? item.EXTERNALPROVIDERID.toString().includes(f.value.toString()) : false);
                        break;
                    case "QUICKBOOKSCUSTOMER":
                        sites = sites.filter(item => item.QUICKBOOKSCUSTOMER ? item.QUICKBOOKSCUSTOMER.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
                        break;
                    case "OWN":
                        sites = sites.filter(item => item.OWN ? f.value.includes(item.OWN) : false);
                        break;
                    case "SITELEADSTATUS":
                        sites = sites.filter(item => item.SITELEADSTATUS ? f.value.includes(item.SITELEADSTATUS) : false);
                        break
                    case "site_contact_secondary":
                        sites = sites.filter(item => item.site_contact_secondary ? f.value.includes(item.site_contact_secondary) : false);
                        break
                    case "site_contact_third":
                        sites = sites.filter(item => item.site_contact_third ? f.value.includes(item.site_contact_third) : false);
                        break
                }
            }
        }

        if (sort.length > 0) {
            sites.sort((a, b) => {
                for (let s of sort) {
                    if (a[s.field] > b[s.field]) {
                        return s.dir.toUpperCase() === 'ASC' ? 1 : -1;
                    } else if (a[s.field] < b[s.field]) {
                        return s.dir.toUpperCase() === 'ASC' ? -1 : 1;
                    }
                }
                return 0;
            });
        }

        const total = sites.length;

        const paginatedData = sites.slice((page - 1) * size, page * size);

        res.json({
            data: paginatedData,
            last_page: Math.ceil(total / size),
            last_row: total,
            trades: uniqueTrades,
            states: uniqueStates,
            systems: uniqueSystems,
            zones: uniqueZones,
            clients: uniqueClients,
            secondaryClients: uniqueSecondaryClients,
            thirdClients: uniqueThirdClients,
            sources: uniqueSources,
            status: allStatus,
        });

    } catch (ex) {
        next(ex);
    }
}
//NOTE: Not used anywhere so not updating for client1, 2 3 and managers
const getSites = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const vendor = (await awaitSafeQuery(`select * from maptile_vendors where vendor_id=?`, [vendorId]))[0];

        const cacheBust = req.body.cacheBust ?? false;
        const activeSites = req.body.activeSites ?? true;

        const isHybrid = vendor['vendor_hybrid'];
        let sites = [];
        let id = `${vendorId}-${internalUid}`;
        const cache = await redisClient.get(`GET_SITES:${id}`);
        if (cache && !cacheBust)
            sites = JSON.parse(cache);
        else {


            let sql = /*SQL*/`SELECT mb_id as SITEID, mb_nickname as SITENAME, mb_address1 as ADDRESS1, mb_city as CITYID, ( select City from maptile_city where CityId = mb_city ) as CITYNAME, mb_state as STATEID, ( select state_abv_name from maptile_state where id = mb_state ) as STATENAME, mb_zip_code as ZIPCODE, mb_client as CLIENTID, ( select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client ) as CLIENTNAME, REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '') as contractors , (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as ZONED, mb_contract_type as TRADES, mb_hybrid as HYBRID, mb_external_src as EXTERNALPROVIDER, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id) as QUICKBOOKSCUSTOMER, mb_user_id, mb_external_id as EXTERNALPROVIDERID, mb_vendor_internal_id as INTERNALID, IF(mb_user_id = '?', '1', '0') AS OWN from maptile_building where (mb_user_id = ? or JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE('?'))) and mb_user_type = '1' and mb_status = '?'`;

            //let sql = /*SQL*/`SELECT mb_id as SITEID, mb_nickname as SITENAME, mb_address1 as ADDRESS1, mb_city as CITYID, ( select City from maptile_city where CityId = mb_city ) as CITYNAME, mb_state as STATEID, ( select state_abv_name from maptile_state where id = mb_state ) as STATENAME, mb_zip_code as ZIPCODE, mb_client as CLIENTID, ( select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client ) as CLIENTNAME, REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '') as contractors , mb_zone as ZONED, mb_contract_type as TRADES, mb_hybrid as HYBRID, mb_external_src as EXTERNALPROVIDER, (select sqc_display_name from sitefotos_quickbooks_contacts where sqc_id=mb_quickbooks_customer_id) as QUICKBOOKSCUSTOMER, mb_user_id, mb_external_id as EXTERNALPROVIDERID, mb_vendor_internal_id as INTERNALID, IF(mb_user_id = '?', '1', '0') AS OWN from maptile_building where mb_user_id = ?  and mb_user_type = '1' and mb_status = '?'`;

            sites = await awaitQuery(sql, [vendorId, vendorId, vendorId, activeSites ? 1 : 0]);
            let forms = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM sf_form_site)), '\"', '') as sites, sf_form_name, sf_id,sf_vendor_id from sitefotos_forms where  sf_vendor_id = ? AND sf_active = '1' AND sf_form_workticket=0 and sf_form_workticket_fiwo=0 and sf_form_provider is null`, [vendorId]);

            let routes = await awaitSafeQuery(/*SQL*/`select REPLACE(TRIM(']' FROM TRIM('[' FROM r_sites)), '\"', '') as sites, r_id, r_name from sitefotos_routes_2 where r_vid = ? and r_active = '1' AND r_status = '1' and r_sites <> '' and r_sites is not null`, [vendorId]);

            let contractors = await awaitSafeQuery(/*SQL*/`select sf_contact_id, sf_contact_fname, sf_contact_lname, sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_vendorid = ? and sf_contact_type = 'Contractor' `, [vendorId]);


            for (let form of forms) {
                if (form.sites == null) continue;
                if (form.sites == '') continue;
                let formSites = form.sites.split(',');
                if (formSites.includes('-1')) {
                    const formOwner = form.sf_vendor_id;
                    formSites = sites
                        .filter(site => site.mb_user_id == formOwner)
                        .map(site => site.SITEID);
                }
                for (let site of formSites) {
                    let target = sites.find(x => x.SITEID == site)
                    if (!target) continue;
                    if (!target.FORMIDS) {
                        target.FORMIDS = [];
                        target.FORMNAMES = [];
                    }
                    target.FORMIDS.push(form.sf_id);
                    target.FORMNAMES.push(form.sf_form_name);
                }
            }

            for (let route of routes) {
                if (route.sites == null) continue;
                if (route.sites == '') continue;
                let bids = route.sites.split(',');
                let routeSites = bids;
                for (let site of routeSites) {
                    let target = sites.find(x => x.SITEID == site)
                    if (!target) continue;
                    if (!target.ROUTEIDS) {
                        target.ROUTEIDS = [];
                        target.ROUTENAMES = [];
                    }
                    target.ROUTEIDS.push(route.r_id);
                    target.ROUTENAMES.push(route.r_name);
                }
            }


            sites = sites.map(x => {
                x.TRADES = x.TRADES ? x.TRADES.split(',') : [];
                if (x.contractors != null) {
                    let con = x.contractors.split(',');
                    for (let i = 0; i < con.length; i++) {
                        let target = contractors.find(y => y.sf_contact_id == con[i])
                        if (!target) continue;
                        if (!x.CONTRACTORIDS) {
                            x.CONTRACTORIDS = [];
                            x.CONTRACTORNAMES = [];
                        }
                        x.CONTRACTORIDS.push(target.sf_contact_id);
                        if (target.sf_contact_company_name)
                            x.CONTRACTORNAMES.push(target.sf_contact_company_name);
                        else
                            x.CONTRACTORNAMES.push(target.sf_contact_fname + ' ' + target.sf_contact_lname);
                    }
                }
                if (!x.CONTRACTORIDS) {
                    x.CONTRACTORIDS = [];
                    x.CONTRACTORNAMES = [];
                }
                if (!x.ROUTEIDS) {
                    x.ROUTEIDS = [];
                    x.ROUTENAMES = [];
                }
                if (!x.FORMIDS) {
                    x.FORMIDS = [];
                    x.FORMNAMES = [];
                }
                return x;
            })




            redisClient.set(`GET_SITES:${id}`, JSON.stringify(sites), { 'EX': 60 * 5 })
        }



        let uniqueStates = uniqBy(sites, 'STATENAME').filter(a => a.STATENAME).sort(function (a, b) {
            return a.STATENAME.localeCompare(b.STATENAME)
        }).map(a => { return { value: a.STATEID, State: a.STATENAME } });
        let uniqueSystems = uniqBy(sites, 'EXTERNALPROVIDER').filter(a => a.EXTERNALPROVIDER).sort(function (a, b) {
            return a.EXTERNALPROVIDER.localeCompare(b.EXTERNALPROVIDER)
        }).map(a => { return { value: a.EXTERNALPROVIDER, System: a.EXTERNALPROVIDER } });
        let uniqueZones = uniqBy(sites, 'ZONED').filter(a => a.ZONED).sort(function (a, b) {
            return a.ZONED.localeCompare(b.ZONED)
        }).map(a => { return { value: a.ZONED, Zone: a.ZONED } });

        let uniqueTrades = chain(sites).map('TRADES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        }).map(a => { return { value: a, Trade: a } });

        let uniqueContractors = chain(sites).map('CONTRACTORNAMES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        }).map(a => { return { value: a, Contractor: a } });

        let uniqueClients = uniqBy(sites, 'CLIENTNAME').filter(a => a.CLIENTNAME).sort(function (a, b) {
            return a.CLIENTNAME.localeCompare(b.CLIENTNAME)
        }).map(a => { return { value: a.CLIENTID, Client: a.CLIENTNAME } });

        let uniqueRoutes = chain(sites).map('ROUTENAMES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        }).map(a => { return { value: a, Route: a } });

        let uniqueForms = chain(sites).map('FORMNAMES').flatten().uniq().value().sort(function (a, b) {
            return a.localeCompare(b)
        }).map(a => { return { value: a, Form: a } });

        const { sortBy, sortDesc, page, itemsPerPage } = req.body.options;
        const total = sites.length
        if (sortBy.length === 1 && sortDesc.length === 1) {
            sites = sites.sort((a, b) => {
                const sortA = a[sortBy[0]]
                const sortB = b[sortBy[0]]

                if (sortDesc[0]) {
                    if (sortA < sortB) return 1
                    if (sortA > sortB) return -1
                    return 0
                } else {
                    if (sortA < sortB) return -1
                    if (sortA > sortB) return 1
                    return 0
                }
            })
        }
        let siteFilters = req.body.filters;
        if (siteFilters.States)
            if (siteFilters.States.length > 0) {

                sites = sites.filter(item => item.STATENAME ? siteFilters.States.includes(item.STATENAME) : false);
            }

        if (siteFilters.Zones)
            if (siteFilters.Zones.length > 0) {

                sites = sites.filter(item => item.ZONED ? siteFilters.Zones.includes(item.ZONED) : false);
            }
        if (siteFilters.Systems)
            if (siteFilters.Systems.length > 0) {

                sites = sites.filter(item => item.EXTERNALPROVIDER ? siteFilters.Systems.includes(item.EXTERNALPROVIDER) : false);
            }

        if (siteFilters.Trades)
            if (siteFilters.Trades.length > 0) {
                sites = sites.filter(item => item.TRADES ? item.TRADES.some(r => siteFilters.Trades.includes(r)) : false);
            }
        if (siteFilters.Own) {
            if (siteFilters.Own.length > 0) {
                let temp = [];
                if (siteFilters.Own.includes('Self'))
                    temp.push('1')
                if (siteFilters.Own.includes('Contracted'))
                    temp.push('0')
                sites = sites.filter(item => item.OWN ? temp.includes(item.OWN) : false);
            }

        }
        if (siteFilters.Contractors)
            if (siteFilters.Contractors.length > 0) {
                sites = sites.filter(item => item.CONTRACTORNAMES ? siteFilters.Contractors.some(r => item.CONTRACTORNAMES.includes(r)) : false);
            }

        if (siteFilters.Clients)
            if (siteFilters.Clients.length > 0) {
                sites = sites.filter(item => item.CLIENTNAME ? siteFilters.Clients.includes(item.CLIENTNAME) : false);
            }

        if (siteFilters.Routes)
            if (siteFilters.Routes.length > 0) {
                sites = sites.filter(item => item.ROUTENAMES ? item.ROUTENAMES.some(r => siteFilters.Routes.includes(r)) : false);
            }

        if (siteFilters.Forms)
            if (siteFilters.Forms.length > 0) {
                sites = sites.filter(item => item.FORMNAMES ? item.FORMNAMES.some(r => siteFilters.Forms.includes(r)) : false);
            }
        if (siteFilters.SiteID)
            if (siteFilters.SiteID.length > 0)
                sites = sites.filter(item => item.SITEID ? item.SITEID.toString().includes(siteFilters.SiteID.toString()) : false);

        if (siteFilters.SiteName)
            if (siteFilters.SiteName.length > 0)
                sites = sites.filter(item => item.SITENAME ? item.SITENAME.toString().toLowerCase().includes(siteFilters.SiteName.toString().toLowerCase()) : false);
        if (siteFilters.QuickbooksCustomer)
            if (siteFilters.QuickbooksCustomer.length > 0)
                sites = sites.filter(item => item.QUICKBOOKSCUSTOMER ? item.QUICKBOOKSCUSTOMER.toString().toLowerCase().includes(siteFilters.QuickbooksCustomer.toString().toLowerCase()) : false);

        if (siteFilters.Address1)
            if (siteFilters.Address1.length > 0)
                sites = sites.filter(item => item.ADDRESS1 ? item.ADDRESS1.toString().toLowerCase().includes(siteFilters.Address1.toString().toLowerCase()) : false);

        if (siteFilters.City)
            if (siteFilters.City.length > 0)
                sites = sites.filter(item => item.CITYNAME ? item.CITYNAME.toString().toLowerCase().includes(siteFilters.City.toString().toLowerCase()) : false);

        if (siteFilters.Zip)
            if (siteFilters.Zip.length > 0)
                sites = sites.filter(item => item.ZIPCODE ? item.ZIPCODE.toString().toLowerCase().includes(siteFilters.Zip.toString().toLowerCase()) : false);

        if (siteFilters.ProviderID)
            if (siteFilters.ProviderID.length > 0)
                sites = sites.filter(item => item.EXTERNALPROVIDERID ? item.EXTERNALPROVIDERID.toString().toLowerCase().includes(siteFilters.ProviderID.toString().toLowerCase()) : false);



        if (itemsPerPage > 0) {
            sites = sites.slice((page - 1) * itemsPerPage, page * itemsPerPage)
        }



        res.status(200).send({ sites, total, uniqueStates, uniqueTrades, uniqueContractors, uniqueClients, uniqueRoutes, uniqueForms, uniqueZones, uniqueSystems });
    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
}

const bulkUpdate = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);

        for (const site of req.body.data) {
            let siteID = site.siteID;
            let contractors = site.contractors;
            if (site.overwriteContractors == false) {
                let existingSiteContractors = await awaitQuery(`select mb_contractor from maptile_building where mb_user_id = ? and mb_id = ?`, [vendorId, siteID]);
                if (existingSiteContractors.length > 0) {
                    let siteContractors = JSON.parse(existingSiteContractors[0].mb_contractor)
                    contractors = [...contractors, ...siteContractors];
                    //remove duplicates
                    contractors = [...new Set(contractors)];
                }

            }

            await Sites.modifySite(vendorId, siteID, { contractors: contractors, trades: site.trades, client: site.client, zone: site.zone, manager: site.manager });
            if (site.forms.length > 0) {
                Form.bulkAssignForms(vendorId, site.forms, siteID);
            }

        }
        res.status(200).send({ message: 'Sites updated successfully' });
    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
}

const disconnectSite = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const siteID = req.query.id;
        res.status(200).send(await Sites.disconnectSite(vendorId, siteID));
    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
}
const getSitebyID = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const siteID = req.query.id;
        const site = await awaitQuery(`Select mb_id, mb_nickname, IF(mb_user_id='?', '1', '0') AS mb_own, mb_locked,mb_external_id, mb_external_src, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code,mb_geo,mb_lat,mb_long, (select City from maptile_city where CityId = mb_city) as cityname, (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client) as customername, (select state_abv_name from maptile_state where id = mb_state ) as statename, mb_notes, mb_maps, mb_ohours, mb_global_customer_location_id, mb_manager, mb_contractor, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone, mb_zone_id, mb_client,mb_contract_type,mb_vendor_internal_id,mb_hybrid From maptile_building where mb_id = ?`, [vendorId, siteID]);
        res.status(200).send(site[0]);
    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
}

const bulkUploadSites = async (req, res) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const sites = req.body
        if (!Array.isArray(sites)) {
            return res.status(400).json({ error: 'Invalid request data. Expected an array of sites.' });
        }

        // Immediately return a response to the frontend
        res.status(202).json({ message: 'Site creation process has started.' });


        // Process sites in the background
        const sitesLength = sites.length;
        let successfulCreations = 0;
        let failedCreations = 0;

        for (let i = 0; i < sitesLength; i++) {
            const site = sites[i];
            const isLastSite = i === sitesLength - 1;
            const updatedSite = { ...site, clusterSites: isLastSite };

            try {
                await Sites.createSite(vendorId, updatedSite);
                successfulCreations++;
            } catch (error) {
                failedCreations++;
                console.error(`Error during site ${i + 1} creation:`, error, site);
            }
        }

        console.log(`Bulk upload completed. ${successfulCreations} sites created successfully, ${failedCreations} failed.`);
    } catch (error) {
        console.error('Unexpected error during bulk site creation:', error);
    }
};


const bulkUploadSiteAndForm = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);

        const { siteFormList, formName } = req.body;

        // Create the site
        let siteIDFormIDs = [];
        for (const siteForm of siteFormList) {
            try {
                const site = siteForm.site;
                const siteId = await Sites.createSite(vendorId, site);
                if (siteId.error == true) continue;
                // Create the form
                const params = {
                    FormName: formName + ' - ' + site.name,
                    FormStatus: 1,
                    FormData: '',
                    AppData: JSON.stringify(siteForm.formJSON),
                    Sites: JSON.stringify([siteId.toString()]),
                    Contacts: '["-1"]',
                    CheckOut: 1,
                    Emails: '',
                    SendAppUser: 0,
                    ownform: 1
                }
                const formId = await Form.saveForm(vendorId, params);
                siteIDFormIDs.push({ 'siteId' :siteId, 'formId': formId });
            } catch (error) {
                console.error('Error creating site => ', error.stack);
            }
        }
        res.status(200).send(siteIDFormIDs);
    } catch (error) {
        next(error);
    }
}

const bulkUploadSiteLeads = async (req, res) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const sites = req.body
        if (!Array.isArray(sites)) {
            return res.status(400).json({ error: 'Invalid request data. Expected an array of sites.' });
        }

        // Immediately return a response to the frontend
        res.status(202).json({ message: 'Site creation process has started.' });

        //We need to get source and building status for this user.
        const sourceTypes = await awaitSafeQuery(`select * from sitefotos_site_source_types ssst where ssst_vendor_id = ? and ssst_active = 1`, [vendorId]);
        const statusTypes = await awaitSafeQuery(`select * from sitefotos_site_building_statuses ssbs where ssbs_vendor_id = ? and ssbs_active = 1`, [vendorId]);

        // Process sites in the background
        const sitesLength = sites.length;
        let successfulCreations = 0;
        let failedCreations = 0;

        for (let i = 0; i < sitesLength; i++) {
            const site = sites[i];
            delete site['mb_status'];
            site.mb_status = '3';
            site.mb_source = sourceTypes.length > 0 ? sourceTypes[0].ssst_id : 1;
            site.mb_building_status = statusTypes.length > 0 ? statusTypes[0].ssbs_id : 1; //Defaulting to Made contact
            const isLastSite = i === sitesLength - 1;
            const updatedSite = { ...site, clusterSites: isLastSite };

            try {
                await Sites.createSite(vendorId, updatedSite);
                successfulCreations++;
            } catch (error) {
                failedCreations++;
                console.error(`Error during site ${i + 1} creation:`, error, site);
            }
        }

        console.log(`Bulk upload completed. ${successfulCreations} sites created successfully, ${failedCreations} failed.`);
    } catch (error) {
        console.error('Unexpected error during bulk site creation:', error);
    }
};

const createSite = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT);
        const site = req.body;

        let resp = await Sites.createSite(vendorId, site)
        if (resp.error) {
            res.status(200).send(resp.message);
        }
        else {
            res.status(200).send(resp.toString());
        }
    } catch (ex) {
        console.log(ex.message ?? ex);
        res.status(200).send('0');
    }
}

const copySiteResources = async (req, res, next) => {
    try {
        const accessCode = req.body.accessCode;
        const { vendorId, internalUid } = await login(req.cookies.JWT, accessCode);

        const sourceSiteID = req.body.sourcebid;
        const destinationSiteID = req.body.destinationbid;
        let response = await Sites.copySiteResources(vendorId, sourceSiteID, destinationSiteID, true);
        if (response) {
            res.status(200).json({ "message": "Success" });
        } else {
            res.status(400).json({ "message": 'Unable to copy resources' });
        }
    } catch (ex) {
        console.log(ex);
        res.status(400).json({ "message": ex.message ?? ex });
    }
}

const updateSiteLeadsStatus = async (req,res,next) => {
    try {
        const {vendorId, internalUid} = await login(req.cookies.JWT);
        const buildingStatus = req.body.buildingStatus;
        const buildingId = req.body.buildingId;
        await updateObj("maptile_building", {mb_building_status: buildingStatus}, ["mb_user_id", "mb_id"], [vendorId,buildingId]);
        res.json("1")
    } catch (ex){
        res.status(400).json({ message: ex.toString() });
    }
}

const getPricingContracts = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const mb_id = req.params.site_id;
        const pricingContracts = await awaitSafeQuery(
          `select * , mb_nickname as mb_name, st_trade as trade
            from sitefotos_pricing_contracts
            inner join maptile_building on mb_id = spc_site_id
            inner join sitefotos_trades on st_id = spc_st_trade_id
            where spc_site_id = ? and spc_vendor_id = ? and spc_contract_active=1`,
          [mb_id, vendorId]
        )
        res.json(pricingContracts)
    } catch (ex){
        next(ex)
    }
}

const assignContractors = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const mb_id = req.body.mb_id;
        let contractors = req.body.contractors;
        //remove null values from contractors
        contractors = contractors.filter(function (el) {
            return el != null;
        });
        let contractorVids = []
        if (contractors) {
            if(typeof contractors == 'string') {
                try {
                    contractors = JSON.parse(contractors)
                } catch (e) {
                    contractors = contractors.split(',')
                }
            }
            if (!Array.isArray(contractors))  {
                throw new Error("Contractors when specified need to be an array")
            }
            if (contractors.length > 0) {
                //NOTE: Making a single loop by fetching all contractor in one request from DB using in operator.
                let contractorResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id in (?) AND sf_contact_vendorid=?`, [contractors, vendorId])
                if (contractorResults || contractorResults.length !== 0) {
                    for (let contractorResult of contractorResults) {
                        const cAccessCode = contractorResult['sf_contact_contractorid']
                        const cVendor = await validateVendorAccessCode(cAccessCode)
                        if (cVendor['userType'] === UserPrivilege.InvalidAccessCode)
                            continue
                        else
                            contractorVids.push(cVendor['vendorId'])
                    }
                } else {
                    contractors = [];
                    contractorVids = [];
                }
            }

            contractors = contractors.length > 0 ? JSON.stringify(contractors.map(a => a.toString())) : '[]'
            contractorVids = contractorVids.length > 0 ? JSON.stringify(contractorVids.map(a => a.toString())) : '[]'
        } else {
            contractors = '[]'
            contractorVids = '[]'
        }


        await updateObj(
          "maptile_building",
          {
              mb_contractor: contractors,
              mb_contractor_vid: contractorVids
          },
          ["mb_id"],
          [mb_id]
        )
        res.json("1")
    } catch (ex){
        next(ex)
    }
}

const getSources = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        await initUserSiteLeadStatus(vendorId);
        let results = await awaitSafeQuery(`select * from sitefotos_site_source_types where ssst_vendor_id=? and ssst_active = 1`, [vendorId]);
        res.json(results)
    } catch (ex){
        next(ex)
    }
}

const insertOrUpdateSource = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const { id, value } = req.body; // Assuming 'id' is the unique identifier in your table.

        //Check for value as duplicate in db.
        let existingRecord = await awaitSafeQuery( `SELECT * FROM sitefotos_site_source_types WHERE ssst_source=? AND ssst_vendor_id=?`, [value, vendorId]);
        if (existingRecord.length > 0 && existingRecord[0].ssst_id != id) {
            res.status(400).json({ message: 'Source already exists' });
            return
        }
        // Check if a record with the given 'id' already exists.
        existingRecord = await awaitSafeQuery(`SELECT * FROM sitefotos_site_source_types WHERE ssst_id=? AND ssst_vendor_id=?`, [id, vendorId]);

        if (existingRecord.length > 0) {
            // Update the existing record.
            await updateObj('sitefotos_site_source_types', {ssst_source: value}, ['ssst_id'], [id]);
            let updatedRecord = await awaitSafeQuery(`SELECT * FROM sitefotos_site_source_types WHERE ssst_id=?`, [id]);
            if (updatedRecord.length > 0) {
                res.status(201).json(updatedRecord[0]);
            } else {
                res.status(400).json({ message: 'Failed to update source' });
            }
        } else {
            // Insert a new record.
            const insertResult = await awaitSafeQuery(`INSERT INTO sitefotos_site_source_types (ssst_vendor_id, ssst_source) VALUES (?, ?)`, [vendorId, value]);

            if (insertResult.affectedRows > 0) {
                res.status(201).json((await awaitSafeQuery(`SELECT * FROM sitefotos_site_source_types WHERE ssst_id=?`, [insertResult.insertId]))[0]);
            } else {
                res.status(400).json({ message: 'Failed to insert source' });
            }
        }
    } catch (ex) {
        next(ex)
    }
};

const deleteSource = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const idToDelete = req.params.id; // Assuming 'id' is the identifier for the record to delete.
        const deleteResult = await awaitSafeQuery(`Update sitefotos_site_source_types set ssst_active = 0 WHERE ssst_id=? AND ssst_vendor_id=?`,[idToDelete, vendorId]);
        if (deleteResult.affectedRows > 0) {
            res.status(204).send(); // Respond with a 204 No Content status for successful deletion.
        } else {
            res.status(400).json({ message: 'Failed to delete source' });
        }
    } catch (ex) {
        next(ex)
    }
};

const getBuildingStatuses = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        await initUserSiteLeadStatus(vendorId);
        let results = await awaitSafeQuery(`SELECT * FROM sitefotos_site_building_statuses WHERE ssbs_vendor_id=? and ssbs_active = 1`,[vendorId]);
        res.json(results);
    } catch (ex) {
        next(ex)
    }
};

const createOrUpdateBuildingStatus = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const { id, status } = req.body;

        //Check existing status name.
        let existingRecord = await awaitSafeQuery( `SELECT * FROM sitefotos_site_building_statuses WHERE ssbs_status=? AND ssbs_vendor_id=?`, [status, vendorId]);
        if (existingRecord.length > 0 && existingRecord[0].ssbs_id != id) {
            res.status(400).json({ message: 'Status already exists' });
            return
        }
        // Check if a record with the given ID exists for the current vendorId.
        existingRecord = await awaitSafeQuery(`SELECT * FROM sitefotos_site_building_statuses WHERE ssbs_id=? AND ssbs_vendor_id=?`,[id, vendorId]);

        if (existingRecord.length > 0) {
            // Update the existing record.
            await updateObj('sitefotos_site_building_statuses', {ssbs_status: status}, ['ssbs_id', 'ssbs_vendor_id'], [id, vendorId]);
            let updatedRecord = await awaitSafeQuery(`SELECT * FROM sitefotos_site_building_statuses WHERE ssbs_id=? AND ssbs_vendor_id=?`, [id, vendorId]);
            if (updatedRecord.length > 0) {
                res.status(200).json(updatedRecord[0]);
            } else {
                res.status(400).json({ message: 'Failed to update building status' });
            }
        } else {
            // Create a new record.
            const insertResult = await awaitSafeQuery(`INSERT INTO sitefotos_site_building_statuses (ssbs_status, ssbs_vendor_id) VALUES (?, ?)`,[status, vendorId]);

            if (insertResult.affectedRows > 0) {
                res.status(201).json((await awaitSafeQuery(`SELECT * FROM sitefotos_site_building_statuses WHERE ssbs_id=? AND ssbs_vendor_id=?`, [insertResult.insertId, vendorId]))[0]);
            } else {
                res.status(400).json({ message: 'Failed to create building status' });
            }
        }
    } catch (ex) {
        next(ex)
    }
};

const deleteBuildingStatus = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const idToDelete = req.params.id;

        const deleteResult = await awaitSafeQuery(`Update sitefotos_site_building_statuses set ssbs_active = 0 WHERE ssbs_id=? AND ssbs_vendor_id=?`, [idToDelete, vendorId]);

        if (deleteResult.affectedRows > 0) {
            res.status(204).send(); // Respond with a 204 No Content status for successful deletion.
        } else {
            res.status(400).json({ message: 'Failed to delete building status' });
        }
    } catch (ex) {
        next(ex)
    }
};
const updateSitesChargify = async (req, res, next) => {
    try {
        // called from php no auth
        const vendoId = req.params.vendor_id;
        updateVendorSitesChargify(vendoId);
        res.status(200).send("1");
    } catch (ex) {
        next(ex)
    }
};
// Update building status
const updateMbStatus = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const { buildingId, newStatus } = req.body; // Assuming you pass buildingId and newStatus in the request body

        const result = await Sites.updateMbStatus(vendorId, buildingId, newStatus);
        if (result === true) {
            res.json({ message: 'Building status updated' });
        } else {
            return res.status(400).json({ message: result });
        }
    } catch (ex) {
        next(ex);
    }
};

// Get unique zones by user
const getUniqueZonesByUser = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);

        res.json(await awaitSafeQuery('select * from sitefotos_geofence_zones where sgz_vendor_id = ? and sgz_active = 1', [vendorId]));

    } catch (ex) {
        next(ex)
    }
};

const deleteZone = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT); // Obtain vendorId from the JWT token
        const { zone } = req.body; // Assuming the zone to delete is sent in the request body

        if (!zone) {
            return res.status(400).json({ message: 'Zone is required' });
        }
        await updateObj('sitefotos_geofence_zones', {sgz_active: 0}, ['sgz_zone_id', 'sgz_vendor_id'], [zone, vendorId]);

        res.json({ message: 'Zone deleted, and associated records updated' });
    } catch (ex) {
        next(ex)
    }
};

const addUpdateZone = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT); // Obtain vendorId from the JWT token
        const { zone, newZone } = req.body; // Assuming the old zone and new zone are sent in the request body

        const query = 'select * from sitefotos_geofence_zones where sgz_vendor_id = ? and sgz_geo_zone = ? and sgz_active = 1';
        const result = await awaitSafeQuery(query, [vendorId, newZone]);
        if (result.length > 0 && result[0].sgz_zone_id != zone) {
            return res.status(400).json({ message: 'Zone already exists' });
        }

        let zoneId
        if (zone) {
            const updateValues = [zone, vendorId];
            const result = await updateObj('sitefotos_geofence_zones', {sgz_geo_zone: newZone}, ['sgz_zone_id', 'sgz_vendor_id'], updateValues);
            if (result.affectedRows === 0) {
                return res.status(404).json({ message: 'Zone not found' });
            }
            zoneId = zone;
        } else {
            //Create zone using insertObj
            const insertValues = [vendorId, newZone];
            const result = await insertObj('sitefotos_geofence_zones', {'sgz_vendor_id': vendorId, 'sgz_geo_zone': newZone});
            zoneId = result.insertId;
        }

        res.json((await awaitSafeQuery('select * from sitefotos_geofence_zones where sgz_zone_id = ? and sgz_vendor_id = ?', [zoneId, vendorId]))[0]);
    } catch (ex) {
        next(ex);
    }
};

const initUserSiteLeadStatus = async (vendorId) => {
        //check if this user has any status in redis so main db does not get hit every time user loads site leads
        const cache = await redisClient.get(`GET_USER_SITELEAD_STATUS:${vendorId}`);
        if (cache) return;
        const createSiteLeadStatus = async () => {
            const cache = await redisClient.get(`GET_USER_SITELEAD_STATUS:${vendorId}`);
            if (cache) return;
            //check if this user has any status
            const statusCount = await awaitSafeQuery(`select count(*) as countt from sitefotos_site_building_statuses WHERE ssbs_vendor_id = ?`, [vendorId]);
            if (statusCount[0].countt == 0){
                const insertQuery = `
                INSERT INTO sitefotos_site_building_statuses (ssbs_status, ssbs_vendor_id)
                SELECT ssbs_status, ? AS ssbs_vendor_id
                FROM sitefotos_site_building_statuses
                WHERE ssbs_vendor_id = ?;
                `;

                await awaitSafeQuery(insertQuery, [vendorId, 0]);
            }
            // Check if this user has any source types
            const sourceTypeCount = await awaitSafeQuery('SELECT COUNT(*) AS countt FROM sitefotos_site_source_types WHERE ssst_vendor_id = ?', [vendorId]);

            if (sourceTypeCount[0].countt === 0) {
                const insertQuery = `
                    INSERT INTO sitefotos_site_source_types (ssst_source, ssst_vendor_id, ssst_active)
                    SELECT ssst_source, ? AS ssst_vendor_id, ssst_active
                    FROM sitefotos_site_source_types
                    WHERE ssst_vendor_id = ?;
                `;

                await awaitSafeQuery(insertQuery, [vendorId, 0]);
            }
            await redisClient.set(`GET_USER_SITELEAD_STATUS:${vendorId}`, "true", {EX: 60 * 60 * 24});
        }
        await common.executeSyncronouslySystemWide(`CREATE_SITE_LEAD_STATUS:${vendorId}`, createSiteLeadStatus, [], 300000, 5000);
}



const getClusters = async (req, res, next) => {
    try {
        const vid = req.params.vendor_id;
        if (isNaN(vid)) {
            return res.send([]);
        }


        const cacheKey = `getClusters${vid}`;
        const cachedData = await redisClient.get(cacheKey);

        if (cachedData) {
            const { data, lastCacheTime } = JSON.parse(cachedData);

            const [latestUpdate] = await awaitQuery("SELECT MAX(mb_last_modified) as lastModified FROM maptile_building where (mb_user_id=? OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE(?))) and mb_status = '1'", [vid,vid]);

            if (new Date(latestUpdate.lastModified).getTime() <= lastCacheTime) {
                return res.send(data);
            }
        }

        let buildings = await awaitQuery("SELECT mb_id, mb_lat, mb_long,mb_user_id,mb_last_modified from maptile_building where (mb_user_id=? OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE(?))) and mb_status = '1'", [vid, vid]);
        let clusterData = [];
        if (buildings.length > 0) {
            let bcount = buildings.length;
            let clusters = Math.ceil(bcount / 50);

            if (bcount < 50 && bcount > 2) {
                clusters = (bcount < 20) ? 2 : 3;
            }
            const points = buildings.map(b => {
                const lat = parseFloat(b.mb_lat);
                const long = parseFloat(b.mb_long);

                if (!isNaN(lat) && !isNaN(long)) {
                    return turf.point([long, lat], { mb_id: b.mb_id });
                } else {
                    console.log(`Skipping invalid coordinates for building ID ${b.mb_id}: lat=${b.mb_lat}, long=${b.mb_long}`);
                    return null;
                }
            }).filter(point => point !== null);

            const fc = turf.featureCollection(points);


            const clustered = turf.clustersKmeans(fc, { numberOfClusters: clusters });



            for (let i = 0; i < clusters; i++) {

                const clusterPoints = clustered.features.filter(f => f.properties.cluster === i);
                if(clusterPoints.length === 0) continue;
                const centroid = turf.centroid(turf.featureCollection(clusterPoints));
                clusterData.push({
                    sgc_vendor_id: vid,
                    sgc_cluster: i.toString(),
                    sgc_centroid: JSON.stringify([centroid.geometry.coordinates[1], centroid.geometry.coordinates[0]]),
                    sgc_numbuildings: clusterPoints.length.toString(),
                    sgc_buildings: clusterPoints.map(p => p.properties.mb_id).join(',')
                });
            }
        }

        const cacheValue = JSON.stringify({ data: clusterData, lastCacheTime: Date.now() });
        await redisClient.set(cacheKey, cacheValue);

        res.send(clusterData);
    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
}

const getClustersOld = async (req, res, next) => {
    try {
        const vid = req.params.vendor_id;
        //check if vid is number
        if (isNaN(vid)) {
            return res.send([]);
        }
        let buildings = await awaitCachedQuery("SELECT mb_id, mb_lat, mb_long,mb_user_id,mb_last_modified from maptile_building where (mb_user_id=? OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE(?))) and mb_status = '1'", [vid, vid], {cacheTimeOut: 180});
        if (buildings.length > 0) {
            let bcount = buildings.length;
            let clusters = Math.ceil(bcount / 50);

            if (bcount < 50 && bcount > 2) {
                clusters = (bcount < 20) ? 2 : 3;
            }
            const points = buildings.map(b => {
                const lat = parseFloat(b.mb_lat);
                const long = parseFloat(b.mb_long);

                if (!isNaN(lat) && !isNaN(long)) {
                    return turf.point([long, lat], { mb_id: b.mb_id });
                } else {
                    console.log(`Skipping invalid coordinates for building ID ${b.mb_id}: lat=${b.mb_lat}, long=${b.mb_long}`);
                    return null;
                }
            }).filter(point => point !== null);

            const fc = turf.featureCollection(points);


            const clustered = turf.clustersKmeans(fc, { numberOfClusters: clusters });


            let clusterData = [];
            for (let i = 0; i < clusters; i++) {

                const clusterPoints = clustered.features.filter(f => f.properties.cluster === i);
                if(clusterPoints.length === 0) continue;
                const centroid = turf.centroid(turf.featureCollection(clusterPoints));
                clusterData.push({
                    sgc_vendor_id: vid,
                    sgc_cluster: i.toString(),
                    sgc_centroid: JSON.stringify([centroid.geometry.coordinates[1], centroid.geometry.coordinates[0]]),
                    sgc_numbuildings: clusterPoints.length.toString(),
                    sgc_buildings: clusterPoints.map(p => p.properties.mb_id).join(',')
                });
            }
            res.send(clusterData);
        }
        else {
            return res.send([]);
        }

    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
}
const getClusters2 = async (req, res, next) => {
    try {
        const vid = req.params.vendor_id;
        let rows = await fleetdb.awaitCachedQuery("SELECT count(sfb_building_id) as count from sitefotos_buildings where (sfb_vid=$1 OR sfb_cvid like $2) and sfb_active=1", [vid, `%${vid}%`]);
        if (rows.length > 0) {
            let bcount = rows[0].count;
            let clusters = Math.ceil(bcount / 50);

            if (bcount < 50 && bcount > 2) {
                clusters = (bcount < 20) ? 2 : 3;
            }

            rows = await fleetdb.awaitCachedQuery("SELECT $1 as sgc_vendor_id, cid as sgc_cluster, ST_AsText(ST_Centroid(ST_FlipCoordinates(ST_Collect(sfb_building_geo)))) AS sgc_centroid, COUNT(sfb_building_id) as sgc_numbuildings, array_agg(sfb_building_id) AS sgc_buildings FROM (SELECT ST_ClusterKMeans(sfb_building_geo, $2) OVER() AS cid, sfb_building_id, sfb_building_geo FROM sitefotos_buildings where (sfb_vid=$3 or sfb_cvid like $4) and sfb_active=1 and ST_IsValid(sfb_building_geo)) sq GROUP BY cid", [vid, clusters, vid, `%${vid}%`]);

            const clusterArray = rows.map(row => {
                return {
                    ...row,
                    sgc_centroid: row.sgc_centroid.substring(5).replace('(', '[').replace(')', ']').replace(' ', ','),
                    sgc_buildings: row.sgc_buildings.join(','),
                    sgc_numbuildings: String(row.sgc_numbuildings),
                    sgc_cluster: String(row.sgc_cluster)
                };
            });

            res.json(clusterArray);
        } else {
            res.json([]);
        }
    } catch (ex) {
        console.log(ex.message ?? ex);
        next(ex);
    }
};

const getBuildingInfo= async (req, res, next) => {
    try {
        const bid = req.params.bid;

        const qry = `
            SELECT mb_id, mb_nickname, mb_address1, mb_zip_code, mb_geo,
            (SELECT City FROM maptile_city WHERE CityId = mb_city) AS cityname,
            (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) AS statename,
            mb_notes, mb_maps, mb_ohours, mb_external_src, mb_external_id, mb_manager,
            mb_contractor, (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) AS mb_zone,
            mb_client, mb_sf, mb_lat, mb_long, mb_min_zoom, mb_contract_type, mb_quickbooks_customer_id,
            mb_sc_pin, mb_sc_store_id, mb_client_secondary, mb_client_third, mb_manager_secondary,
            mb_manager_third, mb_source, mb_building_status, mb_country, mb_kind, mb_zone_id, mb_radius, mb_timezone
            FROM maptile_building
            WHERE mb_id = ?
        `;
        const buildings = await awaitQuery(qry, [bid]);

        if (buildings.length > 0 && buildings[0].mb_id > 0) {

            buildings[0].mb_geo = unserialize(buildings[0].mb_geo);

            const qry2 = `
                SELECT vendor_email FROM maptile_vendors
                JOIN maptile_building_share ON mbs_user = vendor_id
                WHERE mbs_building = ?
            `;
            const vendorEmails = await awaitQuery(qry2, [bid]);
            const emails = vendorEmails.map(v => v.vendor_email).join(',');

            res.json({
                buildingInfo: buildings[0],
                emails: emails
            });
        } else {
            res.json({ message: "No building found." });
        }
    } catch (error) {
        console.error('Error fetching building info:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};
const getNearestOld = async (req, res, next) => {
    try {
        const lat = req.query.lat;
        const lon = req.query.lon;
        let radius = req.query.radius;
        const vid = req.query.vid;

        radius = radius * 1000;
        const nearest = await fleetdb.awaitQuery(/*SQL*/`SELECT sfb_building_id FROM sitefotos_buildings WHERE ST_DWithin(ST_Transform(sfb_building_geo::geometry, 3857),ST_Transform(ST_SetSRID(ST_MakePoint($1, $2), 4326)::geometry, 3857), $3) AND sfb_vid= $4`,[lon, lat, radius, vid]);
        res.json(nearest.map(n => n.sfb_building_id));
    } catch (ex) {
        next(ex)
    }
}
const getNearest = async (req, res, next) => {
    try {
        const lat = req.query.lat;
        const lon = req.query.lon;
        let radius = req.query.radius;
        const vid = req.query.vid;

        const targetPoint = turf.point([lon, lat]);
        const circle = turf.circle(targetPoint, radius, {
            units: 'kilometers',
            steps: 64,
        });
        let buildings = await awaitCachedQuery("SELECT mb_id, mb_lat, mb_long,mb_user_id,mb_last_modified from maptile_building where (mb_user_id=? OR JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE(?))) and mb_status = '1'", [vid, vid]);
        if (!buildings || buildings.length === 0) return res.json([]);
        const points = buildings.map(b => {
            const lat = parseFloat(b.mb_lat);
            const long = parseFloat(b.mb_long);

            if (!isNaN(lat) && !isNaN(long)) {
                return turf.point([long, lat], { mb_id: b.mb_id });
            } else {
                console.log(`Skipping invalid coordinates for building ID ${b.mb_id}: lat=${b.mb_lat}, long=${b.mb_long}`);
                return null;
            }
        }).filter(point => point !== null);
        const pointsWithin = points.filter(point => turf.booleanPointInPolygon(point, circle));
        res.json(pointsWithin.map(n => n.properties.mb_id));

    } catch (ex) {
        next(ex)
    }
}
const getNearest2 = async (req, res, next) => {
    try {
        const lat = req.query.lat;
        const lon = req.query.lon;
        const vid = req.query.vid;

        const stmt = /*SQL*/`SELECT sfb_building_id, ST_Transform(sfb_building_geo, 3857) <-> ST_Transform(ST_SetSRID(ST_MakePoint($1, $2), 4326), 3857) AS dist FROM sitefotos_buildings WHERE sfb_vid=$3 AND sfb_active=1 ORDER BY dist LIMIT 5`;

        const nearest = await fleetdb.awaitQuery(stmt, [lon, lat, vid]);
        res.json(nearest.map(n => ({ sfb_building_id: n.sfb_building_id, dist: n.dist.toString() })));
    } catch (ex) {
        next(ex)
    }
};
const mergeSites = async (req, res, next) => {
    let connection;
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const { sourceSiteId, destinationSiteId } = req.body;

        connection = await getDirectConnection();
        await connection.beginTransaction();

        // Check if both sites exist and belong to vendor
        const [sourceSite] = await connection.execute(
            `SELECT * FROM maptile_building WHERE mb_id = ? AND mb_user_id = ?`,
            [sourceSiteId, vendorId]
        );
        const [destinationSite] = await connection.execute(
            `SELECT * FROM maptile_building WHERE mb_id = ? AND mb_user_id = ?`,
            [destinationSiteId, vendorId]
        );

        if(sourceSite.length === 0 || destinationSite.length === 0) {
            await connection.rollback();
            return res.json({success: false, message: "One or both sites not found"});
        }

        // Check if destination site has external system connection
        if(destinationSite[0].mb_external_src != null) {
            await connection.rollback();
            return res.json({success: false, message: "Destination site is connected to an external system"});
        }

        // Copy resources and update source site status
        await connection.execute(`CALL copy_site_resources(?, ?, 1)`, [sourceSiteId, destinationSiteId]);
        await connection.execute(
            `UPDATE maptile_building SET mb_status = '2' WHERE mb_id = ?`,
            [sourceSiteId]
        );

        await connection.commit();
        res.json({success: true, message: "Sites merged successfully"});

    } catch (ex) {
        if (connection) {
            await connection.rollback();
        }
        next(ex);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
};
const getBuildingTimes = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const { start, end } = req.query;
        if (!start || !end) {
            return res.status(400).json({ error: 'Start and end parameters are required' });
        }

        const existsResult = await awaitCachedQuery(`
            SELECT EXISTS (
                SELECT 1 FROM sitefotos_building_geofence_data WHERE sbgd_vid = ?
            ) AS record_exists`, [vendorId], {cacheTimeOut: 600});

        if (existsResult[0].record_exists==false) return res.json([]);

        const rows = await awaitQuery(`
            SELECT sbgd_building_id, sbgd_vehicle_id, sbgd_enter_time, sbgd_leave_time,
                   sbgd_provider_id, sv_vehicle_label, sv_vehicle_make, sv_vehicle_model,
                   sv_vehicle_licenceplate
            FROM sitefotos_building_geofence_data
            INNER JOIN sitefotos_vehicles
            ON sbgd_vehicle_id = sv_vehicle_id AND sbgd_vid = sv_vid
            WHERE sbgd_vid = ? 
            AND sbgd_enter_time BETWEEN ? AND ?
            AND sbgd_leave_time BETWEEN ? AND ?`, [vendorId, start, end, start, end]);

        res.json(rows);
    } catch (ex) {
        console.error(ex.message ?? ex);
        next(ex);
    }
};


exports.getClusters = getClusters;
exports.getSites = getSites;
exports.bulkUpdate = bulkUpdate;
exports.disconnectSite = disconnectSite;
exports.createSite = createSite;
exports.getSitebyID = getSitebyID;
exports.getBuildings = getBuildings;
exports.getBuildingsOnlySiteLeads = getBuildingsOnlySiteLeads;
exports.bulkUploadSites = bulkUploadSites;
exports.bulkUploadSiteLeads = bulkUploadSiteLeads;
exports.copySiteResources = copySiteResources;
exports.getSitesTabulator = getSitesTabulator;
exports.getClientModelData = getClientModelData;
exports.saveClientModelData =saveClientModelData
exports.getSiteLeadsTabulator = getSiteLeadsTabulator;
exports.updateSiteLeadsStatus = updateSiteLeadsStatus;
exports.getPricingContracts = getPricingContracts;
exports.assignContractors = assignContractors;
exports.getSources = getSources;
exports.insertOrUpdateSource = insertOrUpdateSource;
exports.deleteSource = deleteSource;
exports.getBuildingStatuses = getBuildingStatuses;
exports.createOrUpdateBuildingStatus = createOrUpdateBuildingStatus;
exports.deleteBuildingStatus = deleteBuildingStatus;
exports.updateMbStatus = updateMbStatus;
exports.getUniqueZonesByUser = getUniqueZonesByUser;
exports.deleteZone = deleteZone;
exports.addUpdateZone = addUpdateZone;
exports.updateSiteMaps = updateSiteMaps;
exports.getBuildingTimes = getBuildingTimes;
exports.getClientModelSiteMetadata = getClientModelSiteMetadata;
exports.getClusters2 = getClusters2;
exports.getNearest = getNearest;
exports.getNearest2 = getNearest2;
exports.getBuildingInfo = getBuildingInfo;
exports.getSitesContractors = getSitesContractors;
exports.updateSitesChargify = updateSitesChargify;
exports.getIssues = getIssues;
exports.getIssuesDashboard = getIssuesDashboard;
exports.getIssuesSite = getIssuesSite;
exports.groupIssues = groupIssues;
exports.ungroupIssues = ungroupIssues;
exports.removeIssueFromGroup = removeIssueFromGroup;
exports.addIssuesToGroup = addIssuesToGroup;
exports.updateIssue = updateIssue;
exports.updateGroup = updateGroup;
exports.createWorkorderIssue = createWorkorderIssue;
exports.bulkUploadSiteAndForm = bulkUploadSiteAndForm;
exports.mergeSites = mergeSites;
exports.getCpsSites = getCpsSites;
exports.updateCpsSite = updateCpsSite;
