const service = require('../services/services-trades-equipment.services');
const { login} = require('../utils/vendor');
const servicesService = require("../services/service.services");
const moment = require("moment/moment");
const {sendEmailTemplate} = require("../utils/email");
const Hashids = require('hashids/cjs')
const {awaitSafeQuery, insertObj, updateObj, awaitQuery, deleteObj, getDirectConnection} = require("../utils/db");
const fleetdb = require("../utils/fleetdb");
const hashids = new Hashids('1a3371ee-68b4-47dc-9786-3d57d264240f')
const baseURL = process.env.BASE_URL || 'http://localhost'
const { serialize, unserialize } = require('php-serialize');
const {addServiceHistoryAuditTrail} = require("../services/services-trades-equipment.services");





function parseServiceDetails(value) {
  if (value != null) {
    try {
      var co = unserialize(value);
      if (Array.isArray(co)) {
        return co.join(", ");
      } else {
        return co;
      }
    } catch (e) {

      return value;
    }
  }
  return null;
}


function findClosestSnowTrigger(array, snow) {
  if (!Array.isArray(array)) return undefined;
  return array.find(obj => {
    let start = obj.sps_snow_trigger_start;
    let end = obj.sps_snow_trigger_end;
    // Ensure snow, start, and end are numbers for comparison
    const numSnow = parseFloat(snow);
    const numStart = parseFloat(start);
    const numEnd = parseFloat(end);
    if (isNaN(numSnow) || isNaN(numStart) || isNaN(numEnd)) return false;
    return numSnow >= numStart && numSnow <= numEnd;
  });
}


function getPayRateFromContractorProfile(svc, duration, vendorId){
  // svc.ContractorPersonalProfilePricing is expected to be populated on the svc object
  if ( svc.ContractorPersonalProfilePricing && svc.ContractorPersonalProfilePricing.length > 0 ){
    let payRateFiltered =
      svc.ContractorPersonalProfilePricing
        .find( eR=> eR.scsp_trade_id === svc.TradeId
          && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id
          && eR.scsp_equipment_id === svc.equipment_id);

    if (payRateFiltered === undefined) {
      payRateFiltered = svc.ContractorPersonalProfilePricing
        .find( eR=> eR.scsp_trade_id === svc.TradeId && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id);
    }
    if (payRateFiltered === undefined) {
      payRateFiltered = svc.ContractorPersonalProfilePricing
        .find( eR=> eR.scsp_trade_id === svc.TradeId );
    }
    if (payRateFiltered === undefined) {
      payRateFiltered = svc.ContractorPersonalProfilePricing
        .find( eR=> eR.scsp_trade_id === 0 && eR.scsp_vendor_services_service_type_id === 0 && eR.scsp_equipment_id === 0);
    }

    if (payRateFiltered && payRateFiltered.scsp_price != null) { // Check if payRateFiltered and scsp_price are valid
      if ( (svc.Hours == null || svc.Hours < 1) && (svc.People == null || svc.People < 1) ) { // Handle nulls for Hours/People
        return payRateFiltered.scsp_price * duration.asHours();
      }
      else {
        const hours = svc.Hours || 0; // Default to 0 if null
        const people = svc.People || 1; // Default to 1 if null/0 for multiplication
        return payRateFiltered.scsp_price * hours * people;
      }
    }
  }
  return 0; // Default to 0 if no rate found
}


function findDuplicatesForEvents(arr, keysToGroupBy) {
  if (!Array.isArray(arr)) return;
  arr.sort((a, b) => b.UnixTime - a.UnixTime);
  const seen = {};

  arr.forEach(item => {
    if (item.ContractorPricingUnit === 'EVENT' || item.ClientPricingUnit === 'EVENT') {
      if (item.swsd_event != null && String(item.swsd_event).trim() !== '') {
        const itemKeys = keysToGroupBy.map(key => item[key]);
        const itemKeysString = JSON.stringify(itemKeys);

        if (seen[itemKeysString]) {
          // This is a subsequent item in the group for this page
          if (keysToGroupBy.includes("ClientPricingUnit")) item.ClientPricingUi = 0;
          if (keysToGroupBy.includes("ContractorPricingUnit")) item.ContractorPricingUi = 0;
          // No need to push to a 'duplicates' array like client, just modify in place
        } else {
          // This is the first item encountered for this group on this page
          seen[itemKeysString] = true; // Mark as seen
        }
      }
    }
  });
}

// Helper function to get filtered service IDs for cross-page operations
const getFilteredServiceIds = async (vendorId, restrictedUser, userContactId, filters, sorters, startDate, endDate) => {
  const fieldMap = {
    'Service': 'svc.swsd_service_name',
    'Status': 'svc.swsd_service_status',
    'Source': 'svc.swsd_service_source',
    'TradeTitle': 'st.st_trade',
    'Site': 'mb.mb_nickname',
    'site_id': 'mb.mb_id',
    'Zone': 'gz.sgz_geo_zone',
    'InternalManager': `CONCAT(ic.sf_contact_fname, ' ', ic.sf_contact_lname)`,
    'Notes': 'svc.swsd_notes',
    'WorkOrder': 'swos.swos_workorder_id',
    'ExternalWorkOrder': 'swo.swo_external_id',
    'ParentFormId': 'sfs.sfs_form_id',
    'Form': 'sfs.sfs_form_name',
    'People': 'svc.swsd_people',
    'Hours': 'svc.swsd_hours',
    'Start': 'sfs.sfs_checkin',
    'Stop': 'sfs.sfs_checkout',
    'snow_inch': 'svc.swsd_snow_inch',
    'swsd_event': 'svc.swsd_event',
    'ProviderText': `CONCAT(mv.vendor_fname, ' ', mv.vendor_lname)`,
    'Client': `icc.sf_contact_company_name`,
    'swsd_qb_invoice_id': 'svc.swsd_qb_invoice_id',
    'swsd_qb_bill_id': 'svc.swsd_qb_bill_id',
    'UnixTime': 'svc.swsd_date_time',
    'swsd_vendor_sequence_id': 'svc.swsd_vendor_sequence_id',
    'form_submission_id': 'sfs.sfs_id',
  };

  let baseQuery = `
    FROM sitefotos_wp_services_data svc
    LEFT JOIN maptile_building mb ON mb.mb_id = svc.swsd_site_id
    LEFT JOIN sitefotos_geofence_zones gz ON gz.sgz_zone_id = mb.mb_zone_id
    LEFT JOIN maptile_city city ON city.CityId = mb.mb_city
    LEFT JOIN maptile_state state ON state.id = mb.mb_state
    LEFT JOIN sitefotos_contacts ic ON ic.sf_contact_id = mb.mb_manager
    LEFT JOIN sitefotos_contacts_company_view icc ON icc.sf_contact_id = mb.mb_client
    LEFT JOIN maptile_vendors mv ON mv.vendor_id = svc.swsd_uploader_vid
    LEFT JOIN vendor_services vs ON vs.vs_service_id = svc.swsd_service_id
    LEFT JOIN sitefotos_trades st ON st.st_id = vs.vs_trade_id
    LEFT JOIN sitefotos_forms_submitted sfs ON sfs.sfs_id = svc.swsd_form_submission_reference
    LEFT JOIN sitefotos_work_orders_submissions swos ON swos.swos_form_submission_id = svc.swsd_form_submission_reference
    LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id
    LEFT JOIN (
      SELECT sf_contact_email, CONCAT(sf_contact_fname, ' ', sf_contact_lname) AS InternalContact
      FROM sitefotos_contacts
      WHERE sf_contact_vendorid = ?
      GROUP BY sf_contact_email
    ) ic2 ON ic2.sf_contact_email = svc.swsd_email
  `;

  let whereClauses = [
    `(svc.swsd_vendor_id = ? OR svc.swsd_uploader_vid = ?)`,
    `svc.swsd_date_time >= ?`,
    `svc.swsd_date_time < ?`,
    `svc.swsd_service_status != 'TEMPLOG'`
  ];

  let queryParams = [
    vendorId, // for ic2 subquery
    vendorId, // for main WHERE svc.swsd_vendor_id
    vendorId, // for main WHERE svc.swsd_uploader_vid
    startDate,
    endDate
  ];

  if (restrictedUser && userContactId > 0) {
    whereClauses.push(`(mb.mb_manager = ? OR mb.mb_manager_secondary = ? OR mb.mb_manager_third = ?)`);
    queryParams.push(userContactId, userContactId, userContactId);
  }

  // Apply filters
  if (filters && Array.isArray(filters)) {
    filters.forEach(filter => {
      const dbField = fieldMap[filter.field] || filter.field;
      if (!dbField) return;

      switch (filter.type) {
        case 'like':
          whereClauses.push(`${dbField} LIKE ?`);
          queryParams.push(`%${filter.value}%`);
          break;
        case '=':
          whereClauses.push(`${dbField} = ?`);
          queryParams.push(filter.value);
          break;
        case 'in':
        case 'function':
          if (Array.isArray(filter.value) && filter.value.length > 0) {
            const placeholders = filter.value.map(() => '?').join(',');
            whereClauses.push(`${dbField} IN (${placeholders})`);
            queryParams.push(...filter.value);
          } else if (!Array.isArray(filter.value) && filter.value) {
            whereClauses.push(`${dbField} = ?`);
            queryParams.push(filter.value);
          }
          break;
        case '>=':
          whereClauses.push(`${dbField} >= ?`);
          queryParams.push(filter.value);
          break;
        case '<=':
          whereClauses.push(`${dbField} <= ?`);
          queryParams.push(filter.value);
          break;
      }
    });
  }

  const sql = `SELECT DISTINCT svc.swsd_id ${baseQuery} WHERE ${whereClauses.join(' AND ')}`;
  const results = await awaitQuery(sql, queryParams);
  return results.map(row => row.swsd_id);
};

const getServiceHistoryDashboard2 = async (req, res, next) => {
    try {
      const { vendorId, restrictedUser, userContactId, token: jwtToken } = await login(req.cookies.JWT);
      const timeZone = req.body.tz || 'America/New_York';


      const page = parseInt(req.body.page) || 1;
      const size = parseInt(req.body.size) || 100;
      const sorters = req.body.sort ? (typeof req.body.sort === 'string' ? JSON.parse(req.body.sort) : req.body.sort) : [{ field: "UnixTime", dir: "desc" }];
      const filters = req.body.filter ? (typeof req.body.filter === 'string' ? JSON.parse(req.body.filter) : req.body.filter) : [];

      const offset = (page - 1) * size;

      const fieldMap = {
        'Service': 'svc.swsd_service_name',
        'Status': 'svc.swsd_service_status',
        'Source': 'svc.swsd_service_source',
        'TradeTitle': 'st.st_trade',
        'Site': 'mb.mb_nickname',
        'site_id': 'mb.mb_id',
        'Zone': 'gz.sgz_geo_zone',
        'InternalManager': `CONCAT(ic.sf_contact_fname, ' ', ic.sf_contact_lname)`,
        'Notes': 'svc.swsd_notes',
        'WorkOrder': 'swos.swos_workorder_id',
        'ExternalWorkOrder': 'swo.swo_external_id',
        'ParentFormId': 'sfs.sfs_form_id',
        'Form': 'sfs.sfs_form_name',
        'People': 'svc.swsd_people',
        'Hours': 'svc.swsd_hours',
        'Start': 'sfs.sfs_checkin', // CheckIn (Unix timestamp)
        'Stop': 'sfs.sfs_checkout', // CheckOut (Unix timestamp)
        'snow_inch': 'svc.swsd_snow_inch',
        'swsd_event': 'svc.swsd_event',
        'ProviderText': `CONCAT(mv.vendor_fname, ' ', mv.vendor_lname)`,
        'Client': `icc.sf_contact_company_name`,
        'swsd_qb_invoice_id': 'svc.swsd_qb_invoice_id',
        'swsd_qb_bill_id': 'svc.swsd_qb_bill_id',
        'UnixTime': 'svc.swsd_date_time',
        'swsd_vendor_sequence_id': 'svc.swsd_vendor_sequence_id',
        'form_submission_id': 'sfs.sfs_id',

      };

      let baseQuery = `
        FROM sitefotos_wp_services_data svc
        LEFT JOIN maptile_building mb ON mb.mb_id = svc.swsd_site_id
        LEFT JOIN sitefotos_geofence_zones gz ON gz.sgz_zone_id = mb.mb_zone_id
        LEFT JOIN maptile_city city ON city.CityId = mb.mb_city
        LEFT JOIN maptile_state state ON state.id = mb.mb_state
        LEFT JOIN sitefotos_contacts ic ON ic.sf_contact_id = mb.mb_manager
        LEFT JOIN sitefotos_contacts_company_view icc ON icc.sf_contact_id = mb.mb_client
        LEFT JOIN maptile_vendors mv ON mv.vendor_id = svc.swsd_uploader_vid
        LEFT JOIN vendor_services vs ON vs.vs_service_id = svc.swsd_service_id
        LEFT JOIN sitefotos_trades st ON st.st_id = vs.vs_trade_id
        LEFT JOIN sitefotos_forms_submitted sfs ON sfs.sfs_id = svc.swsd_form_submission_reference
        LEFT JOIN sitefotos_work_orders_submissions swos ON swos.swos_form_submission_id = svc.swsd_form_submission_reference
        LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id
        LEFT JOIN (
          SELECT swmd_parent_service_submission_id, MIN(swmd_mat_id) AS mat_id, swmd_mat_usage, swmd_mat_unit
          FROM sitefotos_wp_material_data
          GROUP BY swmd_parent_service_submission_id
        ) swmd ON swmd.swmd_parent_service_submission_id = svc.swsd_id
        LEFT JOIN (
          SELECT sf_contact_email, CONCAT(sf_contact_fname, ' ', sf_contact_lname) AS InternalContact
          FROM sitefotos_contacts
          WHERE sf_contact_vendorid = ?
          GROUP BY sf_contact_email
        ) ic2 ON ic2.sf_contact_email = svc.swsd_email
      `;

      let whereClauses = [
        `(svc.swsd_vendor_id = ? OR svc.swsd_uploader_vid = ?)`,
        `svc.swsd_date_time >= ?`,
        `svc.swsd_date_time < ?`,
        `svc.swsd_service_status != 'TEMPLOG'`
      ];

      let queryParams = [
        vendorId, // for ic2 subquery
        vendorId, // for main WHERE svc.swsd_vendor_id
        vendorId, // for main WHERE svc.swsd_uploader_vid
        req.body.start,
        req.body.end
      ];

      if (restrictedUser && userContactId > 0) {
        whereClauses.push(`(mb.mb_manager = ? OR mb.mb_manager_secondary = ? OR mb.mb_manager_third = ?)`);
        queryParams.push(userContactId, userContactId, userContactId);
      }

      filters.forEach(filter => {
        console.log(filter)
        const dbField = fieldMap[filter.field] || filter.field;
        if (!dbField) return;

        switch (filter.type) {
          case 'like':
            whereClauses.push(`${dbField} LIKE ?`);
            queryParams.push(`%${filter.value}%`);
            break;
          case '=':
            whereClauses.push(`${dbField} = ?`);
            queryParams.push(filter.value);
            break;
          case 'in':
          case 'function':
             if (Array.isArray(filter.value) && filter.value.length > 0) {
                const placeholders = filter.value.map(() => '?').join(',');
                whereClauses.push(`${dbField} IN (${placeholders})`);
                queryParams.push(...filter.value);
            } else if (!Array.isArray(filter.value) && filter.value) {
                whereClauses.push(`${dbField} = ?`);
                queryParams.push(filter.value);
            }
            break;
          case '>=':
            whereClauses.push(`${dbField} >= ?`);
            queryParams.push(filter.value);
            break;
          case '<=':
            whereClauses.push(`${dbField} <= ?`);
            queryParams.push(filter.value);
            break;

        }
      });

      let orderByClause = "ORDER BY ";
      if (sorters && sorters.length > 0) {
        orderByClause += sorters.map(sorter => {
          const dbField = fieldMap[sorter.field] || sorter.field;

          const dir = sorter.dir && sorter.dir.toLowerCase() === 'desc' ? 'DESC' : 'ASC';
          return `${dbField} ${dir}`;
        }).join(', ');
      } else {
        orderByClause += 'svc.swsd_date_time DESC';
      }

      const selectFields = `
          svc.swsd_id, svc.swsd_service_source, svc.swsd_service_status,
          CASE
            WHEN svc.swsd_uploader_vid = ? THEN svc.swsd_uploader_metadata
            WHEN svc.swsd_vendor_id = ? THEN svc.swsd_vendor_metadata
            ELSE NULL
          END AS metadata,
          svc.swsd_event, svc.swsd_qb_invoice_id, svc.swsd_qb_bill_id,
          mb.mb_nickname AS Site, gz.sgz_geo_zone AS Zone,
          DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(svc.swsd_date_time), '+00:00', ?), '%m/%d/%Y') AS Date,
          DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(svc.swsd_date_time), '+00:00', ?), '%h:%i:%s %p') AS Time,
          svc.swsd_date_time AS UnixTime,
          CONCAT(ic.sf_contact_fname, ' ', ic.sf_contact_lname) AS InternalManager,
          REPLACE(TRIM(']' FROM TRIM('[' FROM mb.mb_contractor)), '\"', '') AS contractors,
          mb.mb_id AS site_id, mb.mb_zip_code AS Zip, city.City, state.state_abv_name AS State,
          mb.mb_address1 AS Address, mb.mb_quickbooks_customer_id, svc.swsd_service_name AS Service,
          svc.swsd_snow_inch AS snow_inch, svc.swsd_hours AS hours, svc.swsd_people AS people,
          svc.swsd_service_id, svc.swsd_email, vs.vs_service_id AS vendor_service_vs_id,
          vs.vs_service_type AS connected_service, vs.vs_equipment_id AS equipment_id,
          vs.vs_service_options AS service_options_vendor_services,
          vs.vs_service_type_id AS vendor_services_service_type_id,
          vs.vs_trade_id AS vendor_service_trade_id, svc.swsd_service_options AS options,
          sfs.sfs_form_name AS Form, sfs.sfs_id AS FormId, sfs.sfs_form_id AS ParentFormId,
          svc.swsd_notes AS Notes, svc.swsd_uploader_vid, sfs.sfs_checkin AS Start,
          sfs.sfs_id AS form_submission_id, sfs.sfs_checkout AS Stop, svc.swsd_vendor_sequence_id,
          CONCAT(mv.vendor_fname, ' ', mv.vendor_lname) AS Provider,
          mv.vendor_email AS ProviderEmail, svc.swsd_uploader_vid AS ProviderId,
          svc.swsd_vendor_id AS VendorId,
          IF(mb.mb_user_id = ?, icc.sf_contact_company_name, NULL) AS Client,
          IF(mb.mb_user_id = ?, mb.mb_client, NULL) AS ClientId,
          ic2.InternalContact, swos.swos_workorder_id AS WorkOrder, swo.swo_id AS WoID,
          swo.swo_external_id AS ExternalWorkOrder, swo.swo_system_id AS WoSystemId,
          swo.swo_nte AS ClientNte, swo.swo_nte_contractor AS ContractorNte,
          swmd.swmd_mat_usage AS MaterialQuantity, swmd.swmd_mat_unit AS MaterialUnit,
          st.st_id AS TradeId, st.st_trade AS TradeTitle, svc.swsd_service_options AS ServiceDetails
      `;

      const dataSql = `SELECT ${selectFields} ${baseQuery} WHERE ${whereClauses.join(' AND ')} ${orderByClause} LIMIT ? OFFSET ?`;
      const countSql = `SELECT COUNT(DISTINCT svc.swsd_id) as total ${baseQuery} WHERE ${whereClauses.join(' AND ')}`;


      const selectParams = [
          vendorId, // CASE uploader_vid
          vendorId, // CASE vendor_id
          timeZone, // Date format
          timeZone, // Time format
          vendorId, // IF Client
          vendorId  // IF ClientId
      ];

      const finalDataQueryParams = [...selectParams, ...queryParams, size, offset];
      const finalCountQueryParams = [...queryParams];

      const startSeconds = req.body.start;
      const endSeconds = req.body.end;

      const stormResponseAccuWeatherPromise = fetch(`https://weather.sitefotos.com/snow-events?start=${startSeconds}&end=${endSeconds}`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + jwtToken
        }
      });

      const [lineItems, countResult, stormResponse] = await Promise.all([
        awaitQuery(dataSql, finalDataQueryParams),
        awaitQuery(countSql, finalCountQueryParams),
        stormResponseAccuWeatherPromise
      ]);

      let stormResultsAccuWeather = [];
      if (stormResponse.ok) {
        stormResultsAccuWeather = await stormResponse.json();
      } else {
        console.error(`Failed to fetch storm data: ${stormResponse.status}`);

      }

      const totalRecords = countResult[0].total;
      const totalPages = Math.ceil(totalRecords / size);

      const siteIdSet = new Set();
      lineItems.forEach(row => {
        if (row.site_id) siteIdSet.add(row.site_id);
      });

      let pricingDetails = [];
      if (siteIdSet.size > 0) {

        pricingDetails = await awaitQuery(
          `SELECT * FROM sitefotos_pricing_contracts spc
           INNER JOIN sitefotos_pricing_structure sps ON sps.sps_spc_id = spc.spc_id
           WHERE spc.spc_site_id IN (${[...siteIdSet].join(',')})
             AND spc.spc_contract_active = 1 AND spc.spc_vendor_id = ?`,
          [vendorId]
        );
      }

      const contractors = await getContractors(vendorId);
      const contractorByVendorId = {};
      contractors.forEach(contractor => {
        contractorByVendorId[contractor.vendorId] = contractor;
      });

      const contractorIds = contractors.map(c => c.id).filter(id => id != null);
      let allContractorPricing = [];
      if (contractorIds.length > 0) {
        allContractorPricing = await awaitQuery(
          `SELECT * FROM sitefotos_contact_service_pricing scsp
           WHERE scsp.scsp_vendor_id = ?
             AND scsp.scsp_contact_id IN (?)
             AND scsp_active = true`,
          [vendorId, contractorIds]
        );
      }

      const pricingLookup = {};
      allContractorPricing.forEach(row => {
        if (!pricingLookup[row.scsp_contact_id]) {
          pricingLookup[row.scsp_contact_id] = [];
        }
        pricingLookup[row.scsp_contact_id].push(row);
      });



      const enrichedLineItems = lineItems.map(svc => {
        const contractorData = contractorByVendorId[svc.swsd_uploader_vid] || {};
        svc.ContractorData = contractorData;
        if (contractorData.email) {
          svc.ProviderEmail = contractorData.email;
          svc.ProviderContactId = contractorData.id;
          svc.ContractorQuickbooksId = contractorData.quickbooksId;
          svc.ContractorPersonalProfilePricing = pricingLookup[contractorData.id] || [];
        }


        let connected;
        let mainPrice;
        const contractTypes = ['CONTRACTOR', 'CLIENT'];
        let clientPriceDetails = [], contractorPriceDetails = [];

        svc.ServiceLoggedByEmployee = true;

        if (svc.VendorId == vendorId) {
          connected = pricingDetails.filter(e => (
            (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
              (e.sps_service_type === svc.connected_service && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id) :
              (e.sps_service_type_id === svc.vendor_services_service_type_id && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id)
          ));

          contractTypes.forEach(contractType => {
            let price = pricingDetails.filter(e => (
              (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
                (e.sps_service_type === svc.connected_service && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id && e.sps_contract_for === contractType) :
                (e.sps_service_type_id === svc.vendor_services_service_type_id && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id && e.sps_contract_for === contractType)
            ));

            if (contractType === 'CONTRACTOR') {
              if (svc.ProviderId == vendorId) {
                contractorPriceDetails = [];
                svc.ProviderText = svc.InternalContact != null ? (String(svc.InternalContact).trim() === "" ? svc.swsd_email : svc.InternalContact) : "";
                svc.ServiceLoggedByEmployee = true;
              } else {
                svc.ProviderText = svc.ContractorData.company;
                svc.ServiceLoggedByEmployee = false;
                let foundContractorPrice = [];
                if (svc.ProviderContactId && price.some(p => p.spc_contractors && p.spc_contractors.includes(String(svc.ProviderContactId)))) {
                    foundContractorPrice = price.filter(p => p.spc_contractors && p.spc_contractors.includes(String(svc.ProviderContactId)));
                } else {
                    const generalContract = price.find(p => !p.spc_contractors || p.spc_contractors.length === 0);
                    if (generalContract) foundContractorPrice = price.filter(p => p.spc_id === generalContract.spc_id);
                }
                contractorPriceDetails = foundContractorPrice;
                mainPrice = price;
              }
            } else if (contractType === 'CLIENT') {
              clientPriceDetails = price;
            }
          });
        } else {
          svc.Notes = "";
          connected = pricingDetails.filter(e => (
            (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
            (e.sps_service_type === svc.connected_service && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id) :
            (e.sps_service_type_id === svc.vendor_services_service_type_id && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id)
          ));
           contractTypes.forEach(contractType => {
              let price = pricingDetails.filter(e => (
                (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
                  (e.sps_service_type === svc.connected_service && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id && e.sps_contract_for === contractType) :
                  (e.sps_service_type_id === svc.vendor_services_service_type_id && e.sps_site_id === svc.site_id && svc.vendor_service_trade_id == e.spc_st_trade_id && e.sps_contract_for === contractType)
              ));
               if (contractType === 'CONTRACTOR') {
                    let foundClientPrice = [];
                    if (svc.ProviderContactId && price.some(p => p.spc_contractors && p.spc_contractors.includes(String(svc.ProviderContactId)))) {
                        foundClientPrice = price.filter(p => p.spc_contractors && p.spc_contractors.includes(String(svc.ProviderContactId)));
                    } else {
                         const generalContract = price.find(p => !p.spc_contractors || p.spc_contractors.length === 0);
                         if (generalContract) foundClientPrice = price.filter(p => p.spc_id === generalContract.spc_id);
                    }
                    clientPriceDetails = foundClientPrice;
               }
           });
          contractorPriceDetails = [];
        }

        if (typeof(connected) != "undefined") {
          svc.PricingType = clientPriceDetails.length > 0 ? clientPriceDetails[0].spc_contract_for : "";
          svc.ParentId = clientPriceDetails.length > 0 ? clientPriceDetails[0].spc_id : null;
          svc.PricingTypeContractor = contractorPriceDetails.length > 0 ? contractorPriceDetails[0].spc_contract_for : "";
          svc.ParentIdContractor = contractorPriceDetails.length > 0 ? contractorPriceDetails[0].spc_id : null;
          svc.TradeIdContractor = contractorPriceDetails.length > 0 ? contractorPriceDetails[0].spc_st_trade_id : "";
          svc.ClientContractId = clientPriceDetails.length > 0 ? clientPriceDetails[0].spc_id : 0;
          svc.ContractorContractId = contractorPriceDetails.length > 0 ? contractorPriceDetails[0].spc_id : 0;
          svc.ClientContract = clientPriceDetails.length > 0 ? clientPriceDetails[0].spc_name : "";
          svc.ContractorContract = contractorPriceDetails.length > 0 ? contractorPriceDetails[0].spc_name : "";
          svc.ClientPricingUnit = clientPriceDetails.length < 1 ? "" : (clientPriceDetails[0].sps_contract_type && clientPriceDetails[0].sps_contract_type.length > 0 ? clientPriceDetails[0].sps_contract_type : clientPriceDetails[0].sps_service_option_trigger);
          svc.ContractorPricingUnit = contractorPriceDetails.length < 1 ? "" : (contractorPriceDetails[0].sps_contract_type && contractorPriceDetails[0].sps_contract_type.length > 0 ? contractorPriceDetails[0].sps_contract_type : contractorPriceDetails[0].sps_service_option_trigger);

          svc.Hours = svc.hours === null ? 0 : svc.hours;
          svc.People = svc.people === null ? 0 : svc.people;
          svc.Source = !svc.swsd_service_source || String(svc.swsd_service_source).trim().length === 0 ? 'MOBILE' : svc.swsd_service_source;
          svc.Status = String(svc.swsd_service_status).trim().length === 0 ? 'LOGGED' : svc.swsd_service_status;

          if (svc.snow_inch == null || svc.snow_inch == undefined) svc.snow_inch = 0;

          if (Array.isArray(stormResultsAccuWeather)) {
            const snowAccuWeather = stormResultsAccuWeather.find(sraw => sraw.sfb_building_id == svc.site_id);
            svc.SnowAccuWeather = snowAccuWeather === undefined ? 0 : snowAccuWeather.totalsnow;
          } else {
            svc.SnowAccuWeather = 0;
          }

          svc.ClientPricing = 0;
          svc.ContractorPricing = 0;

          const moment1 = moment.unix(svc.Start);
          const moment2 = moment.unix(svc.Stop);
          const duration = moment.duration(moment2.diff(moment1));
          svc.OutIn = moment.utc(duration.as('milliseconds')).format('HH:mm');
          svc.ServiceDetails = parseServiceDetails(svc.ServiceDetails);

          if (svc.Status == 'REJECTED') {
            svc.ClientPricingUi = 0;
            svc.ContractorPricingUi = 0;
          } else {
            // CLIENT PRICING
            if (svc.WoID > 0 && svc.ClientNte > 0) {
              svc.ClientPricing = svc.ClientNte;
              svc.ClientPricingUnit = null;
            } else {
              const unit = svc.ClientPricingUnit;
              let priceList = clientPriceDetails;
              if (unit == 'HOURS') {
                const filterClientPriceByEquipment = priceList.find(cObj => cObj.sps_equipmentservice_id == svc.equipment_id);
                if (filterClientPriceByEquipment) {
                  let price = filterClientPriceByEquipment.sps_price || 0;
                  if ( (svc.Hours < 1 && svc.People < 1) ) {
                    if (filterClientPriceByEquipment.sps_minimum_minutes > 0 && duration.asMinutes() < filterClientPriceByEquipment.sps_minimum_minutes) {
                      svc.ClientPricing = price * (filterClientPriceByEquipment.sps_minimum_minutes / 60);
                    } else {
                      svc.ClientPricing = price * duration.asHours();
                    }
                  } else {
                     const effHours = svc.Hours || 0; const effPeople = svc.People || 1;
                    if (filterClientPriceByEquipment.sps_minimum_minutes > 0 && effHours * 60 < filterClientPriceByEquipment.sps_minimum_minutes) {
                      svc.ClientPricing = price * (filterClientPriceByEquipment.sps_minimum_minutes / 60) * effPeople;
                    } else {
                      svc.ClientPricing = price * effHours * effPeople;
                    }
                  }
                }
              } else if (['DAY', 'WEEK', 'MONTH', 'SEASON', 'YEAR', 'SERVICE', 'VISIT', 'PARTIAL_SERVICE', 'INCH'].includes(unit)) {
                svc.ClientPricing = priceList.length > 0 ? (priceList[0].sps_price || 0) : 0;
              } else if (unit == 'EVENT' || unit == 'PUSH') {
                if (svc.snow_inch === null || svc.snow_inch < 0) {
                  svc.ClientPricing = 0;
                } else {
                  let filtered = findClosestSnowTrigger(priceList, svc.snow_inch);
                  svc.ClientPricing = filtered ? (filtered.sps_price || 0) : 0;
                }
              } else if (unit == 'ITEM') {
                let price = priceList.length > 0 ? (priceList[0].sps_price || 0) : 0;
                svc.ClientPricing = price * parseFloat(svc.MaterialQuantity || 0);
              }
            }

            // CONTRACTOR PRICING
            if (svc.WoID > 0 && svc.ContractorNte > 0) {
              svc.ContractorPricing = svc.ContractorNte;
            } else {
              const unit = svc.ContractorPricingUnit;
              let priceList = contractorPriceDetails;
              if (unit === 'HOURS' && (!priceList || priceList.length === 0) && mainPrice && mainPrice.length > 0) {
                  priceList = mainPrice;
              }

              if (unit == 'HOURS') {
                const filterContractorPriceByEquipment = priceList.find(cObj => cObj.sps_equipmentservice_id == svc.equipment_id);
                if (filterContractorPriceByEquipment) {
                  let price = filterContractorPriceByEquipment.sps_price || 0;
                  const effHours = svc.Hours || 0; const effPeople = svc.People || 1;
                  if (filterContractorPriceByEquipment.sps_minimum_minutes > 0 && effHours * 60 < filterContractorPriceByEquipment.sps_minimum_minutes) {
                    svc.ContractorPricing = price * (filterContractorPriceByEquipment.sps_minimum_minutes / 60) * effPeople;
                  } else {
                    if (svc.Hours < 1 && svc.People < 1) {
                       svc.ContractorPricing = price * duration.asHours();
                    } else {
                       svc.ContractorPricing = price * effHours * effPeople;
                    }
                  }
                } else {
                  svc.TradeIdContractor = -1;
                  svc.ContractorPricing = getPayRateFromContractorProfile(svc, duration, vendorId) || 0;
                }
              } else if (['DAY', 'WEEK', 'MONTH', 'SEASON', 'YEAR', 'SERVICE', 'VISIT', 'PARTIAL_SERVICE', 'INCH'].includes(unit)) {
                svc.ContractorPricing = priceList.length > 0 ? (priceList[0].sps_price || 0) : 0;
                 if (svc.ContractorPricing === 0 && unit !== 'HOURS') {
                    svc.TradeIdContractor = -1;
                    svc.ContractorPricing = getPayRateFromContractorProfile(svc, duration, vendorId) || 0;
                }
              } else if (unit == 'EVENT' || unit == 'PUSH') {
                if (svc.snow_inch === null || svc.snow_inch < 0) {
                  svc.ContractorPricing = 0;
                } else {
                  let filtered = findClosestSnowTrigger(priceList, svc.snow_inch);
                  svc.ContractorPricing = filtered ? (filtered.sps_price || 0) : 0;
                }
                 if (svc.ContractorPricing === 0 && unit !== 'HOURS') {
                    svc.TradeIdContractor = -1;
                    svc.ContractorPricing = getPayRateFromContractorProfile(svc, duration, vendorId) || 0;
                }
              } else if (unit == 'ITEM') {
                let price = priceList.length > 0 ? (priceList[0].sps_price || 0) : 0;
                svc.ContractorPricing = price * parseFloat(svc.MaterialQuantity || 0);
              } else {
                  svc.TradeIdContractor = -1;
                  svc.ContractorPricing = getPayRateFromContractorProfile(svc, duration, vendorId) || 0;
              }
            }
            svc.ClientPricingUi = svc.ClientPricing;
            svc.ContractorPricingUi = svc.ContractorPricing;
          }
        } else {
            svc.id = svc.swsd_id || 0;
            svc.PricingType = ""; svc.ParentId = null; svc.TradeId = svc.vendor_service_trade_id || "";
            svc.PricingTypeContractor = ""; svc.ParentIdContractor = null; svc.TradeIdContractor = "";
            svc.ClientContract = ""; svc.ContractorContract = "";
            svc.ClientPricingUnit = ""; svc.ContractorPricingUnit = "";
            svc.Hours = svc.hours === null ? 0 : svc.hours;
            svc.People = svc.people === null ? 0 : svc.people;
            svc.Source = svc.swsd_service_source;
            svc.Status = svc.swsd_service_status;
            svc.ClientPricing = 0; svc.ContractorPricing = 0;
            svc.ClientPricingUi = 0; svc.ContractorPricingUi = 0;
            svc.OutIn = "";
            svc.snow_inch = svc.snow_inch || 0;
            svc.ServiceDetails = parseServiceDetails(svc.ServiceDetails);
        }
        return svc;
      });


      findDuplicatesForEvents(enrichedLineItems, ["ClientPricingUnit", "vendor_services_service_type_id", "ClientContractId", "swsd_event"]);
      findDuplicatesForEvents(enrichedLineItems, ["ContractorPricingUnit", "vendor_services_service_type_id", "ContractorContractId", "swsd_event"]);

      res.json({
        data: enrichedLineItems,
        last_page: totalPages,
        total: totalRecords,

      });
    } catch (ex) {
      console.error("Error in getServiceHistoryDashboard:", ex);
      next(ex);
    }
  };
const getTrades = async (req, res, next) => {
    try {
        const token = req.cookies.JWT;
        const { vendorId } = await login(token);
        if (!vendorId) {
            return res.sendStatus(401);
        }
        const result = await service.getAllTrades(vendorId);
        res.json(result);
    } catch (error) {
        next(error);
    }
}

const getEquipment = async (req, res, next) => {
    try {
        const token = req.cookies.JWT;
        const { vendorId } = await login(token);
        if (!vendorId) {
            return res.sendStatus(401);
        }
        const result = await service.getAllEquipmentTypesWithTrades(vendorId);
        res.json( result.sort( (a, b) => a.set_equipment_name.localeCompare(b.set_equipment_name)) );
    } catch (error) {
        next(error);
    }
}

const getServiceTypes = async (req, res, next) => {
    try {
        const token = req.cookies.JWT;
        const { vendorId } = await login(token);
        if (!vendorId) {
            return res.sendStatus(401);
        }
        const result = await service.getAllServiceTypes();
        res.json(result);
    } catch (error) {
        next(error);
    }
}

//Service history dashboard
const getServiceHistoryDashboard = async (req, res, next) => {
    try {
      const { vendorId, restrictedUser, userContactId } = await login(req.cookies.JWT);
      const timeZone = req.query.tz || 'America/New_York';
      const start = req.query.start;
      const end = req.query.end

      const getPricingSql = /*SQL*/`
        SELECT 
          svc.swsd_id, 
          svc.swsd_service_source, 
          svc.swsd_service_status, 
          CASE 
            WHEN svc.swsd_uploader_vid = ? THEN svc.swsd_uploader_metadata
            WHEN svc.swsd_vendor_id = ? THEN svc.swsd_vendor_metadata
            ELSE NULL
          END AS metadata,
          svc.swsd_event, 
          svc.swsd_qb_invoice_id,
          svc.swsd_qb_bill_id,
          mb.mb_nickname AS Site, 
          gz.sgz_geo_zone AS Zone,
          mb.mb_timezone AS Timezone, 
          svc.swsd_date_time AS UnixTime,
          CONCAT(ic.sf_contact_fname, ' ', ic.sf_contact_lname) AS InternalManager,
          REPLACE(TRIM(']' FROM TRIM('[' FROM mb.mb_contractor)), '\"', '') AS contractors, 
          mb.mb_id AS site_id, 
          mb.mb_zip_code AS Zip,
          city.City, 
          state.state_abv_name AS State,
          mb.mb_address1 AS Address,
          mb.mb_quickbooks_customer_id, 
          svc.swsd_service_name AS Service, 
          svc.swsd_date_time, 
          svc.swsd_snow_inch AS snow_inch, 
          svc.swsd_hours AS hours, 
          svc.swsd_people AS people, 
          svc.swsd_service_id, 
          svc.swsd_email,
          vs.vs_service_id, 
          vs.vs_service_type AS connected_service, 
          vs.vs_equipment_id AS equipment_id, 
          vs.vs_service_options AS service_options_vendor_services, 
          vs.vs_service_type_id AS vendor_services_service_type_id, 
          vs.vs_trade_id AS vendor_service_trade_id,
          svc.swsd_service_options AS options, 
          sfs.sfs_form_name AS Form, 
          sfs.sfs_id AS FormId, 
          sfs.sfs_form_id AS ParentFormId,
          svc.swsd_notes AS Notes, 
          svc.swsd_uploader_vid,
          sfs.sfs_checkin AS Start,
          sfs.sfs_id AS form_submission_id,
          sfs.sfs_checkout AS Stop,
          svc.swsd_vendor_sequence_id,
          CONCAT(mv.vendor_fname, ' ', mv.vendor_lname) AS Provider, 
          mv.vendor_email AS ProviderEmail, 
          svc.swsd_uploader_vid AS ProviderId,
          svc.swsd_vendor_id AS VendorId,
          IF(mb.mb_user_id = ?, icc.sf_contact_company_name, NULL) AS Client, 
          IF(mb.mb_user_id = ?, mb.mb_client, NULL) AS ClientId, 
          ic2.InternalContact,
          swos.swos_workorder_id AS WorkOrder, 
          swo.swo_id AS WoID,
          swo.swo_external_id AS ExternalWorkOrder, 
          swo.swo_system_id AS WoSystemId,
          swo.swo_nte AS ClientNte,
          swo.swo_nte_contractor AS ContractorNte,
          swmd.swmd_mat_usage AS MaterialQuantity, 
          swmd.swmd_mat_unit AS MaterialUnit,
          st.st_id AS TradeId,
          st.st_trade AS TradeTitle,
          svc.swsd_service_options AS ServiceDetails ,
          svc.swsd_status_updated_by AS UpdatedBy,
          svc.swsd_status_updated_at AS UpdatedAt
        FROM sitefotos_wp_services_data svc
        LEFT JOIN maptile_building mb ON mb.mb_id = svc.swsd_site_id 
        LEFT JOIN sitefotos_geofence_zones gz ON gz.sgz_zone_id = mb.mb_zone_id
        LEFT JOIN maptile_city city ON city.CityId = mb.mb_city
        LEFT JOIN maptile_state state ON state.id = mb.mb_state
        LEFT JOIN sitefotos_contacts ic ON ic.sf_contact_id = mb.mb_manager
        LEFT JOIN sitefotos_contacts_company_view icc ON icc.sf_contact_id = mb.mb_client
        LEFT JOIN maptile_vendors mv ON mv.vendor_id = svc.swsd_uploader_vid
        LEFT JOIN vendor_services vs ON vs.vs_service_id = svc.swsd_service_id 
        LEFT JOIN sitefotos_trades st ON st.st_id = vs.vs_trade_id
        LEFT JOIN sitefotos_forms_submitted sfs ON sfs.sfs_id = svc.swsd_form_submission_reference 
        LEFT JOIN sitefotos_work_orders_submissions swos ON swos.swos_form_submission_id = svc.swsd_form_submission_reference
        LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id
        LEFT JOIN (
          SELECT swmd_parent_service_submission_id, MIN(swmd_mat_id) AS mat_id, swmd_mat_usage, swmd_mat_unit
          FROM sitefotos_wp_material_data 
          GROUP BY swmd_parent_service_submission_id
        ) swmd ON swmd.swmd_parent_service_submission_id = svc.swsd_id
        LEFT JOIN (
          SELECT sf_contact_email, CONCAT(sf_contact_fname, ' ', sf_contact_lname) AS InternalContact
          FROM sitefotos_contacts 
          WHERE sf_contact_vendorid = ?
          GROUP BY sf_contact_email
        ) ic2 ON ic2.sf_contact_email = svc.swsd_email
        WHERE (svc.swsd_vendor_id = ? OR svc.swsd_uploader_vid = ?)
          AND svc.swsd_date_time >= ? 
          AND svc.swsd_date_time < ? 
          AND svc.swsd_service_status != 'TEMPLOG'
          ${restrictedUser && userContactId > 0 
            ? `AND (mb.mb_manager = ${userContactId} OR mb.mb_manager_secondary = ${userContactId} OR mb.mb_manager_third = ${userContactId})`
            : ''
          }
        ORDER BY svc.swsd_date_time
      `;


      const queryParams = [
        vendorId,        // 1. CASE: svc.swsd_uploader_vid = ?
        vendorId,        // 2. CASE: svc.swsd_vendor_id = ?
        vendorId,        // 5. IF condition for Client (mb.mb_user_id = ?)
        vendorId,        // 6. IF condition for ClientId (mb.mb_user_id = ?)
        vendorId,        // 7. InternalContact subquery filter: WHERE sf_contact_vendorid = ?
        vendorId,        // 8. WHERE: svc.swsd_vendor_id = ?
        vendorId,        // 9. WHERE: svc.swsd_uploader_vid = ?
        start, // 10. Date range start
        end    // 11. Date range end
      ];

      const lineItems = await awaitQuery(getPricingSql, queryParams);


      const siteIdSet = new Set();
      lineItems.forEach(row => {
        if (row.site_id) siteIdSet.add(row.site_id);
      });

      let pricingDetails = [];
      if (siteIdSet.size > 0) {
        pricingDetails = await awaitQuery(
          `SELECT * FROM sitefotos_pricing_contracts spc
           INNER JOIN sitefotos_pricing_structure sps ON sps.sps_spc_id = spc.spc_id
           WHERE spc.spc_site_id IN (${[...siteIdSet].join(',')}) 
             AND spc.spc_contract_active = 1`,
          [vendorId]
        );
      }


      const contractors = await getContractors(vendorId);


      const contractorByVendorId = {};
      contractors.forEach(contractor => {
        contractorByVendorId[contractor.vendorId] = contractor;
      });


      const contractorIds = contractors.map(c => c.id);
      let allContractorPricing = [];
      if (contractorIds.length > 0) {
        allContractorPricing = await awaitQuery(
          `SELECT * FROM sitefotos_contact_service_pricing scsp 
           WHERE scsp.scsp_vendor_id = ? 
             AND scsp.scsp_contact_id IN (?)
             AND scsp_active = true`,
          [vendorId, contractorIds]
        );
      }


      const pricingLookup = {};
      allContractorPricing.forEach(row => {
        if (!pricingLookup[row.scsp_contact_id]) {
          pricingLookup[row.scsp_contact_id] = [];
        }
        pricingLookup[row.scsp_contact_id].push(row);
      });


      const enrichedLineItems = lineItems.map(item => {
        const contractor = contractorByVendorId[item.swsd_uploader_vid] || {};
        item.ContractorData = contractor;
        if (contractor.email) {
          item.ProviderEmail = contractor.email;
          item.ProviderContactId = contractor.id;
          item.ContractorQuickbooksId = contractor.quickbooksId;
          item.ContractorPersonalProfilePricing = pricingLookup[contractor.id] || [];
        }
        return item;
      });

      const snow_storms = await service.getSnowStorms(siteIdSet, start, end);

      res.json({
        pricingDetails,
        lineItems: enrichedLineItems,
        snowStorms: snow_storms
      });
    } catch (ex) {
      console.error(ex);
      next(ex);
    }
  };

  const getContractors = async (vendorId) => {
    try {
      const contacts = await awaitQuery(/*SQL*/`
        SELECT * FROM sitefotos_contacts_company_view 
        LEFT JOIN maptile_vendors ON sf_contact_contractor_vendor_id = vendor_id 
        WHERE sf_contact_vendorid = ? 
          AND sf_contact_type = 'Contractor' 
          AND sf_contact_active = '1'
      `, [vendorId]);
      return contacts.map(contact => ({
        id: contact.sf_contact_id,
        company: contact.sf_contact_company_name || contact.vendor_company_name,
        vendorId: contact.vendor_id,
        email: contact.sf_contact_email,
        quickbooksId: contact.sf_contact_quickbooks_id
      }));
    } catch (ex) {
      throw ex;
    }
  };




const getWorkerHistoryDashboard = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT, req.body?.accessCode);
        //const {vendorId} = await login(req.cookies.JWT);
        const start = req.query.start;
        const end = req.query.end;
        const vendorAccount = await awaitQuery(`select CONCAT(mv.vendor_fname, " ", mv.vendor_lname) as VendorName from maptile_vendors mv where mv.vendor_id=?`, [vendorId]);
        const workerHistory = await service.getWorkerHistory(vendorId, start, end, internalUid);
        const employees = await awaitQuery(`select * from sitefotos_contacts sc where sc.sf_contact_vendorid =? and sf_contact_type = 'EMPLOYEE' and sf_contact_active = true`, [vendorId]);
        const employeeRates = await awaitQuery(`select * from sitefotos_contact_service_pricing scsp where scsp.scsp_vendor_id=? and scsp_contact_type=? and scsp.scsp_active = 1`, [vendorId, 'EMPLOYEE']);
        const vendorSettings = await awaitQuery(`select * from sitefotos_vendor_settings svs where svs.svs_vendor_id =?`, [vendorId]);
        res.json(
          {
              vendorDetails: vendorAccount.length > 0 ? vendorAccount[0]:{ VendorName: ''},
              breadCrumbs: workerHistory.bc,
              servicesData: workerHistory.servtab,
              employeeList: employees,
              employeeRates: employeeRates,
              vendorSettings: vendorSettings.length > 0 ? vendorSettings[0] :
                {
                    svs_distance_unit: 'mi',
                    svs_volume_unit: 'gallon(us)',
                    svs_overtime_hours: 40,
                    svs_overtime_rate_multiplier: 1.5
                }
          }
        );
    }
    catch (e) {
        next(e);
        console.error("Error at worker history", e);
    }
}

const updatedAdjustedCheckInOutForWorkerPayroll = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT, req.body?.accessCode);
        let internalID = internalUid;
        internalID = internalID == null ? 0 : internalID;
        let request = req.body;
        request.swp_user_id = internalID;
        let responseRowId = 0;
        if (request.swp_id > 0) {
            //Update
            responseRowId = request.swp_id;
            await updateObj(
              'sitefotos_worker_payroll',
              request,
              ['swp_id'],
              [request.swp_id]
            );
        }
        else {
            //Insert
            const insertion = await insertObj(
              'sitefotos_worker_payroll',
              request
            );
            responseRowId = insertion.insertId;
        }
        res.json({
            "dbChangeId": responseRowId
        });
    }
    catch (error) {
        console.error(error);
        next(new Error('Failed on updatedAdjustedCheckInOutForWorkerPayroll'));
    }
}

const updateWorkerPayrollLineItems = async (req, res, next) => {
    try {
        const { vendorId, internalUid } = await login(req.cookies.JWT, req.body?.accessCode);
        let internalID = internalUid;
        internalID = internalID == null ? 0 : internalID;

        const rows = req.body.rows;
        for (const request of rows) {
            if (request.swp_id > 0) {
                //Update
                request.swp_user_id = internalID;
                await updateObj(
                  'sitefotos_worker_payroll',
                  request,
                  ['swp_id'],
                  [request.swp_id]
                );
            }
            else {
                request.swp_user_id = internalID;
                //Insert
                const insertion = await insertObj(
                  'sitefotos_worker_payroll',
                  request
                );
            }
        }
        res.json("1");
    }
    catch (error) {
        console.error(error);
        next(new Error('Failed to update payroll line items'));
    }
}

// In-memory job store
const jobStore = new Map();

// Add this at the top of the file with other requires
const { Worker } = require('worker_threads');
const crypto = require('crypto');
const path = require('path');

const updateSubmittedService = async (req, res, next) => {
    let connection;
    try {
        const { vendorId, restrictedUser, userContactId } = await login(req.cookies.JWT);
        let { ids, snowTotal, eventName, hours, people, selectAllAcrossPages, filters, sorters, start, end } = req.body;

        // Handle cross-page selection mode with worker threads
        if (selectAllAcrossPages) {
            if (!start || !end) {
                return res.status(400).json({
                    success: false,
                    error: "Start and end date parameters are required for cross-page operations"
                });
            }
            
            const parsedFilters = filters ? (typeof filters === 'string' ? JSON.parse(filters) : filters) : [];
            const parsedSorters = sorters ? (typeof sorters === 'string' ? JSON.parse(sorters) : sorters) : [];
            
            // Create job ID
            const jobId = crypto.randomUUID();
            
            // Initialize job in store
            jobStore.set(jobId, {
                status: 'processing',
                total: 0,
                processed: 0,
                percentage: 0,
                startTime: Date.now(),
                vendorId: vendorId
            });
            
            // Start worker thread
            const workerPath = path.join(__dirname, '..', 'workers', 'bulk-update-worker.js');
            console.log(`[Controller] Starting worker for job ${jobId}, worker path: ${workerPath}`);
            console.log(`[Controller] Worker data:`, {
                jobId,
                filtersCount: parsedFilters.length,
                sortersCount: parsedSorters.length,
                start,
                end,
                updateData: { snowTotal, eventName, hours, people },
                vendorId
            });

            const worker = new Worker(workerPath, {
                workerData: {
                    jobId,
                    filters: parsedFilters,
                    sorters: parsedSorters,
                    start,
                    end,
                    updateData: { snowTotal, eventName, hours, people },
                    vendorId,
                    restrictedUser,
                    userContactId
                }
            });
            
            // Handle worker messages
            worker.on('message', (message) => {
                console.log(`[Controller] Received message from worker ${message.jobId}:`, message.type, message.data);
                if (message.type === 'updateJob' || message.type === 'progress') {
                    const job = jobStore.get(message.jobId);
                    if (job) {
                        Object.assign(job, message.data);
                    }
                } else if (message.type === 'complete') {
                    const job = jobStore.get(message.jobId);
                    if (job) {
                        Object.assign(job, message.data);
                        // Clean up after 5 minutes
                        setTimeout(() => jobStore.delete(message.jobId), 300000);
                    }
                } else if (message.type === 'error') {
                    const job = jobStore.get(message.jobId);
                    if (job) {
                        Object.assign(job, message.data);
                        // Clean up after 5 minutes
                        setTimeout(() => jobStore.delete(message.jobId), 300000);
                    }
                }
            });
            
            worker.on('error', (error) => {
                console.error('Worker error:', error);
                const job = jobStore.get(jobId);
                if (job) {
                    job.status = 'failed';
                    job.error = error.message;
                }
            });
            
            // Return immediately with job ID
            return res.json({
                success: true,
                jobId,
                message: 'Bulk update started. Poll /node/services/job-status/' + jobId + ' for progress.'
            });
            
        } else if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                error: "No valid IDs provided for update"
            });
        }


        connection = await getDirectConnection();
        await connection.beginTransaction();


        const setClause = [];
        const params = [];
        const logEntries = [];

        if (snowTotal != null) {
            setClause.push('swsd_snow_inch = ?');
            params.push(snowTotal);
            for (const id of ids) {
              logEntries.push({
                field: 'swsd_snow_inch',
                newValue: snowTotal,
                action: 'SNOW',
                note: 'Updated snow total'
              });
            }
        }
        if (eventName != null) {
            setClause.push('swsd_event = ?');
            params.push(eventName);
            for (const id of ids) {
              logEntries.push({
                field: 'swsd_event',
                newValue: eventName,
                action: 'EVENT',
                note: 'Updated event name'
              });
            }
        }
        if (hours != null) {
            setClause.push('swsd_hours = ?');
            params.push(hours);
            for (const id of ids) {
              logEntries.push({
                field: 'swsd_hours',
                newValue: hours,
                action: 'HOURS',
                note: 'Updated hours'
              });
            }
        }
        if (people != null) {
            setClause.push('swsd_people = ?');
            params.push(people);
            for (const id of ids) {
              logEntries.push({
                field: 'swsd_people',
                newValue: people,
                action: 'PEOPLE',
                note: 'Updated people count'
              });
            }
        }

        if (setClause.length === 0) {
            await connection.rollback();
            return res.status(400).json({
                success: false,
                error: "No valid update fields provided"
            });
        }


        params.push(...ids);


        const query = `
            UPDATE sitefotos_wp_services_data 
            SET ${setClause.join(', ')}
            WHERE swsd_id IN (${ids.map(() => '?').join(',')})
        `;

        // Audit trail
        for (const id of ids) {
          for (const entry of logEntries) {
            await addServiceHistoryAuditTrail(
              vendorId,
              userContactId,
              id,
              entry.action,
              entry.field,
              null,
              entry.newValue,
              entry.note
            );
          }
        }

        await connection.execute(query, params);
        await connection.commit();

        res.json({ success: true });
    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error('Service update error:', error);
        next(error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}




const changeServiceStatus = async (req, res, next) => {
    try {
        const {vendorId, restrictedUser, userContactId} = await login(req.cookies.JWT);
        let { rows, status, selectAllAcrossPages, filters, sorters, start, end } = req.body;

        // Handle cross-page selection mode
        if (selectAllAcrossPages) {
            if (!start || !end) {
                return res.status(400).json({
                    success: false,
                    error: "Start and end date parameters are required for cross-page operations"
                });
            }
            
            if (!status) {
                return res.status(400).json({
                    success: false,
                    error: "Status is required for bulk status change operations"
                });
            }
            
            const parsedFilters = filters ? (typeof filters === 'string' ? JSON.parse(filters) : filters) : [];
            const parsedSorters = sorters ? (typeof sorters === 'string' ? JSON.parse(sorters) : sorters) : [];
            
            // Get all filtered service IDs
            const serviceIds = await getFilteredServiceIds(vendorId, restrictedUser, userContactId, parsedFilters, parsedSorters, start, end);
            
            if (serviceIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    error: "No services found matching the current filters"
                });
            }
            
            // Update status for all filtered services
            for (const serviceId of serviceIds) {
                await servicesService.updateServiceStatusSitefotosWpTable(vendorId, serviceId, status, userContactId);
            }
            
            res.json({ 
                success: true, 
                message: `Updated ${serviceIds.length} services to ${status} status`,
                affectedCount: serviceIds.length
            });
        } else {
            // Original behavior - update specific rows
            if (!rows || !Array.isArray(rows) || rows.length === 0) {
                return res.status(400).json({
                    success: false,
                    error: "No valid rows provided for update"
                });
            }
            
            for (const row of rows) {
                await servicesService.updateServiceStatusSitefotosWpTable(vendorId, row.swsd_id, status, userContactId);
            }
            
            res.json("1");
        }
    } catch (error) {
        console.error('Change service status error:', error);
        next(error);
    }
}

const addUpdateServiceNotes = async (req, res, next) => {
    try {
        const {vendorId, internalUid, userContactId} = await login(req.cookies.JWT);
        //swsd_id
        let serviceId = req.body.serviceId;
        let notes = req.body.notes;
        await servicesService.addServiceNotes(vendorId, serviceId, notes, userContactId);
        res.json("1");
    } catch (error) {
        next(error);
    }
}

const loadEmailData = async (req,res,next) => {
    try {
        let {vendorId} = await login(req.cookies.JWT);
        if (!vendorId) {
            console.error("Invalid Access Code");
            res.status(401).send(err.message);
            return
        }
        let emailBody = await awaitSafeQuery(`select * from sitefotos_postdated_services_email_templates where spset_vendor_id=?`, [vendorId]);
        if (emailBody.length > 0){
            res.json(emailBody[0])
        } else {
            res.json(0)
        }
    }
    catch (e) {
        next(e)
    }
}

//Sending services in postdated email flow
const sendServicesPostdated = async (req, res, next) => {
    try {
        let {vendorId, restrictedUser, userContactId} = await login(req.cookies.JWT);
        if(!vendorId) {
            console.error("Invalid Access Code");
            res.status(401).send(err.message);
            return
        }
        let contractorsToEmail = [];
        //Insertion or updation in email templates table: sitefotos_postdated_services_email_templates
        let saveTemplate = req.body.saveTemplate;
        let includePricingColumn = req.body.includePricingColumn;
        let emailBody = req.body.emailBody;
        res.send({'id': 0});

        if (saveTemplate){
            const dbRowEmail = await awaitSafeQuery(`select * from sitefotos_postdated_services_email_templates where spset_vendor_id=?`,[vendorId]);
            if (dbRowEmail.length>0){
                //Update
                const rowId = dbRowEmail[0].spset_id;
                await updateObj("sitefotos_postdated_services_email_templates", {spset_email_body: emailBody, spset_show_pricing: includePricingColumn}, ["spset_id", "spset_vendor_id"], [rowId, vendorId]);
            } else {
                //Insert
                await insertObj(
                  "sitefotos_postdated_services_email_templates",
                  {
                      spset_vendor_id: vendorId,
                      spset_email_body: emailBody,
                      spset_show_pricing: includePricingColumn
                  });
            }
        }

        let startDate = req.body.startDate;
        let endDate = req.body.endDate;
        let checkIn = req.body.checkIn;
        let checkOut = req.body.checkOut;
        let allServices;
        
        // Handle cross-page selection mode
        if (req.body.selectAllAcrossPages) {
            if (!req.body.start || !req.body.end) {
                return res.status(400).json({
                    success: false,
                    error: "Start and end date parameters are required for cross-page operations"
                });
            }
            
            const parsedFilters = req.body.filters ? (typeof req.body.filters === 'string' ? JSON.parse(req.body.filters) : req.body.filters) : [];
            const parsedSorters = req.body.sorters ? (typeof req.body.sorters === 'string' ? JSON.parse(req.body.sorters) : req.body.sorters) : [];
            
            // Get all filtered service IDs
            const serviceIds = await getFilteredServiceIds(vendorId, restrictedUser, userContactId, parsedFilters, parsedSorters, req.body.start, req.body.end);
            
            if (serviceIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    error: "No services found matching the current filters"
                });
            }
            
            // Query full service data for the filtered IDs
            // We need the same fields that would be in selectedRows for email generation
            const serviceQuery = `
                SELECT 
                    svc.swsd_id,
                    svc.swsd_uploader_vid,
                    mb.mb_id as site_id,
                    mb.mb_nickname AS Site,
                    svc.swsd_uploader_vid AS ProviderId,
                    CONCAT(mv.vendor_fname, ' ', mv.vendor_lname) AS Provider,
                    mv.vendor_email AS ProviderEmail,
                    0 AS ContractorPricing
                FROM sitefotos_wp_services_data svc
                LEFT JOIN maptile_building mb ON mb.mb_id = svc.swsd_site_id
                LEFT JOIN maptile_vendors mv ON mv.vendor_id = svc.swsd_uploader_vid
                WHERE svc.swsd_id IN (${serviceIds.map(() => '?').join(',')})
            `;
            
            allServices = await awaitQuery(serviceQuery, serviceIds);
        } else {
            // Original behavior - use provided selectedRows
            allServices = req.body.selectedRows; //Even if tabulator is showing 100 services, it sends all the services in the table on all its pages.
        }
        const groupedData = {};
        allServices.forEach(item => {
          const serviceProviderVid = item.swsd_uploader_vid;
          if (!groupedData[serviceProviderVid]) {
            groupedData[serviceProviderVid] = [];
          }
          groupedData[serviceProviderVid].push(item);
        });
        const serviceDividedPerContractor = Object.values(groupedData);

        const now = moment().unix();
        let senderVendorDetails = await awaitQuery(`select mv.vendor_email, CONCAT(vendor_fname, " ", vendor_lname) as completename, mv.vendor_company_name as companyname, mv.vendor_company_logo as company_logo from maptile_vendors mv where mv.vendor_id =?`, [vendorId]);

        for (const element of serviceDividedPerContractor) { //element is also array
            //all incoming data has been divided per contractor, but now it also needs to be divided per site.
            const groupedDataSite = {};
            element.forEach(item => {
              const siteId = item.site_id;
              if (!groupedDataSite[siteId]) {
                  groupedDataSite[siteId] = [];
              }
                groupedDataSite[siteId].push(item);
            });
            const resultArray = Object.values(groupedDataSite);
            for (const resultArrayElement of resultArray) {
                let getSwsdIdFroSelectedRows = resultArrayElement.map((sr) => sr.swsd_id);

                let serviceIdAndItsContractorPrice = resultArrayElement.map((sr)=> {
                    return {
                        'swsd_id': sr.swsd_id,
                        'price': sr.ContractorPricing
                    }
                });

                let queryString = hashids.encode([vendorId, resultArrayElement[0].ProviderId, now]);
                //In any case hashid fails.
                if (queryString.trim().length !== 0){
                    for (const swsdIdFroSelectedRow of getSwsdIdFroSelectedRows) {
                        await servicesService.updateServiceStatusSitefotosWpTable(vendorId, swsdIdFroSelectedRow, 'SENT', userContactId);
                    }

                    let dbObj = {
                        "sps_vendor_id": vendorId,
                        "sps_report_status": 0, //0 means generated but not yet opened
                        "sps_querystring": queryString,
                        "sps_report_start_date": startDate,
                        "sps_report_end_date": endDate,
                        "sps_swsd_id": getSwsdIdFroSelectedRows,
                        "sps_site_name": resultArrayElement[0].Site,
                        "sps_site_id": resultArrayElement[0].site_id,
                        "sps_contractor_id": resultArrayElement[0].ProviderId,
                        "sps_show_pricing": includePricingColumn,
                        "sps_price_mapping_service": serviceIdAndItsContractorPrice,
                        "sps_checkin_restriction": checkIn,
                        "sps_checkout_restriction": checkOut
                    };

                    let insertedRecord = await insertObj('sitefotos_postdated_services', dbObj);

                    const vars = {
                        url: `${baseURL}/node/reports/post-dated-services?q=${queryString}`,
                        firstName: resultArrayElement[0].Provider,
                        companyname: senderVendorDetails[0].companyname,
                        vendorcompanyurl: senderVendorDetails[0].company_logo,
                        emailBody: emailBody
                    };
                    console.log("Audit Start")
                  await addServiceHistoryAuditTrail(
                    vendorId,
                    userContactId,
                    0,
                    'EMAIL_SENT',
                    null,
                    null,
                    queryString,
                    `Postdated email sent to ${resultArrayElement[0].ProviderEmail}`
                  );
                  console.log("Audit End")
                    contractorsToEmail.push({...vars, "ProviderEmail": resultArrayElement[0].ProviderEmail});
                }
            }
        }

        const uniqueEmails = new Set();
        const uniqueData = [];
        contractorsToEmail.forEach(item => {
            if (!uniqueEmails.has(item.url)) {
                uniqueEmails.add(item.url);
                uniqueData.push(item);
            }
        });
        for (const contractorE of uniqueData) {
            let emailResp = await sendEmailTemplate(
              contractorE.firstName,
              contractorE.ProviderEmail,
              'Contractor Work Summary from '+ contractorE.companyname,
              './static/emails/reports/postdatedservices-email.html',
              contractorE,
              '<EMAIL>'
            );
        }
    } catch (error) {
        next(error)
    }
}

const updateReportEmail = async (req, res, next) => {
    try {
        let queryString = req.body.query_string;
        let status = req.body.status;

        if (!queryString){
            res.json({success: 0, message: "missing params"});
            return
        }

        //We need to update status of all the services here

        let getRow = await awaitSafeQuery(
          `
        SELECT sre_id, sps_swsd_id, sps_vendor_id from sitefotos_postdated_services where sps_querystring = ? 
      `,
          [queryString]);

        for (const servicesIds of getRow) {
            let vendorId = servicesIds.sps_vendor_id;
            for (const servicesId of servicesIds.sps_swsd_id) {
                await servicesService.updateServiceStatusSitefotosWpTable(vendorId, servicesId, 'PENDING', userContactId);
            }

        let update = {
            sps_report_status: status
        };

            let updateRecord = await updateObj(
              'sitefotos_postdated_services',
              update,
              ['sre_id', 'sps_vendor_id','sps_querystring'],
              [servicesIds.sre_id, vendorId, queryString]
            );
        }


        res.status(200).json({success: 1})
    } catch (error) {
        next(error)
    }
}

const addNewServicesToReportEmail = async (req,res,next) => {
    try {
        let vendorId = req.body.vendor_id;
        let queryString = req.body.query_string;
        let siteId = req.body.site_id;
        let serviceIdsString = req.body.service_ids;
        let serviceIds = serviceIdsString.map(str => parseInt(str));

        let record = await awaitSafeQuery(`select sps_swsd_id from sitefotos_postdated_services where sps_querystring=? and sps_site_id=?`,[queryString,siteId]);

        if (record.length > 0){
            //Record exists
            let oldServiceIds = record[0].sps_swsd_id;
            let combinedIds = [...oldServiceIds, ...serviceIds];
            await updateObj(
              'sitefotos_postdated_services',
              {
                  'sps_swsd_id': combinedIds
              },
              ['sps_querystring', 'sps_vendor_id', 'sps_site_id'],
              [queryString, vendorId, siteId]
            );
            // await awaitSafeQuery(
            //   `
            //   UPDATE sitefotos_postdated_services
            //   SET sps_swsd_id =?
            //   WHERE sps_querystring = ? AND sps_vendor_id = ? and sps_site_id=?
            //   `,
            //   [combinedIds, queryString, vendorId, siteId]
            // );
        } else {
            let siteRecord = await awaitSafeQuery(`select * from maptile_building where mb_id =?`, [siteId]);
            let siteName = siteRecord.length > 0 ? siteRecord[0].mb_nickname:"-";
            let spsRecord = await awaitSafeQuery(
              `SELECT sps_querystring, sps_vendor_id, sps_contractor_id, sps_created_at, sps_modified_at, sps_report_status, sps_report_start_date, sps_report_end_date
                FROM sitefotos_postdated_services
                WHERE sps_querystring =? AND sps_vendor_id =?`,
              [queryString, vendorId]
            );
            //siteId, serviceIds, siteName,
            if (spsRecord.length > 0) {
                spsRecord = spsRecord[0];
                //It's the site that has been added to this report on email process so we will need to add row then update
                await insertObj(
                  'sitefotos_postdated_services',
                  { ...spsRecord,
                      sps_site_id: siteId,
                      sps_swsd_id: serviceIds,
                      sps_site_name: siteName
                  }
                )
              //   await awaitSafeQuery(
              //     `
              //     INSERT INTO sitefotos_postdated_services
              //         (sps_querystring, sps_vendor_id, sps_contractor_id, sps_created_at, sps_modified_at, sps_report_status, sps_report_start_date, sps_report_end_date, sps_site_id, sps_swsd_id, sps_site_name)
              //     SELECT sps_querystring, sps_vendor_id, sps_contractor_id, sps_created_at, sps_modified_at, sps_report_status, sps_report_start_date, sps_report_end_date, ?, ?, ?
              //     FROM sitefotos_postdated_services
              //     WHERE sps_querystring =? AND sps_vendor_id =?;
              // `,
              //     [siteId, serviceIds, siteName,queryString, vendorId]);
            }
        }
        res.status(200).json({success: 1});
    } catch (error) {
        next(error)
    }
}

const deleteService = async (req, res, next) => {
    try {
        let {vendorId} = await login(req.cookies.JWT)
        if (!vendorId) {
            res.status(401).send(err.message);
            return
        }
        let queryString = req.body.query_string;
        let serviceId = req.body.service_id;
        let siteId = req.body.site_id;

        await updateObj(
          'sitefotos_wp_services_data',
          {
            'swsd_active': 0,
            'swsd_service_status': 'REMOVEDEMAIL'
          },
          ['swsd_vendor_id', 'swsd_id'],
          [vendorId,serviceId]
        );

        let record = await awaitSafeQuery(`select * from sitefotos_postdated_services where sps_querystring=? and sps_vendor_id=? and sps_site_id=?`,[queryString, vendorId, siteId]);
        if (record && record.length > 0){
            let servicesArray = record[0].sps_swsd_id;
            let newArray = servicesArray.filter(item => item !== serviceId);
            if (newArray.length > 0){
                await updateObj(
                  'sitefotos_postdated_services',
                  {
                      'sps_swsd_id': newArray
                  },
                  ['sps_vendor_id', 'sps_querystring', 'sps_site_id'],
                  [vendorId,queryString,siteId]
                );
            } else {
                await deleteObj('sitefotos_postdated_services', ['sps_querystring', 'sps_vendor_id', 'sps_site_id'], [queryString, vendorId, siteId])
            }
        }
        res.status(200).json({success: 1});
    } catch (error) {
        next(error)
    }
}

const removeServicesFromPostdatedEmailWronglySent = async (req,res,next) => {
    try {
        let {vendorId} = await login(req.cookies.JWT)
        if (!vendorId) {
            res.status(401).send(err.message);
            return
        }
        const report = req.body;
        for (const reportElement of report) {
            const services = reportElement.services_fromwp.map(sfw => sfw.swsd_id);
            await updateObj('sitefotos_postdated_services', {sps_swsd_id: services}, ['sps_vendor_id', 'sps_querystring', 'sps_site_id', 'sps_contractor_id'], [vendorId, reportElement.sps_querystring, reportElement.sps_site_id, reportElement.sps_contractor_id]);
        }
        res.status(200).json({success: 1});
    } catch (error) {
        next(error)
    }
}

const clockInClockOut = async (req,res,next) => {
    try {
        // addClockInClockOut
        const { vendorId, internalUid } = await login(req.cookies.JWT, req.body?.accessCode);
        if (!vendorId) {
            res.status(401).send(err.message);
            return
        }
        const bcData = req.body;
        await service.addClockInClockOut(bcData, vendorId, internalUid);
        res.json("1")
    }
    catch (error) {
        next(error);
    }
}


const boservicesget = async (req, res, next) => {
    try {

      console.log(req.query?.accessCode);

      const { vendorId, internalUid, isHybrid, isClientViewEnabled, restrictedUser, userContactId } = await login(req.cookies.JWT, req.query?.accessCode);

      const forms = await awaitSafeQuery(/*SQL*/`SELECT sfcs_owner FROM( SELECT sf_id, sfcs_contractor, sfcs_owner, REPLACE(TRIM(']' FROM TRIM('[' FROM sf_form_site)), '\"', '') as sites FROM sitefotos_forms LEFT JOIN sitefotos_forms_contractor_share ON sf_id = sfcs_form_id and sfcs_contractor = ? WHERE (sf_vendor_id = ? OR sfcs_contractor = ?) AND sf_active = 1) FORMTABLESELECT WHERE sites != '-2' AND sites <> '' AND sfcs_owner IS NOT NULL AND sites IS NOT NULL`, [vendorId, vendorId, vendorId]);


      let vendors = [...new Set(forms.map((form) => parseInt(form.sfcs_owner)).filter((id) => !isNaN(id)))];
      vendors.push(vendorId);


      let hybrid = await awaitSafeQuery(/*SQL*/`SELECT 
         sf_form_sharer_vendor_id
     FROM 
         sitefotos_forms
     WHERE 
         sf_active = 1 AND sf_form_sharer_vendor_id is not null
         AND (sf_vendor_id = ? OR EXISTS (
             SELECT 1
             FROM sitefotos_forms_contractor_share
             WHERE sfcs_form_id = sf_id AND sfcs_contractor = ?
         ));`, [vendorId, vendorId]);

      hybrid = hybrid.map((row) => parseInt(row.sf_form_sharer_vendor_id)).filter((id) => !isNaN(id));
      vendors = Array.from(new Set([...vendors, ...hybrid]));
      if (isClientViewEnabled) {
        const qry = "SELECT DISTINCT (sscm_vendor_id) from sitefotos_site_client_mapping_extended_grouped where scs_client_vendor_id = ?";

        const clientViewVendors = await awaitQuery(qry, [vendorId]);
        const clientViewVendorIds = clientViewVendors.map(vendor => vendor.sscm_vendor_id);
        vendors = [...new Set([...vendors, ...clientViewVendorIds])];

      }

      const vendorsStr = vendors.join(',');

      const servicesResponse = await awaitSafeQuery(/*SQL*/`select vs_service_id, vs_service_name, vs_service_status, vs_service_description, vs_service_category, vs_provider, vs_service_options, IF(vs_vendor_id=?, '1', '0') as service_own,vs_service_quickbook_id, vs_service_type, vs_service_type_id, vs_trade_id,vs_equipment_id, vs_provider_id,sst_service_type,sst_trade_id, vs_material_id from vendor_services left join sitefotos_service_types on vs_service_type_id=sst_id where vs_vendor_id in (${vendorsStr}) AND NOT (vs_provider = 'BOSSLMTM' AND vs_service_status != '1')`, [vendorId])
      res.json(servicesResponse);

    } catch (error) {
      console.log(error.stack);
      next(error);
    }
  }
// New endpoint for getting total count of filtered services (for Gmail-style select all)
const getServiceHistoryCount = async (req, res, next) => {
    try {
        const { vendorId, restrictedUser, userContactId } = await login(req.cookies.JWT);
        const { start, end, filter, sort } = req.body;
        
        if (!vendorId) {
            return res.sendStatus(401);
        }
        
        if (!start || !end) {
            return res.status(400).json({ error: "Start and end date parameters are required." });
        }

        const filters = filter ? (typeof filter === 'string' ? JSON.parse(filter) : filter) : [];
        const sorters = sort ? (typeof sort === 'string' ? JSON.parse(sort) : sort) : [];

        // Get the filtered service IDs count
        const serviceIds = await getFilteredServiceIds(vendorId, restrictedUser, userContactId, filters, sorters, start, end);
        
        res.json({ 
            total: serviceIds.length,
            success: true 
        });
        
    } catch (error) {
        console.error("Error in getServiceHistoryCount:", error);
        next(error);
    }
};

// Job status endpoint for polling
const getJobStatus = async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const { jobId } = req.params;
        
        if (!vendorId) {
            return res.sendStatus(401);
        }
        
        if (!jobId) {
            return res.status(400).json({ error: "Job ID is required" });
        }
        
        const job = jobStore.get(jobId);
        
        if (!job) {
            return res.status(404).json({ 
                error: "Job not found",
                message: "Job may have completed and been cleaned up" 
            });
        }
        
        // Verify job belongs to this vendor
        if (job.vendorId && job.vendorId !== vendorId) {
            return res.status(403).json({ error: "Unauthorized access to job" });
        }
        
        // Calculate elapsed time and ETA if still processing
        if (job.status === 'processing' && job.startTime) {
            const elapsed = Date.now() - job.startTime;
            job.elapsedSeconds = Math.round(elapsed / 1000);
            
            if (job.processed > 0 && job.total > 0) {
                const rate = job.processed / elapsed;
                const remaining = job.total - job.processed;
                const eta = remaining / rate;
                job.etaSeconds = Math.round(eta / 1000);
            }
        }
        
        res.json(job);
        
        // Clean up completed/failed jobs after returning status
        if (job.status === 'completed' || job.status === 'failed' || job.status === 'completed_with_errors') {
            // Keep job for 1 more minute after it's been retrieved
            setTimeout(() => {
                const currentJob = jobStore.get(jobId);
                if (currentJob && (currentJob.status === 'completed' || currentJob.status === 'failed' || currentJob.status === 'completed_with_errors')) {
                    jobStore.delete(jobId);
                }
            }, 60000);
        }
        
    } catch (error) {
        console.error("Error in getJobStatus:", error);
        next(error);
    }
};

const getServiceHistoryFilterValues = async (req, res, next) => {
    try {
        const { vendorId, restrictedUser, userContactId } = await login(req.cookies.JWT);
        const { start: startDate, end: endDate } = req.query; // Unix timestamps

        if (!vendorId) {
            return res.sendStatus(401);
        }
        if (!startDate || !endDate) {
            return res.status(400).json({ error: "Start and end date parameters are required." });
        }

        const filterFields = [
            { name: 'Status', dbField: 'svc.swsd_service_status', alias: 'Status' },
            { name: 'Service', dbField: 'svc.swsd_service_name', alias: 'Service' },
            { name: 'TradeTitle', dbField: 'st.st_trade', alias: 'TradeTitle' },
            { name: 'InternalManager', dbField: `CONCAT(ic.sf_contact_fname, ' ', ic.sf_contact_lname)`, alias: 'InternalManager', requiresJoin: ['ic'] },
            { name: 'ProviderText', dbField: `CONCAT(mv.vendor_fname, ' ', mv.vendor_lname)`, alias: 'ProviderText', requiresJoin: ['mv'] },
            { name: 'Client', dbField: 'icc.sf_contact_company_name', alias: 'Client', requiresJoin: ['icc'] },
            { name: 'swsd_event', dbField: 'svc.swsd_event', alias: 'swsd_event' },
            { name: 'Source', dbField: 'svc.swsd_service_source', alias: 'Source' },
            { name: 'Zone', dbField: 'gz.sgz_geo_zone', alias: 'Zone', requiresJoin: ['gz'] }
        ];

        let baseFromClause = `
            FROM sitefotos_wp_services_data svc
            LEFT JOIN maptile_building mb ON mb.mb_id = svc.swsd_site_id
        `;
        // Dynamically add joins based on required fields to optimize
        const requiredJoins = new Set();
        filterFields.forEach(f => {
            if (f.requiresJoin) f.requiresJoin.forEach(j => requiredJoins.add(j));
        });

        if (requiredJoins.has('st') || filterFields.some(f => f.dbField === 'st.st_trade')) {
            baseFromClause += ` LEFT JOIN vendor_services vs ON vs.vs_service_id = svc.swsd_service_id LEFT JOIN sitefotos_trades st ON st.st_id = vs.vs_trade_id`;
        }
        if (requiredJoins.has('ic') || filterFields.some(f => f.dbField.includes('ic.'))) {
            baseFromClause += ` LEFT JOIN sitefotos_contacts ic ON ic.sf_contact_id = mb.mb_manager`;
        }
        if (requiredJoins.has('mv') || filterFields.some(f => f.dbField.includes('mv.'))) {
            baseFromClause += ` LEFT JOIN maptile_vendors mv ON mv.vendor_id = svc.swsd_uploader_vid`;
        }
        if (requiredJoins.has('icc') || filterFields.some(f => f.dbField.includes('icc.'))) {
            baseFromClause += ` LEFT JOIN sitefotos_contacts_company_view icc ON icc.sf_contact_id = mb.mb_client`;
        }
         if (requiredJoins.has('gz') || filterFields.some(f => f.dbField === 'gz.sgz_geo_zone')) {
            baseFromClause += ` LEFT JOIN sitefotos_geofence_zones gz ON gz.sgz_zone_id = mb.mb_zone_id`;
        }


        let baseWhereClauses = [
            `(svc.swsd_vendor_id = ? OR svc.swsd_uploader_vid = ?)`,
            `svc.swsd_date_time >= ?`,
            `svc.swsd_date_time < ?`,
            `svc.swsd_service_status != 'TEMPLOG'`
        ];
        let baseQueryParams = [vendorId, vendorId, startDate, endDate];

        if (restrictedUser && userContactId > 0) {
            baseWhereClauses.push(`(mb.mb_manager = ? OR mb.mb_manager_secondary = ? OR mb.mb_manager_third = ?)`);
            baseQueryParams.push(userContactId, userContactId, userContactId);
        }

        const whereCondition = baseWhereClauses.join(' AND ');
        const results = {};

        for (const field of filterFields) {
            if (field.dbField.toLowerCase().includes('concat(') && !requiredJoins.has(field.requiresJoin[0])) {
                 // Skip if a CONCAT field's required join wasn't added (e.g. if no other field needed it)
                 // This is a simplification; ideally, joins are added if any part of the CONCAT needs them.
            }

            const distinctQuery = `SELECT DISTINCT ${field.dbField} AS value ${baseFromClause} WHERE ${whereCondition} AND ${field.dbField} IS NOT NULL AND TRIM(${field.dbField}) != '' ORDER BY value ASC`;
            const distinctValues = await awaitQuery(distinctQuery, baseQueryParams);
            results[field.alias] = distinctValues.map(row => row.value);
        }

        res.json(results);

    } catch (error) {
        console.error("Error in getServiceHistoryFilterValues:", error);
        next(error);
    }
};

module.exports = {
    getTrades,
    getEquipment,
    getServiceTypes,
    getServiceHistoryDashboard,
    updateSubmittedService,
    changeServiceStatus,
    addUpdateServiceNotes,
    sendServicesPostdated,
    loadEmailData,
    updateReportEmail,
    addNewServicesToReportEmail,
    deleteService,
    removeServicesFromPostdatedEmailWronglySent,
    getWorkerHistoryDashboard,
    updatedAdjustedCheckInOutForWorkerPayroll,
    updateWorkerPayrollLineItems,
    clockInClockOut,
    boservicesget,
    getServiceHistoryDashboard2,
    getServiceHistoryFilterValues,
    getServiceHistoryCount,
    getJobStatus
}
