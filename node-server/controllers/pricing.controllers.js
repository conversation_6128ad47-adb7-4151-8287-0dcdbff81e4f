const {await<PERSON><PERSON>y, insertObj, updateObj, awaitSafe<PERSON><PERSON>y,deleteObj} = require('../utils/db')
const {login} = require("../utils/vendor");
const pricingServices = require("../services/pricing.services");
const moment = require("moment");

const getPricing = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        let parentId = req.params.parentid;

        let result = {
            "parent": {},
            "pricingRows": []
        }
        let parentPricing = {};

        if (parentId && parentId > 0){
            //When opened from dashboard
            parentPricing = await awaitSafeQuery(
              `select * from sitefotos_pricing_contracts where spc_id=?;`,
              [parentId]
            );
        } else {
            parentPricing = await awaitSafeQuery(
              `select * from sitefotos_pricing_contracts where spc_vendor_id=? and spc_site_id=? and spc_contract_active = 1 and spc_contract_for=? and spc_st_trade_id=? LIMIT 1;`,
              [vendorId, req.params.id, req.params.contractfor, req.params.tradeid]
            );
        }

        if (parentPricing.length > 0) {
            let parentId = parentPricing[0].spc_id;
            result.parent = parentPricing[0];
            result.pricingRows = await awaitSafeQuery(
              `select
                       sps_id as id,
                       sps_price as price,
                       sps_partial_price as partial_price,
                       sps_contract_type as contractType,
                       sps_service_option_trigger as contractDropDownType,
                       sps_snow_trigger_start as snowThresholdStart,
                       sps_snow_trigger_end as snowThresholdEnd,
                       sps_service_type as task,
                       sps_service_number_of_payments as numberOfPayments,
                       sps_season_start_date as startDate,
                       sps_season_stop_date as endDate,
                       sps_service_used_on_site as serviceChecked,
                       sps_contract_for as contractFor,
                       sps_pricing_base as pricingBaseOrUnit,
                       sps_service_type_id as serviceTypeId,
                       sps_equipmentservice_id as equipmentServiceId,
                       sps_material_id as materialId,
                       sps_contractors as contractors,
                       sps_sesl_id as selectedServiceLevelId,
                       sps_trigger_depth as triggerDepth,
                       sps_minimum_minutes as minimum_minutes,
                       sps_snow_season_hi as seasonal_hi,
                       sps_snow_season_lo as seasonal_lo,
                       sps_use_seasonal_values as use_seasonal_values
                   from sitefotos_pricing_structure
                   where sps_spc_id=? and sps_vendor_id=?`,
              [parentId, vendorId]
            );
        } else {
            result = {
                "parent": {},
                "pricingRows": []
            }
        }
        res.json(result);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
};

const getPricingPerContractType = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const getPricingSql = `select
                sps_id as id,
                sps_price as price,
                sps_partial_price as partial_price,
                sps_contract_type as contractType,
                sps_service_option_trigger as contractDropDownType,
                sps_snow_trigger_start as snowThresholdStart,
                sps_snow_trigger_end as snowThresholdEnd,
                sps_service_type as task,
                sps_service_number_of_payments as numberOfPayments,
                sps_season_start_date as startDate,
                sps_season_stop_date as endDate,
                sps_service_used_on_site as serviceChecked,
                sps_contract_for as contractFor,
                sps_equipmentservice_id as equipmentServiceId,
                sps_material_id as materialId
            from sitefotos_pricing_structure
            where sps_vendor_id=? and sps_service_option_trigger=? and sps_site_id=? and sps_contract_for=?
        `;
        const pricings = await awaitQuery(getPricingSql, [vendorId, req.params.contract, req.params.id, req.params.contractfor]);
        const result = { pricings };
        res.json(result);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
};

const getPricingContractFor = async (req, res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const getPricingSql = `select
                sps_id as id,
                sps_price as price,
                sps_partial_price as partial_price,
                sps_contract_type as contractType,
                sps_service_option_trigger as contractDropDownType,
                sps_snow_trigger_start as snowThresholdStart,
                sps_snow_trigger_end as snowThresholdEnd,
                sps_service_type as task,
                sps_service_number_of_payments as numberOfPayments,
                sps_season_start_date as startDate,
                sps_season_stop_date as endDate,
                sps_service_used_on_site as serviceChecked,
                sps_contract_for as contractFor,
                sps_equipmentservice_id as equipmentServiceId,
                sps_material_id as materialId
            from sitefotos_pricing_structure
            where sps_vendor_id=? and sps_service_option_trigger=? and sps_site_id=? and sps_contract_for=?
        `;
        const pricings = await awaitQuery(getPricingSql, [vendorId, req.params.contract, req.params.id, req.params.contractfor]);
        const result = { pricings };
        res.json(result);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
};

const addPricingContract = async (req, resp, next) => {
    //This method is getting used where we will pass complete contract to it and it will store it in the database
    try {
        const {vendorId} = await login(req.cookies.JWT);
        let contract = req.body;
        let lastId = 0;
        let lastRowOfThisVendorInPricingContracts = await awaitSafeQuery(`select * from sitefotos_pricing_contracts where spc_vendor_id=? order by spc_id desc limit 1`, [vendorId]);

        if (lastRowOfThisVendorInPricingContracts.length > 0){
            lastId = lastRowOfThisVendorInPricingContracts[0].spc_id + 1;
        }

        let parent = contract.parent;
        //Contract name for now will be autogenerated here.
        let contractName = '';
        let triggerDepth = contract.triggerDepth;
        let serviceLevelId = contract.serviceLevelId
        let contractorsSelected = contract.contractorsSelected;
        let selectedTrade = contract.selectedTrade;
        let start = contract.contractStartDateInUnix;
        let end = contract.contractEndDateInUnix;
        let site = contract.siteId;
        let contractFor = contract.contractFor;
        let pricingData = contract.contract;
        let tradeId = contract.tradeId;
        let copyMode = contract.copy;
        let response;

        let sLevelsDb;
        if ( serviceLevelId === undefined ) {
            //Service Level cannot be undefined, adding check here and if it is coming undefined then fetch them from db for the vendor.
            sLevelsDb = await awaitSafeQuery(`select sesl_id from sitefotos_estimator_service_levels where sesl_vendor_id = ? limit 1`, [vendorId]);
            if (sLevelsDb) {
                serviceLevelId = sLevelsDb[0].sesl_id;
            }
        }

        if (contractFor == 'CLIENT'){
            contractName = `${lastId}-${contract.siteName}${contract.clientName}`;
        } else {
            contractName = `${lastId}-${contract.siteName}-CONTRACTOR`;
        }

        if (Object.entries(parent).length === 0 || copyMode){

            //Deactivate any old client pricing for this site if the contract to be added is of type client
            if (contractFor == 'CLIENT')
            {
                await updateObj('sitefotos_pricing_contracts',
                  { spc_contract_active: 0 },
                  ['spc_vendor_id', 'spc_site_id', 'spc_contract_for', 'spc_st_trade_id'],
                  [vendorId, site, 'CLIENT', tradeId]
                );
            }

            //Check if contract exists for the same contractor and deactivate it
            if (contractFor == 'CONTRACTOR')
            {
                await updateObj('sitefotos_pricing_contracts',
                  { spc_contract_active: 0 },
                  ['spc_vendor_id', 'spc_site_id', 'spc_contract_for', 'spc_st_trade_id', 'spc_contractors'],
                  [vendorId, site, 'CONTRACTOR', tradeId, JSON.stringify(contractorsSelected)]
                );
            }

            //Insertion
            let parentObj = {
                spc_vendor_id: vendorId,
                spc_name: contractName,
                spc_sesl_id: serviceLevelId,
                spc_contractors: contractorsSelected,
                spc_st_trade_id: tradeId,
                spc_contract_for: contractFor,
                spc_contract_active: 1,
                spc_season_start_date: start,
                spc_season_stop_date: end,
                spc_site_id: site,
                spc_trigger_depth: triggerDepth
            };

            let insertedId = (await insertObj('sitefotos_pricing_contracts', parentObj)).insertId

            for (const pricingObj of pricingData) {

                const pricing = {
                    sps_vendor_id: vendorId,
                    sps_site_id: site,
                    sps_contract_type: "",
                    sps_price: pricingObj.price,
                    sps_snow_trigger_start: pricingObj.lo,
                    sps_snow_trigger_end: pricingObj.hi,
                    sps_season_start_date: start,
                    sps_season_stop_date: end,
                    sps_pricing_trigger: '',
                    sps_pricing_base: pricingObj.unit,
                    sps_partial_price: 0,
                    sps_service_option_trigger: pricingObj.unit,
                    sps_service_type_id: pricingObj.service,
                    sps_service_number_of_payments: 0,
                    sps_service_used_on_site: 1,
                    sps_contract_for: contractFor,
                    sps_equipmentservice_id: pricingObj.equipment == undefined ? null:pricingObj.equipment,
                    sps_material_id: pricingObj.materials == undefined ? null:pricingObj.materials,
                    sps_spc_id: insertedId,
                    sps_minimum_minutes: pricingObj.minimum_minutes_converted,
                    sps_snow_season_hi: pricingObj.seasonal_hi ?? 0,
                    sps_snow_season_lo: pricingObj.seasonal_lo ?? 0
                };
                await insertObj('sitefotos_pricing_structure', pricing);
            }

            response = {
                message: "Contract has been added.", success: 1, id: insertedId
            }
        }
        else {
            //Update
            //First update any changes made to parent
            parent.spc_contractors= contractorsSelected;
            parent.spc_season_start_date = start;
            parent.spc_season_stop_date = end;
            parent.spc_sesl_id = serviceLevelId;
            parent.spc_trigger_depth = triggerDepth;
            await updateObj('sitefotos_pricing_contracts', parent,
              ['spc_id', 'spc_vendor_id'], [parent.spc_id, vendorId]
            );
            //Now iterate and update contract rows
            for (const pricingObj of pricingData) {
                const pricing = {
                    sps_vendor_id: vendorId,
                    sps_site_id: site,
                    sps_contract_type: "",
                    sps_price: pricingObj.price,
                    sps_snow_trigger_start: pricingObj.lo,
                    sps_snow_trigger_end: pricingObj.hi,
                    sps_season_start_date: start,
                    sps_season_stop_date: end,
                    sps_pricing_trigger: '',
                    sps_pricing_base: pricingObj.unit,
                    sps_partial_price: 0,
                    sps_service_option_trigger: pricingObj.unit,
                    sps_service_type_id: pricingObj.service,
                    sps_service_number_of_payments: 0,
                    sps_service_used_on_site: 1,
                    sps_contract_for: contractFor,
                    sps_equipmentservice_id: pricingObj.equipment == undefined ? null:pricingObj.equipment,
                    sps_material_id: pricingObj.materials == undefined ? null:pricingObj.materials,
                    sps_spc_id: parent.spc_id,
                    sps_sesl_id: serviceLevelId,
                    sps_trigger_depth: triggerDepth,
                    sps_minimum_minutes: pricingObj.minimum_minutes_converted,
                    sps_snow_season_hi: pricingObj.seasonal_hi ?? 0,
                    sps_snow_season_lo: pricingObj.seasonal_lo ?? 0
                };
                if (pricingObj.dbRowId > 0){
                    //Update
                    await updateObj('sitefotos_pricing_structure', pricing,
                      ['sps_id', 'sps_vendor_id'], [pricingObj.dbRowId, vendorId]
                    );
                } else {
                    //Insert
                    await insertObj('sitefotos_pricing_structure', pricing);
                }
            }

            response = {
                message: "Contract has been updated.", success: 1, id: parent.spc_id
            }
        }

        resp.json(response);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
};

const addPricing = async (req, resp, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const pricing = {
            sps_vendor_id: vendorId,
            sps_site_id: req.params.id,
            sps_contract_type: req.body.contractType,
            sps_price: req.body.price,
            sps_snow_trigger_start: req.body.snowThresholdStart,
            sps_snow_trigger_end: req.body.snowThresholdEnd,
            sps_season_start_date: req.body.startDate,
            sps_season_stop_date: req.body.endDate,
            sps_pricing_trigger: 'SERVICE',
            sps_pricing_base: req.body.pricingBase != undefined ? req.body.pricingBase : null,
            sps_partial_price: req.body.partialPrice,
            sps_service_option_trigger: req.body.optionTrigger,
            sps_service_type: req.body.serviceType,
            sps_service_number_of_payments: req.body.numberOfPayments,
            sps_rowindex_from_datatable: req.body.rowIndex,
            sps_service_used_on_site: req.body.serviceChecked,
            sps_contract_for: req.body.contractFor,
            sps_extra_data: req.body.extraData,
            sps_equipmentservice_id: req.body.equipmentServiceId != undefined ? req.body.equipmentServiceId : 0
        };
        const result = await insertObj('sitefotos_pricing_structure', pricing);
        resp.json(result);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
};

const updatePricing = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const pricing = {
            sps_contract_type: req.body.contractType,
            sps_price: req.body.price,
            sps_snow_trigger_start: req.body.snowThresholdStart,
            sps_snow_trigger_end: req.body.snowThresholdEnd,
            sps_season_start_date: req.body.startDate,
            sps_season_stop_date: req.body.endDate,
            sps_pricing_trigger: 'SERVICE',
            sps_partial_price: req.body.partialPrice,
            sps_service_option_trigger: req.body.optionTrigger,
            sps_service_number_of_payments: req.body.numberOfPayments,
            sps_service_used_on_site: req.body.serviceChecked,
            sps_contract_for: req.body.contractFor,
            sps_extra_data: req.body.extraData
        };
        await updateObj('sitefotos_pricing_structure', pricing,
          ['sps_id', 'sps_vendor_id'], [req.params.id, vendorId]
        );
        res.json(true);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
}

const updateService = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        await updateObj(
          'sitefotos_wp_services_data',
          {
              'swsd_snow_inch': req.body.snow_inch
          },
          ['swsd_id', 'swsd_vendor_id'],
          [req.params.id, vendorId]
        );
        res.json(true);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
}

const updateHours = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        await updateObj(
          'sitefotos_wp_services_data',
          {
              'swsd_hours': req.body.hours
          },
          ['swsd_id', 'swsd_vendor_id'],
          [req.params.id, vendorId]
        );
        res.json(true);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
}

const updateContractStatus = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        await updateObj(
          'sitefotos_pricing_structure',
          {
              sps_contract_active: 0
          },
          ['sps_service_option_trigger','sps_vendor_id'],
          [req.body.old_service, vendorId]
        );
        await updateObj(
          'sitefotos_pricing_structure',
          {
              sps_contract_active: 1
          },
          ['sps_service_option_trigger','sps_vendor_id'],
          [req.body.new_service, vendorId]
        );
        res.json(true);
    } catch (ex) {
        console.error(ex);
        next(ex)
    }
}

const getUserEquipmentAndServices = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const equipments = await awaitSafeQuery(`SELECT * from sitefotos_estimator_services_equipment_data where sesed_vendor_id =?`, [vendorId]);
        const materials = await awaitSafeQuery(`SELECT * from sitefotos_estimator_materials_data where semd_vendor_id=?`, [vendorId]);
        res.json({
            equipments: equipments,
            materials: materials
        });
    } catch (ex) {
        next(ex)
    }
}

const getAllPricingContractsOld = async (req,res,next)=>{
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const contracts = await awaitSafeQuery(`
            select * from sitefotos_pricing_structure sps
                     inner join maptile_building mb on mb.mb_id = sps.sps_site_id
                     where sps_vendor_id=? and mb.mb_status != '0'`,
          [vendorId]);
        res.json(contracts)
    } catch (ex) {
        next(ex)
    }
}

const getAllPricingContracts = async (req,res,next)=>{
    try {
        const {vendorId, restrictedUser, userContactId } = await login(req.cookies.JWT); //
        const contracts = await awaitSafeQuery(`
            select * from sitefotos_pricing_contracts spc
                     inner join maptile_building mb on mb.mb_id = spc.spc_site_id
                     where spc.spc_vendor_id=? and mb.mb_status != '0' ${restrictedUser && userContactId>0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''}  order by spc.spc_created desc `,
          [vendorId]);
        res.json(contracts)
    } catch (ex) {
        next(ex)
    }
}

const addUpdatePricingContractName = async (req,res,next)=>{
    try {
        const {vendorId} = await login(req.cookies.JWT);
        let contractName = req.body.contract_name;
        let contractId = req.body.contract_id;

        let obj = {
            spc_name: contractName
        };
        await updateObj('sitefotos_pricing_contracts', obj,
          [
              'spc_id',
              'spc_vendor_id'
          ],
          [
              contractId,
              vendorId
          ]
        );
        res.json(1)
    } catch (ex) {
        next(ex)
    }
}

const updatePricingContractStatus = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        let contracts = req.body.contracts;
        let status = req.body.status;
        for (const contract of contracts) {
            if (status === 1){
                //Deactivate any active contract for this site
                await updateObj(
                  'sitefotos_pricing_contracts',
                  {
                      spc_contract_active: 0
                  },
                  ['spc_vendor_id', 'spc_site_id', 'spc_contract_for', 'spc_contract_active'],
                  [vendorId, contract.spc_site_id, contract.spc_contract_for, 1]
                );
            }
            await updateObj(
              'sitefotos_pricing_contracts',
              {
                  spc_contract_active: status
              },
              ['spc_vendor_id', 'spc_id'],
              [vendorId,contract.spc_id]
            );
        }
        res.json(1)
    } catch (ex) {
        next(ex)
    }
}

const deletePricingRow = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const rowId = req.body.row_id;
        await deleteObj('sitefotos_pricing_structure', ['sps_id', 'sps_vendor_id'], [rowId,vendorId]);

        res.json(1)
    } catch (ex) {
        next(ex)
    }
}

const addBulkPricing = async (req, res) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);

        const data = req.body.data;
        const vendor = vendorId;
        const contractFor = req.body.contractFor;
        const serviceLevelId = req.body.serviceLevelId;
        const source = req.body.source;
        const contractors = req.body.contractors;

        if (data.length === 0) {
            res.json("1");
            return;
        }

        res.json("We are adding contracts in background");
        // Group data based on site id to convert it to single contracts
        const groupedData = data.reduce((result, item) => {
            const SiteId = item.SiteId;
            if (!result[SiteId]) {
                result[SiteId] = [];
            }
            result[SiteId].push(item);
            return result;
        }, {});

        const subarrays = Object.values(groupedData);

        for (const subarray of subarrays) {
            const start = subarray[0]['Start Date'];
            const end = subarray[0]['End Date'];
            // const contractors = [];

            // Fetch site details and apply error handling
            const siteDetails = await awaitSafeQuery(
              `select * from maptile_building where mb_id = ? and mb_user_id =?`,
              [subarray[0].SiteId, vendor]
            );

            if (siteDetails.length === 0) {
                // Handle the case where no site details are found
                continue;
            }

            // Fetch service levels and apply error handling
            const serviceLevel = await awaitSafeQuery(
              `select * from sitefotos_estimator_service_levels sesl where sesl_vendor_id =? limit 1`,
              [vendor]
            );

            if (serviceLevel.length === 0) {
                // Handle the case where no service level is found
                continue;
            }

            const contract = {
                copy: true,
                siteName: siteDetails[0].mb_nickname,
                clientName: "",
                parent: [],
                triggerDepth: 0,
                source: source,
                serviceLevelId: serviceLevelId,
                contractorsSelected: contractors,
                selectedTrade: 1, // Snow for now
                contractStartDateInUnix: start,
                contractEndDateInUnix: end,
                siteId: siteDetails[0].mb_id,
                contract: [],
                contractFor: contractFor,
                tradeId: 1
            };

            // Use a contractId -
            let contractId = 0;

            for (const subarrayElement of subarray) {
                // Fetch service type and apply error handling
                let serviceType = await awaitSafeQuery(
                  `select * from sitefotos_service_types where sst_service_type =?`,
                  [subarrayElement.Service]
                );

                if (serviceType.length === 0) {
                    // Handle the case where no service type is found
                    serviceType.sst_id = 100;
                }

                contract.contract.push({
                    dbRowId: 0,
                    equipment: parseInt(subarrayElement.Equipment),
                    hi: parseFloat(subarrayElement.Hi).toFixed(2),
                    id: contractId,
                    lo: parseFloat(subarrayElement.Lo).toFixed(2),
                    price: parseFloat(subarrayElement.Price),
                    service: serviceType[0].sst_id,
                    unit: subarrayElement.Unit
                });

                // Increment the contractId
                contractId++;
            }

            await pricingServices.addPricingContract(vendor, contract);
        }

    } catch (error) {
        // Handle any errors that occur during execution
        console.error('Error:', error);
        res.status(500).json({ error: error.message });
    }
};

const generatePricingGoogleSheet = async(req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const sitesData = req.body.sitesData;
        const apiCall = await fetch(`https://script.google.com/macros/s/AKfycbwI8wO9mBfgl2q3xc8CSC1c1htH9iyjP6qInVwPuQIlAZhkdZk5YSUKfyHBpuFn0Se4/exec`,
          {
              method: "POST",
              headers: {
                  "Content-Type": "application/json"
              },
              body: JSON.stringify(sitesData)
          });
        if (apiCall.ok){
            const sheetId = await apiCall.text();
            res.json(sheetId);
        }
        else {
            res.json(null);
        }
    }
    catch (ex) {
        next(ex);
    }
}

const getDataFromPricingGoogleSheet = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const sheetId = req.body.sheet_id; //
        const apiCall = await fetch(`https://script.google.com/macros/s/AKfycbw9cVHxDAlmhzyV0GvEvmodJfAbSoRXjC9egIInaH3GcXgYPlUC5zHIqoSM5eicv0FoeA/exec`,
          {
              method: "POST",
              headers: {
                  "Content-Type": "application/json"
              },
              body: JSON.stringify({
                  "sheet_id": sheetId
              })
          });
        if (apiCall.ok){
            const data = await apiCall.json();
            res.json(data);
        }
        else {
            res.json([]);
        }
    }
    catch (ex) {
        next(ex);
    }
}

const getContactServicePricing = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const contactId = req.params.contactId;
        const data = await pricingServices.contactServicePriceFromDb(vendorId, contactId);
        res.json(data);
    }
    catch (ex) {
        next(ex);
    }
}

const addContactServicePricing = async ( req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const request = req.body;
        //TODO: debug cases where it is coming as null, urgent
        await pricingServices.addContactServicePricing(
          vendorId,
          request.vs_trade_id,
          request.vs_service_type_id ?? 0,
          request.vs_equipment_id ?? 0,
          request.contactId,
          request.price ?? 0,
          parseInt(request.vs_trade_id)  == 0 ? 'DEFAULT':'CUSTOM' ,
          request.contactType
        );

        res.json(1);
    }
    catch (ex) {
        next(ex);
    }
}

const updateContractPricing = async ( req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const request = req.body;
        await updateObj(
          'sitefotos_contact_service_pricing',
          {
              scsp_vendor_id: vendorId,
              scsp_trade_id: request.vs_trade_id,
              scsp_vendor_services_service_type_id: request.vs_service_type_id == null ? 0: request.vs_service_type_id,
              scsp_equipment_id: request.vs_equipment_id == null ? 0: request.vs_equipment_id,
              scsp_contact_id: request.contactId,
              scsp_price: request.price,
          },
          ['scsp_id', 'scsp_vendor_id'],
          [request.dbId, vendorId]
        );
        res.json(1);
    }
    catch (e) {
        next(e);
    }
}

const deleteServicePricing = async (req, res, next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const request = req.body;
        await updateObj('sitefotos_contact_service_pricing', {scsp_active: false}, ['scsp_id', 'scsp_vendor_id'], [request.id, vendorId]);
        res.json(1);
    }
    catch (e) {
        next(ex);
    }
}

const getContactDefaultServicePricing = async (req,res, next)=> {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const contactId = req.params.contactId;
        const data = await pricingServices.contactDefaultServicePriceFromDb(vendorId, contactId);
        res.json(data);
    }
    catch (ex) {
        next(ex);
    }
}

const addUpdateContactDefaultServicePricing = async (req,res,next) => {
    try {
        const {vendorId} = await login(req.cookies.JWT);
        const request = req.body;

        const defaultPriceForContactAndTrade
          = await pricingServices.contactDefaultServicePriceFromDbByTrade(vendorId, request.contactId, request.tradeId);
        if (defaultPriceForContactAndTrade.length === 0) {
            await pricingServices.addContactServicePricing(
              vendorId,
              request.tradeId,
              0,
              0,
              request.contactId,
              request.price,
              'DEFAULT',
              request.contactType
            )
        } else {
            //Update
            await updateObj(
              'sitefotos_contact_service_pricing',
              {'scsp_price': request.price},
              ['scsp_vendor_id', 'scsp_contact_id', 'scsp_trade_id', 'scsp_price_type'],
              [vendorId, request.contactId, request.tradeId, 'DEFAULT']
            );
        }

        res.json(1);
    }
    catch (ex) {
        next(ex);
    }
}

module.exports = {
    getPricing,
    addPricing,
    updateService,
    updateHours,
    updatePricing,
    updateContractStatus,
    getPricingPerContractType,
    getPricingContractFor,
    getUserEquipmentAndServices,
    addPricingContract,
    getAllPricingContractsOld,
    getAllPricingContracts,
    addUpdatePricingContractName,
    updatePricingContractStatus,
    deletePricingRow,
    addBulkPricing,
    generatePricingGoogleSheet,
    getDataFromPricingGoogleSheet,
    getContactServicePricing,
    addContactServicePricing,
    deleteServicePricing,
    updateContractPricing,
    getContactDefaultServicePricing,
    addUpdateContactDefaultServicePricing
}
