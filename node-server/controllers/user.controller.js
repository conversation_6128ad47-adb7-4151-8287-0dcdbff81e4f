const userServices = require('../services/api/v2/users-services');
const { createUserSchemaAPI, patchUserSchemaAPI } = require('./schemas/users');
const { login } = require('../utils/vendor');
const { z } = require('zod');

const createUser= async (req, res, next) => {
    try {
        const { vendorId } = await login(req.cookies.JWT);
        const userSchema = createUserSchemaAPI(vendorId);
        const parsedQuery = await userSchema.parseAsync(req.body);

        const newUser = await userServices.createUser(vendorId, parsedQuery)
        res.status(201).json(newUser)
    } catch (error) {
        if (error instanceof z.ZodError) {
            res.status(400).json({ error: error.errors });
        } else {
            console.error('Error in createUser:', error);
            next(error)
        }
    }
}



const updateUser = async (req, res) => {

    try {
        const { vendorId } = await login(req.cookies.JWT);
        const userId = req.params.user_id;
        const userSchema = patchUserSchemaAPI(vendorId);
        const userQuery = await userSchema.parseAsync(req.body);

        const patchedUser = await userServices.patchUser(vendorId, userId, userQuery)
        if (patchedUser) {
            res.status(200).json(patchedUser)
        } else {
            res.status(404).json({ error: 'User not found' })
        }
    } catch (error) {
        if (error instanceof z.ZodError) {
            res.status(400).json({ error: error.errors });
        } else {
            console.error('Error in updateUser:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
}
module.exports = {
    createUser,
    updateUser
}


