const mysql = require('../utils/db');
let PHPUnserialize = require('php-unserialize');
const { redisClient } = require('../utils/redis-client.js')
;
const moment = require("moment/moment");

const getPricingStructure = async(vendorId, contractFor) => {
    let results = await mysql.awaitQuery(`select * from sitefotos_pricing_structure 
         where sps_vendor_id = ? and sps_contract_active = '1' and sps_contract_for = ?`, [vendorId,contractFor]);
    return results;
}

const getContractorDetailsFromVendorTable = async (contractorId) => {
    let results = await mysql.awaitQuery(`select mv.vendor_id from maptile_vendors as mv
                inner join sitefotos_contacts as sf on mv.vendor_access_code = sf.sf_contact_contractorid
                where sf.sf_contact_id = ?`, [contractorId]);
    return results;
}
const getHybridUserSites = async (userId) => {
    const userDetails = await mysql.awaitSafeQuery(`Select modified_at, vuc_data from verizon_user_configuration where vuc_user_id=?`, [userId]);

    if (userDetails.length > 0) {
        const cacheKey = `getHybridUserSites:${userId}:${userDetails[0].modified_at}`;
        const cachedData = await redisClient.get(cacheKey);

        if (cachedData) return JSON.parse(cachedData);
        const sites = JSON.parse(PHPUnserialize.unserialize(userDetails[0].vuc_data));
        const buildingIds = sites.map((row) => row.mb_id);
        let siteDetails = await mysql.awaitQuery(`SELECT mb_nickname,mb_external_id, mb_external_src, mb_vendor_internal_id, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code,mb_lat,mb_long, (select City from maptile_city where CityId = mb_city) as cityname,  (select state_abv_name from maptile_state where id = mb_state ) as statename,GROUP_CONCAT(IFNULL(mb_contract_type, 'No Trade') order by mb_id separator ';') as mb_contract_type,mb_vendor_internal_id,GROUP_CONCAT(vendor_company_name order by mb_id separator ',') as contractors, GROUP_CONCAT(mb_id order by mb_id separator  ',') AS site_id from maptile_building left join maptile_vendors on mb_user_id = vendor_id   where mb_id in (?) group by mb_vendor_internal_id`, [buildingIds]);

        
        redisClient.set(cacheKey, JSON.stringify(siteDetails), { 'EX': 60 * 60 * 24 * 3 });
        return siteDetails;
    }
    return [];
}

const getPricingForAllContractors = async (vendorId, startDate, endDate) => {
    let results = await mysql.awaitQuery(`select
              swsd_id as service_id,
              mb_nickname as site_name,
              mb_id as site_id,
              swsd_service_name as service_name,
              from_unixtime(svc.swsd_date_time) as time,
              swsd_snow_inch as snow_inch,
              swsd_hours as hours,
              swsd_people as people,
              swsd_service_id as s_id,
              swsd_uploader_vid as contractor_vid,
              vendor_email as contractor_email,
              CONCAT(vendor_fname," ", vendor_lname) as contractor_name,
              vendor_company_name as company_name,
              vendor_company_logo as company_logo,
              vs_service_type as connected_service,
              vs_equipment_id as equipment_id,
              swsd_service_options as options
          from sitefotos_wp_services_data svc
              inner join maptile_building
                  on mb_id=svc.swsd_site_id
              inner join maptile_vendors
                  on vendor_id=svc.swsd_uploader_vid
              inner join vendor_services
                  on vs_service_id = svc.swsd_service_id
          where swsd_vendor_id=?
            and swsd_date_time >= ?
            and swsd_date_time < ?
          order by svc.swsd_date_time`, [vendorId, startDate, endDate]);

    return results;
}

const getDeicePrice = (serviceOptions, fullPrice, partialPrice) =>
{
    if(serviceOptions == null || serviceOptions == "null")
        return fullPrice;
    let data = PHPUnserialize.unserialize(serviceOptions)

    if(data.length>0)
    {
        if(data[0].toString().toLowerCase() == "full")
            return fullPrice;
        else if(data[0].toString().toLowerCase() == "partial")
            return partialPrice
        else
            return fullPrice
    }
    else
        return fullPrice
}

const getSnowDataOptions = (serviceOptions) =>
{
    if(serviceOptions == null || serviceOptions == "null"){
        serviceOptions = 'a:1:{i:0;s:3:"0-4";}';
    }
    serviceOptions = 'a:1:{i:0;s:3:"0-4";}'; //This needs to be removed
    let data = PHPUnserialize.unserialize(serviceOptions)[0].replace(/["]+/g,'').split('-')
    if(data.length > 1){
        return Math.min(data[1],data[0])
    }
    else
        return data[0];
}

const postFormToTableReportToGoogleSheets = async (vendorId, data, sheetName) => {
    let url = "https://script.google.com/macros/s/AKfycbx_4YGnEg2V5MSElJbyiAhdlsxqPy250tJOcZyh_GFFrTcpoiol-LdQfn-m0refz-jL/exec";
    console.log("***SErvice***", JSON.stringify(data))
    let postToGoogle = await fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "user_id": vendorId,
          "sheet_name": sheetName,
          "data": data
        })
    });
    postToGoogle = await postToGoogle.text();
    return postToGoogle;
}

const fetchSitesWithNoServicesInGivenTimePeriod = async (vendorId, startUnix, endUnix) =>{
    let results = await mysql.awaitSafeQuery(
      /*SQL*/`SELECT mb.mb_id AS SiteID, mb.mb_nickname AS Name, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb.mb_zone_id) as SiteZone FROM maptile_building mb LEFT JOIN maptile_vendor_pics mvp ON mb.mb_id = mvp.mvp_building_id AND mvp.mvp_vendor_id = ? AND mvp.created > ? AND mvp.created < ? LEFT JOIN sitefotos_forms_submitted sfs ON mb.mb_id = sfs.sfs_building_id AND sfs.sfs_vendor_id = ? AND sfs.sfs_created > ? AND sfs.sfs_created < ? WHERE mb.mb_user_id = ? AND mb.mb_status = '1' AND mvp.mvp_building_id IS NULL AND sfs.sfs_building_id IS NULL;`
    , [vendorId, startUnix, endUnix, vendorId, startUnix, endUnix, vendorId]);
    return results;
}

const fetchWorkordersReport = async (vendorId, startUnix, endUnix, page, itemsPerPage, sort) =>{
    let sortBy = ['sfs_created'];
    let sortDesc = ['DESC']
    if (sort, sort.length > 0){
      sortBy = [sort[0].field];
      sortDesc = sort[0].dir.toUpperCase()
    }
    //Let's first get total count
    let count = await mysql.awaitSafeQuery(`select count(sfs.sfs_id) as count
        from sitefotos_forms_submitted sfs LEFT JOIN sitefotos_work_orders_submissions swos on swos.swos_form_submission_id = sfs.sfs_id LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id
        where sfs.sfs_vendor_id = ? and sfs.sfs_created > ? and sfs.sfs_created < ? and swo.swo_id is not null;`
    , [vendorId, startUnix, endUnix]);
    itemsPerPage = itemsPerPage > -1 ? itemsPerPage : count[0].count;
    let results = await mysql.awaitSafeQuery(`select (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client) as Client,
               mb.mb_id as SiteID,
               mb.mb_nickname as SiteName,
               IF(swos_vendor_id = swos_submitter_vendor_id, 'SELF', ( select vendor_company_name from maptile_vendors where vendor_id = swos_submitter_vendor_id)) as Contractor,
               swo.swo_internal_id as InternalId,
               swo.swo_external_id as ExternalId,
               swo.swo_system_id as WorkOrderSystemId,
               swo.swo_schedule_datetime as ScheduledDate,
               sfs.sfs_created as FormSubmittedDate,
               swos.swos_checkout_datetime as LastWorkTime,
               swo.swo_id as WorkorderId,
               (select swos_name from sitefotos_work_orders_systems where swos_id=swo_system_id) as SystemName,
               swo.swo_nte as NTE,
               sf.sf_form_name as FormName,
               sfs.sfs_id as SubmittedFormID,
               sfs.sfs_id as FormSubmittedId,
               sfs.sfs_form_data_full as FormDataSubmitted  
            from sitefotos_forms_submitted sfs LEFT JOIN sitefotos_work_orders_submissions swos on swos.swos_form_submission_id = sfs.sfs_id LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id AND swos.swos_checkout_datetime = ( SELECT MAX(swos.swos_checkout_datetime) FROM sitefotos_work_orders_submissions swos2 WHERE swos2.swos_workorder_id = swo.swo_id ) LEFT JOIN maptile_building mb on sfs.sfs_building_id = mb.mb_id LEFT JOIN sitefotos_forms sf on sfs.sfs_form_id = sf.sf_id where sfs.sfs_vendor_id = ? and sfs.sfs_created > ? and sfs.sfs_created < ? and swo.swo_id is not null ORDER BY ${sortBy} ${sortDesc} limit ${itemsPerPage} offset ${itemsPerPage * (page - 1)};`, [vendorId, startUnix, endUnix]);
    return {
        last_page: Math.ceil(count[0].count / itemsPerPage),
        last_row: count[0].count,
        data: results
    };
}

const postToGoogle = async (url, user_id, sheet_name, data) => {
  let objToBePosted = {
    "user_id": user_id,
    "sheet_name": sheet_name,
    "data": data
  };

  let postToGoogle = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(objToBePosted)
  });
  postToGoogle = await postToGoogle.text();
  return postToGoogle;
}

const getChaseDashboard = async (internalUid, vendorId) =>{
    const buildingIds = Array.from(new Set((await mysql.awaitQuery(`select ssc_data from sitefotos_subuser_configuration where ssc_user_id=?`, [internalUid])).map(row => row.ssc_data.map(bldg => parseInt(bldg))).flat()));
    if(!buildingIds || buildingIds.length === 0){
        return {
            Landscape: { sites: 0, forms: 0 },
            Snow: { sites: 0, forms: 0 },
            Janitorial: { sites: 0, forms: 0 },
            HVAC: {sites: 0, forms: 0}
        }
    }
    console.log(buildingIds)

    const siteSql = `
        SELECT f.contract_type, COUNT(1) as count
        FROM ( SELECT 'Snow removal' AS contract_type
        UNION ALL SELECT 'Landscaping' 
        UNION ALL SELECT 'Janitorial services'
        UNION ALL SELECT 'HVAC installation and maintenance'
        ) f
        JOIN maptile_building c 
        ON FIND_IN_SET(binary f.contract_type,c.mb_contract_type)>0
        AND c.mb_id IN (?) AND mb_status='1'
        GROUP BY f.contract_type
    `;
    const sites = (await mysql.awaitQuery(siteSql, [buildingIds])).reduce((acc, cur) => ({ ...acc, [cur.contract_type]: { sites: cur.count } }), {});
    console.log(sites)
    const formsSql = `
        SELECT sf_form_contract_type, COUNT(DISTINCT sfs_building_id) count
        FROM sitefotos_forms_submitted 
        INNER JOIN sitefotos_forms ON sfs_form_id = sf_id AND sfs_time_stamp >= CURDATE() - INTERVAL 1 DAY
        WHERE sfs_building_id IN (?)
        GROUP BY sf_form_contract_type
    `;
    const forms = await mysql.awaitQuery(formsSql, [buildingIds]);
    Object.keys(sites).forEach(site => sites[site].forms = (forms.find(form => form.sf_form_contract_type === site) || { count: 0 }).count);
    console.log(sites)
    return sites;
}


module.exports = {
    getPricingStructure,
    getContractorDetailsFromVendorTable,
    getPricingForAllContractors,
    getDeicePrice,
    getSnowDataOptions,
    postFormToTableReportToGoogleSheets,
    fetchSitesWithNoServicesInGivenTimePeriod,
    fetchWorkordersReport,
    postToGoogle,
    getHybridUserSites,
    getChaseDashboard
}
//select *
//           from sitefotos_pricing_structure
//           where
//               sps_vendor_id = ? and sps_contract_active = '1' and sps_contract_for = ?
