const { awaitSafe<PERSON><PERSON>y, insertObj, updateObj, awaitQuery } = require('../utils/db')
const { serialize, unserialize } = require('php-serialize');
const mysql = require("../utils/db");
const {v4: uuidv4} = require("uuid");
const postgres = require("../utils/fleetdb");

const addClockInClockOut = async (bcData, vendorId, internalUid) => {
    let internalID = internalUid;
    internalID = internalID == null ? 0 : internalID;

    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/x-www-form-urlencoded");

    const urlencoded = new URLSearchParams();
    urlencoded.append("accessCode", bcData.accessCode);
    urlencoded.append("email", bcData.email);
    urlencoded.append("lat", bcData.lat);
    urlencoded.append("lon", bcData.lon);
    urlencoded.append("type", bcData.type);
    urlencoded.append("crew", bcData.employeeId);
    urlencoded.append("dt", bcData.date);
    urlencoded.append("uuid", uuidv4());
    urlencoded.append("appVersion", "1.0.1");
    urlencoded.append("deviceType", bcData.deviceType);
    urlencoded.append("deviceModel", bcData.deviceModel);
    urlencoded.append("tempprofileid", -1);
    urlencoded.append("siteid", bcData.siteId);

    const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: urlencoded.toString(),
        redirect: "follow"
    };
    await fetch("http://http/vpics/uploadbreadcrumb", requestOptions);

    insertObj('sitefotos_worker_payroll_log', {
        swp_id: 0,
        swp_start_time: bcData.date,
        swp_end_time: 0,
        swp_vendor_id: vendorId,
        swp_user_id: internalID,
        swp_status: 'Logged',
        swp_source_type: 'breadcrumb_data',
        swp_source_id: 0,
        operation_type: bcData.type == 4 ? 'WEBCLOCKIN':'WEBCLOCKOUT',
        is_latest: 0,
    });

    return true;
}

const createUpdateService = async (vendorID, serviceData, serviceID = null) => {
    const { name, description, provider, providerID, tradeID, serviceTypeID, quickbooksID, category, serviceOptions, providerMeta1, providerMeta2, status } = serviceData;
    const data = {}

    //These are required for creating a service now.
    if(!vendorID) throw new Error('Vendor ID is required to create a service.');
    if(!name) throw new Error('Service name is required to create a service.');
    if(!tradeID) throw new Error('Trade ID is required to create a service.');
    if(!serviceTypeID) throw new Error('Service Type ID is required to create a service.');


    if (name) data.vs_service_name = name;
    if (vendorID) data.vs_vendor_id = vendorID;
    if (description) data.vs_service_description = description;
    if (provider) data.vs_provider = provider;
    if (providerID) data.vs_provider_id = providerID;
    if (tradeID) data.vs_service_type = tradeID;
    if (serviceTypeID) data.vs_service_type = serviceTypeID;
    if (quickbooksID) data.vs_quickbooks_id = quickbooksID;
    // category: This will not be passed anymore by the frontend. Instead we need to migrate the old data in it and match it with tradeid's and popupulate those instead in our data migration.
    if (category) data.vs_service_category = category;
    // serviceOptions: Need a better approach for the storage and retrieval of service options. What are our options? Maybe use another table, atleast stop serializing and deserializing it and change the column type to json.
    if (serviceOptions) data.vs_service_options = serialize(serviceOptions);
    if (providerMeta1) data.vs_service_provider_meta1 = providerMeta1;
    if (providerMeta2) data.vs_service_provider_meta2 = providerMeta2;
    if (status === 1 || status === 0) data.vs_service_status = status;
    if (serviceID) {
        await updateObj('vendor_services', data, ['vs_service_id', 'vs_vendor_id'], [serviceID, vendorID]);
        return serviceID;
    } else {
        const record = await insertObj('vendor_services', data);
        return record.insertId;
    }
}

const activateService = async (vendorID, serviceID) => {
    const data = {
        vs_service_status: '1'
    }
    await updateObj('vendor_services', data, ['vs_service_id', 'vs_vendor_id'], [serviceID, vendorID]);
    return serviceID;
}

const deactivateService = async (vendorID, serviceID) => {
    const data = {
        vs_service_status: '0'
    }
    await updateObj('vendor_services', data, ['vs_service_id', 'vs_vendor_id'], [serviceID, vendorID]);
    return serviceID;
}

const getActiveServices = async (vendorID, serviceID = null) => {
    let sql = `SELECT * FROM vendor_services WHERE vs_service_status = '1' AND vs_vendor_id = ?`;
    let params = [vendorID];
    if (serviceID) {
        sql += ` AND vs_service_id = ?`;
        params.push(serviceID);
    }
    const rows = await awaitSafeQuery(sql, params);
    return rows;
}

const getAllServices = async (vendorID, serviceID = null) => {
    let sql = `SELECT * FROM vendor_services WHERE vs_vendor_id = ?`;
    let params = [vendorID];
    if (serviceID) {
        sql += ` AND vs_service_id = ?`;
        params.push(serviceID);
    }
    const rows = await awaitSafeQuery(sql, params);
    return rows;
}

//Equipment Types
const createUpdateEquipmentType = async (vendorID, equipmentTypeData, equipmentTypeID = null) => {
    const { name, description, active, status, trades } = equipmentTypeData;
    const data = {};
    if(!name) throw new Error('Equipment type name is required to create an equipment type.');


    if (name) data.set_equipment_name = name;
    //vendorid can also be 0, so we need to check if it is null or undefined
    if(vendorID != null) data.set_vendor_id = vendorID;
    if (description) data.set_equipment_description = description;
    if (active !== undefined) data.set_active = active ? 1 : 0;
    if (status !== undefined) data.set_status = status ? 1 : 0;

    if (trades) data.set_trades = JSON.stringify(trades);

    if (equipmentTypeID) {
        await updateObj('sitefotos_equipment_types', data, ['set_id'], [equipmentTypeID]);
        return equipmentTypeID;
    } else {
        const record = await insertObj('sitefotos_equipment_types', data);
        return record.insertId;
    }
};


const getAllEquipmentTypesWithTrades = async (vendorID) => {

    //Test if the user has default equipments. If yes ignore, if no add them
    let test = await mysql.awaitQuery(`SELECT * from sitefotos_equipment_types where set_vendor_id = ?`, [vendorID]);
    if(test && test.length === 0){
        await mysql.awaitQuery(`INSERT INTO sitefotos_equipment_types (set_vendor_id, set_equipment_name, set_active, set_status, set_trades) SELECT ?, set_equipment_name, set_active, set_status, set_trades FROM sitefotos_equipment_types where set_vendor_id = '0'`, [vendorID]);
    }

    const rows = await awaitSafeQuery(/*SQL*/`SELECT et.set_id, et.set_vendor_id, et.set_active, et.set_status, et.set_equipment_name, et.set_equipment_description, et.set_modified_at, et.set_created_at, et.set_trades FROM sitefotos_equipment_types AS et WHERE et.set_vendor_id = ? ORDER BY et.set_id`, [vendorID]);

    const equipmentTypesWithTrades = rows.map(row => ({
        ...row,
        trades: row.set_trades ? JSON.parse(row.set_trades) : [],
    }));

    return equipmentTypesWithTrades;
};


//service types - this is not needed but just in case.
const createUpdateServiceType = async (serviceTypeData, serviceTypeID = null) => {
    const { tradeID, serviceType, category } = serviceTypeData;
    const data = {};
    if(!tradeID) throw new Error('Trade ID is required to create a service type.');
    if(!serviceType) throw new Error('Service type is required to create a service type.');


    if (tradeID) data.sst_trade_id = tradeID;
    if (serviceType) data.sst_service_type = serviceType;
    if (category) data.sst_category = category;

    if (serviceTypeID) {
        await updateObj('sitefotos_service_types', data, ['sst_id'], [serviceTypeID]);
        return serviceTypeID;
    } else {
        const record = await insertObj('sitefotos_service_types', data);
        return record.insertId;
    }
};


//returns all the service types for a vendor
const getAllServiceTypes = async () => {

    const rows = await awaitSafeQuery(/*SQL*/`SELECT * FROM sitefotos_service_types`, []);
    return rows;
}

//return all service types for a trade
const getAllServiceTypesForTrade = async (tradeID) => {
    if(!tradeID) throw new Error('Trade ID is required to get all service types for a trade.');

    const rows = await awaitSafeQuery(/*SQL*/`SELECT * FROM sitefotos_service_types WHERE sst_trade_id = ?`, [tradeID]);
    return rows;
}

const getWorkerHistory = async (vendorId, start, end, internalID) => {
    internalID = internalID == "null" || internalID == 'undefined' ? 0 : internalID;
    const results =
      await awaitSafeQuery(
        `
            SELECT
                sbd.sbd_id                                                                            AS ParentTableId,
                sbt.sbt_type                                                                          AS BreadcrumbTypeID,
                sbt.sbt_name                                                                          AS AuditTrail,
                sbd.sbd_lat                                                                           AS GpsLat,
                sbd.sbd_lng                                                                           AS GpsLng,
                (SELECT concat(mb_long,",",mb_lat) FROM maptile_building mb WHERE mb_id = sbd.sbd_nearest_building_id) AS ClosestSiteLocation,
                (SELECT mb_nickname FROM maptile_building mb WHERE mb_id = sbd.sbd_nearest_building_id) AS ClosestSiteIN,
                sbt.sbt_name                                                                          AS TimeEvent,
                sbd.sbd_logged                                                                        AS AuditTime,
                sbd.sbd_crew                                                                          AS CrewId,
                sbd.sbd_nearest_building_distance                                                     AS NearestSiteDistance,
                swp.swp_start_time                                                                    AS PayrollAdjustmentIn,   -- Start time from payroll
                swp.swp_end_time                                                                      AS PayrollAdjustmentOut,  -- End time from payroll
                swp.swp_id                                                                            AS PayrollId,
                swp.swp_status                                                                        AS Status,
                sbd.sbd_vendor_id                                                                     AS VendorID,
                (select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = swp.swp_user_id) as SubUserName
            FROM
                sitefotos_breadcrumb_data sbd
                    LEFT JOIN sitefotos_breadcrumb_types sbt ON sbt.sbt_type = sbd.sbd_type
                    LEFT JOIN sitefotos_worker_payroll swp ON swp.swp_source_id = sbd.sbd_id
                    AND swp.swp_source_type = 'breadcrumb_data'  -- Join to worker payroll for breadcrumb entries
            WHERE
                sbd.sbd_vendor_id = ?
              AND sbd.sbd_logged >= ?
              AND sbd.sbd_logged < ?
              AND sbd.sbd_type IN (4, 30, 31, 5);
        `,
        [vendorId, start, end]
      );
    const servicesTable = await awaitSafeQuery(
      `
          SELECT
              swsd.swsd_id                                                                          AS ParentTableId,
          sfs.sfs_checkout                                                                          AS AuditTime,
              2                                                                                    AS BreadcrumbTypeID,
              'Checkin/Checkout'                                                                    AS AuditTrail,
              swsd.swsd_email                                                                       AS CrewEmail,
              swsd.swsd_service_name                                                                AS Service,
              swsd.swsd_people                                                                      AS People,
              swsd.swsd_hours                                                                       AS Hours,
              swsd.swsd_service_source                                                              AS Source,
              swsd.swsd_uploader_vid                                                                AS UploaderVid,
              swsd.swsd_date_time                                                                   AS ServiceLoggedDateTime,
              sfs.sfs_checkin                                                                       AS CheckIn,
              sfs.sfs_checkout                                                                      AS CheckOut,
              swp.swp_start_time                                                                    AS PayrollAdjustmentIn,  -- Generic start time
              swp.swp_end_time                                                                      AS PayrollAdjustmentOut, -- Generic end time
              swp.swp_id                                                                            AS PayrollId,
              swp.swp_status                                                                        AS Status,
              sfs.sfs_id                                                                            AS FormSubmissionId,
              sfs.sfs_form_id                                                                       AS FormId,
              sfs.sfs_form_name                                                                     AS Form,
              mb.mb_nickname                                                                        AS MB,
              mb.mb_lat                                                                             AS Latitude,
              mb.mb_long                                                                            AS Longitude,
              mb.mb_id                                                                              AS SiteId,
              mb.mb_nickname                                                                        AS SiteName,
              (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb.mb_zone_id) AS Zone,
              (SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id = mb.mb_client) AS Client,
              vs.vs_service_id                                                                      AS VendorServiceId,
              vs.vs_service_type_id                                                                 AS VendorServiceTypeId,
              vs.vs_equipment_id                                                                    AS EquipmentId,
              swsd.swsd_vendor_id                                                                   AS VendorID,
              st.st_trade                                                                           AS TradeTitle,
              st.st_id                                                                              AS TradeId,
              (select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = swp.swp_user_id) as SubUserName
          FROM
              sitefotos_wp_services_data swsd
                  LEFT JOIN sitefotos_forms_submitted sfs ON sfs.sfs_id = swsd.swsd_form_submission_reference
                  LEFT JOIN maptile_building mb ON mb.mb_id = swsd.swsd_site_id
                  LEFT JOIN vendor_services vs ON vs_service_id = swsd.swsd_service_id
                  LEFT JOIN sitefotos_trades st ON st.st_id = vs.vs_trade_id
                  LEFT JOIN sitefotos_worker_payroll swp ON swp.swp_source_id = swsd.swsd_id
                  AND swp.swp_source_type = 'wp_services_data'  -- Ensure the join happens for wp_services_data
          WHERE
              swsd.swsd_vendor_id = ?
            AND swsd.swsd_date_time >= ?
            AND swsd.swsd_date_time < ?
            AND swsd.swsd_service_status != 'TEMPLOG';
      `,
      [vendorId, start, end]
    );
    return {bc: results, servtab: servicesTable};
}

//should not be used but just in case
const createUpdateTrade = async (tradeData, tradeID = null) => {
    const { trade, tradeDescription } = tradeData;

    if(!trade) throw new Error('Trade is required to create a trade.');

    const data = {};



    if (trade) data.st_trade = trade;
    if (tradeDescription) data.st_trade_description = tradeDescription;

    if (tradeID) {
        await updateObj('sitefotos_trades', data, ['st_id'], [tradeID]);
        return tradeID;
    } else {
        const record = await insertObj('sitefotos_trades', data);
        return record.insertId;
    }
};

const getAllTrades = async () => {
    const rows = await awaitSafeQuery(/*SQL*/`SELECT * FROM sitefotos_trades`);
    return rows;
}

const getTradeID = async (trade) => {
    if(!trade) throw new Error('Trade is required to get trade ID.');

    const rows = await awaitSafeQuery(/*SQL*/`SELECT st_id FROM sitefotos_trades WHERE st_trade = ?`, [trade]);
    return rows[0].st_id;
}

const getSnowStorms = async (buildings, startRange, endRange) => {
    const ids = Array.from(buildings).map(item => parseInt(item)).filter(Number.isFinite);
    if (ids.length === 0) return 0;

    const placeholders = ids.map((_, i) => `$${i + 3}`).join(','); // start from $3
    const query = `SELECT wssr.*, wsm.*, ws.* as time_zone FROM weather_station_storms_readings wssr 
                        INNER JOIN weather_stations_mapping wsm 
                            ON wssr.wssr_ws_external_id = wsm.wsg_ws_external_id 
                            AND wssr.wssr_provider_id = 101 
                        INNER JOIN weather_stations ws 
                            ON wsm.wsg_ws_external_id = ws.ws_external_id  
                    WHERE wsm.wsg_bid IN (${placeholders}) AND wssr.wssr_start_time > $1 AND wssr.wssr_end_time < $2`;

    const values = [startRange, endRange, ...ids];
    const snowStorms = await postgres.awaitQuery(query, values);
    const totalsByBuilding = new Map();
    for (const row of snowStorms) {
        const bid = parseInt(row.wsg_bid);
        const total = parseFloat(row.wssr_total) || 0;

        if (!totalsByBuilding.has(bid)) {
            totalsByBuilding.set(bid, 0);
        }

        totalsByBuilding.set(bid, totalsByBuilding.get(bid) + total);
    }
    // Ensure all input IDs are represented, even if they had no storms
    for (const id of ids) {
        if (!totalsByBuilding.has(id)) {
            totalsByBuilding.set(id, 0);
        }
    }

    return Object.fromEntries(totalsByBuilding);
};

const addServiceHistoryAuditTrail = async ( vendorId, userId, serviceLoggedId, actionPerformed, fieldChanged, oldValue, newValue, additionalInformation ) => {
    try {
        const obj = {
            sshat_vendor_id: vendorId,
            sshat_user_id: userId,
            sshat_service_id: serviceLoggedId,
            sshat_action: actionPerformed,
            sshat_field_changed: fieldChanged ?? null,
            sshat_old_value: oldValue ?? null,
            sshat_new_value: newValue ?? null,
            sshat_note: additionalInformation ?? null
        };
        const insertion = await insertObj('sitefotos_service_history_audit_trail', obj);
        return insertion.insertId;
    }
    catch(ex) {
        throw ex
    }
}

//Setup Equipment Method
const seedEquipmentTableSnow = async () => {
    try {
        const sql = `SELECT sesed_id, sesed_service_name, sesed_equipment_label, sesed_vendor_id from sitefotos_estimator_services_equipment_data group by sesed_equipment_label, sesed_vendor_id`;
        const rows = await awaitSafeQuery(sql);
        //group rows by sesed_vendor_id and iterate over each group
        const groupedRows = rows.reduce((acc, row) => {
            const vendorID = row.sesed_vendor_id;
            if (!acc[vendorID]) {
                acc[vendorID] = [];
            }
            acc[vendorID].push(row);
            return acc;
        }, {});
        const vendor0Rows = rows.filter(row => row.sesed_vendor_id === 0);
        //iterate over each group
        for (const vendorID in groupedRows) {
            let existingEquipmentTypes = await getAllEquipmentTypesWithTrades(vendorID);
            console.log(`Processing vendor ${vendorID} in seedEquipmentTableSnow ...`)


            //only create a new equipment type if it does not exist
            for (const row of groupedRows[vendorID]) {
                const { sesed_equipment_label } = row;
                const existingEquipmentType = existingEquipmentTypes.find(equipmentType => equipmentType.set_equipment_name === sesed_equipment_label);
                let trade = await getTradeID('Snow removal');
                if (!existingEquipmentType) {
                    const equipmentTypeID = await createUpdateEquipmentType(vendorID, {
                        name: sesed_equipment_label,
                        trades: [trade],
                    });
                }
            }
        }


    } catch (err) {
        console.log(err);
    }
}

//Migrate Service Types
const seedServiceTypesSnow = async () => {
    try {
        const sql = `SELECT vs_service_id, vs_service_type from vendor_services`;
        const rows = await awaitSafeQuery(sql);
        const mapping = {
            PUSH_WALKS: 100,
            PUSH_LOTS: 101,
            PUSH_PUBLIC_WALKS: 102,
            DEICE_WALKS: 103,
            DEICE_LOTS: 104,
            DEICE_PUBLIC_WALKS: 105
        }
        //iterate over each and populate vs_service_type_id based on the mapping and value in vs_service_type ENUM column
        for (const row of rows) {
            const { vs_service_id, vs_service_type } = row;
            const serviceTypeID = mapping[vs_service_type];

            if(serviceTypeID) {
                console.log(serviceTypeID)
                updateObj('vendor_services', { vs_service_type_id: serviceTypeID, vs_trade_id: 1 }, ['vs_service_id'], [vs_service_id]);
            }
        }
    } catch (err) {
        console.log(err);
    }
}


//Migrate Equipments in vendor_services
const seedEquipmentSnow = async () => {
    try {
        const rows = await awaitSafeQuery(`SELECT vs_service_id, vs_equipment_id from vendor_services`,[]);

        const oldEquipment = await awaitSafeQuery(`SELECT sesed_id, sesed_equipment_label, sesed_vendor_id from sitefotos_estimator_services_equipment_data`)

        const newEquipment = await awaitSafeQuery(`SELECT set_id, set_equipment_name,set_vendor_id from sitefotos_equipment_types`)
        for (const row of rows) {
            const { vs_service_id, vs_equipment_id } = row;
            if(vs_equipment_id) {
                const oldEquipmentRow = oldEquipment.find(equipment => equipment.sesed_id == vs_equipment_id);
                if (oldEquipmentRow){
                    const newEquipmentRow = newEquipment.find(equipment => equipment.set_equipment_name === oldEquipmentRow.sesed_equipment_label && equipment.set_vendor_id === oldEquipmentRow.sesed_vendor_id);
                    if(newEquipmentRow) {
                        updateObj('vendor_services', { vs_equipment_id: newEquipmentRow.set_id }, ['vs_service_id'], [vs_service_id]);
                    }
                }
            }
        }
        console.log("Done!")
    } catch (err) {
        console.log(err);
    }
}

const seedServiceTypePricingTable = async () => {
    try {
        const rows = await awaitQuery(`SELECT sps_id, sps_service_type from sitefotos_pricing_structure`,[]);
        const mapping = {
            PUSH_WALKS: 100,
            PUSH_LOTS: 101,
            PUSH_PUBLIC_WALKS: 102,
            DEICE_WALKS: 103,
            DEICE_LOTS: 104,
            DEICE_PUBLIC_WALKS: 105
        }
        for (const row of rows) {
            const { sps_id, sps_service_type } = row;
            const serviceTypeID = mapping[sps_service_type];
            if(serviceTypeID) {
                updateObj('sitefotos_pricing_structure', { sps_service_type_id: serviceTypeID }, ['sps_id'], [sps_id]);
            }
        }
        console.log("Done!")

    } catch (err) {
        console.log(err);
    }

}

const seedTypeServicesLoggedSource = async () => {
    try {
        console.log("Seeding...")
        // const rows = await awaitQuery(`select sfs.sfs_id, sfs.sfs_devicemodel from sitefotos_forms_submitted sfs`,[]);
        const servicesRows = await awaitQuery(`select swsd.swsd_id, swsd.swsd_form_submission_reference from sitefotos_wp_services_data swsd where swsd.swsd_form_submission_reference is not null`,[]);

        for (const servicesRow of servicesRows) {
            const { swsd_id, swsd_form_submission_reference } = servicesRow;
        //     //find attached form type
            const attachedForm = await awaitQuery(`select sfs.sfs_id, sfs.sfs_devicemodel from sitefotos_forms_submitted sfs where sfs.sfs_id = ?`,[servicesRow.swsd_form_submission_reference]);
            const { sfs_id, sfs_devicemodel } = attachedForm[0];
            //swsd_service_source
            updateObj('sitefotos_wp_services_data', { swsd_service_source: sfs_devicemodel }, ['swsd_id'], [swsd_id]);
        }
        console.log("Seeded!")
        // const mapping = {
        //     PUSH_WALKS: 100,
        //     PUSH_LOTS: 101,
        //     PUSH_PUBLIC_WALKS: 102,
        //     DEICE_WALKS: 103,
        //     DEICE_LOTS: 104,
        //     DEICE_PUBLIC_WALKS: 105,
        // }
        // for (const row of rows) {
        //     const { sfs_id, sfs_devicemodel } = row;
        //     const { swsd_id, swsd_form_submission_reference } = row;
        //     console.log(sfs_id, sfs_devicemodel)
        //     // if(serviceTypeID) {
        //     //     updateObj('sitefotos_pricing_structure', { sps_service_type_id: serviceTypeID }, ['sps_id'], [sps_id]);
        //     // }
        // }

    } catch (err) {
        console.log(err);
    }

}
//seedServiceTypePricingTable()
//seedServiceTypesSnow()
// seedTypeServicesLoggedSource()
// seedEquipmentSnow();

module.exports = {
    getAllTrades,
    getActiveServices,
    getAllServices,
    createUpdateService,
    deactivateService,
    activateService,
    createUpdateEquipmentType,
    getAllEquipmentTypesWithTrades,
    getAllServiceTypesForTrade,
    getAllServiceTypes,
    getWorkerHistory,
    addClockInClockOut,
    getSnowStorms,
    addServiceHistoryAuditTrail
};
