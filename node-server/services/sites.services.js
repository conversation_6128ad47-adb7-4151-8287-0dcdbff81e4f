const { updateVendorSitesChargify, syncFleetServer, checkSiteLimit } = require('../utils/sites')
const nodeGeocoder = require('node-geocoder')
const { awaitQuery,insertObj,awaitSafeQuery,updateObj,deleteObj, getConnection } = require('../utils/db')
const { validateVendorAccessCode, UserPrivilege,getVendorByEmail, createVendor, getVendorByID } = require("../utils/vendor")
const {serialize} = require('php-serialize')
const {sendEmailTemplate} = require('../utils/email')
const {isEmailValid} = require('../utils/common');
const fleetdb = require('../utils/fleetdb')
const { redisClient } = require('../utils/redis-client.js')
const { withLock } = require('../utils/lock')
const adminServices = require('./admin.services');
const formServices  = require('./form.services');

const bus = require('../utils/eventbus');

const options = {
    provider: 'google',
    apiKey: 'AIzaSyA5RJ6ybPQAOIVNtTI8O9xuyAuOAnrnMg8',
    formatter: null
}

let mapping = {
    'USA': 254,
    "US": 254,
    "MEXICO": 159,
    "MX": 159,
    "CANADA": 43,
    "CA": 43
}

const geocoder = nodeGeocoder(options)

const fixGeo = async (siteId) => {
    try {
        let site = await awaitQuery(/*SQL*/`select * from maptile_building where mb_id = ?`, [siteId])
        let lat = parseFloat(site[0].mb_lat)
        let lng = parseFloat(site[0].mb_long)
        let geo = await getParcel(lat, lng)
        await awaitQuery(/*SQL*/`update maptile_building set mb_geo = ? where mb_id = ?`, [serialize(geo), siteId])

    } catch(ex) {
        console.log(ex)
    }

}


bus.on('CONTACT_SAVED', async (event) => {
    try {
        const vendorId = event.vendorId;
        const contactId = event.id.toString();
        let [contact] = await awaitQuery(/*SQL*/`select sf_contact_id, sf_contact_active, sf_contact_contractor_vendor_id from sitefotos_contacts where sf_contact_id = ?`, [contactId], { useMainPool: true})
        if(!contact) return;
        let contractorVendorId = contact.sf_contact_contractor_vendor_id;
        if(!contractorVendorId) return;

        let active = contact.sf_contact_active == 1;
        contractorVendorId = contractorVendorId.toString();
        let buildings = await awaitQuery(/*SQL*/`SELECT mb_id, mb_contractor, mb_contractor_vid FROM maptile_building WHERE (mb_user_id = ? AND JSON_CONTAINS(mb_contractor, JSON_QUOTE(?)))`, [vendorId, contactId]);

        for (let building of buildings) {
            let conVendors = JSON.parse(building.mb_contractor_vid);
            let conContacts = JSON.parse(building.mb_contractor);
            let mbId = building.mb_id;

            if (!conVendors.includes(contractorVendorId) && active) {
                conVendors.push(contractorVendorId);
                let data2 = { mb_contractor_vid: JSON.stringify(conVendors) };
                await updateObj('maptile_building', data2, ['mb_id'], [mbId]);
            }
            if (!active) {
                conVendors = conVendors.filter(item => item != contractorVendorId);
                conContacts = conContacts.filter(item => item != contactId);
                let data2 = { mb_contractor_vid: JSON.stringify(conVendors), mb_contractor: JSON.stringify(conContacts) };
                await updateObj('maptile_building', data2, ['mb_id'], [mbId]);
            }
        }

    } catch (ex) {
        console.log(ex);
    }
});

const disconnectSite = async (vendorID, siteID) => {
    try {
        let site = await awaitQuery(/*SQL*/`select * from maptile_building where mb_id = ?`, [siteID])
        if (site && site[0]) {
            site = site[0]
            let siteContractors = JSON.parse(site.mb_contractor)
            let siteContractorVIDs = JSON.parse(site.mb_contractor_vid)
            let vendorClient = await awaitQuery(/*SQL*/`select * from maptile_vendors where vendor_id = ?`, [site.mb_user_id])
            let vendorContractor = await awaitQuery(/*SQL*/`select * from maptile_vendors where vendor_id = ?`, [vendorID])
            let contacts = await awaitQuery(/*SQL*/`select * from sitefotos_contacts LEFT JOIN maptile_vendors ON sf_contact_contractor_vendor_id = vendor_id where sf_contact_vendorid = ? and sf_contact_type = 'Contractor' and vendor_id=? ;`, [site.mb_user_id, vendorID])

            if (vendorClient && vendorClient[0] && vendorContractor && vendorContractor[0] && contacts && contacts[0]) {
                let contactID = contacts[0].sf_contact_id

                siteContractors = siteContractors.filter(item => item != contactID)
                siteContractorVIDs = siteContractorVIDs.filter(item => item != vendorID)

                let obj = {
                    mb_contractor: JSON.stringify(siteContractors),
                    mb_contractor_vid: JSON.stringify(siteContractorVIDs)
                }
                await updateObj('maptile_building', obj, ['mb_id'], [siteID])


                const vars = {
                    firstName: vendorClient[0].vendor_fname,
                    site: site.mb_nickname,
                    company: vendorContractor[0].vendor_company_name,
                }
                await sendEmailTemplate(`${vendorClient[0].vendor_fname} ${vendorClient[0].vendor_lname}`,vendorClient[0].vendor_email,'A site was removed by a contractor','./static/emails/sites/disconnect-email.html',vars)
                redisClient.del(`getapptm5:buildings:${vendorID}`);
                bus.emit('SITE_DISCONNECTED', siteID);
                return true

            }
        }
    } catch (ex) {
        console.log(ex || ex.message)
        throw ex
    }

}
const getOrCreateCity = async (city, stateID, lat, lng) => {
    let city2 = "%" + city + "%";
    let cityId
    let cityResp = await awaitQuery(`select CityId from maptile_city where City like ? AND RegionID=? ORDER BY length(City) asc`, [city2, stateID])
    if (cityResp && cityResp[0]) {
        cityId = cityResp[0]['CityId']
    } else {
        let result = await awaitQuery(`INSERT INTO maptile_city (CountryID, RegionID, City, Latitude, Longitude, TimeZone) VALUES(254, ${stateID}, ?, ?, ?, '')`, [city, lat, lng])
        cityId = result.insertId
    }
    return cityId
}



const getState = async (state, countryId) => {
    let stateQuery = await awaitQuery(`select * from maptile_state where (state_name LIKE ? OR state_abv_name LIKE ?) AND country_id = ?`, [state, state, countryId])
    if (stateQuery && stateQuery[0])
        return stateQuery[0]['id'];
    else
        return 0
}
const clusterSites = async (vendorID) => {
    try {
        await syncFleetServer(vendorID)
    } catch (e) {
        console.log(e)
    }
}
const updateSiteDetails = async (sites, updateAddress, updateCity, updateState, updateZip, updateCountry) => {
    const results = await awaitSafeQuery(`select * from maptile_building where mb_id  IN (${sites.join(',')})`);
    if (results && results[0]) {
        for (let result of results) {
            try {

                let addressData = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${result.mb_lat},${result.mb_long}&key=AIzaSyCYhjhhI0WsVM40t1dqryswbnCedl_lwEI`)
                addressData = await addressData.json();

                if (addressData && addressData.results && addressData.results[0]) {
                    let address = addressData.results[0].formatted_address;
                    //get city, cstate,zip,country
                    let city, state, zip, country, name;
                    if (addressData.results[0].address_components) {
                        city = addressData.results[0].address_components.find((item) => {
                            return item.types.includes('locality')
                        })
                        state = addressData.results[0].address_components.find((item) => {
                            return item.types.includes('administrative_area_level_1')
                        })
                        zip = addressData.results[0].address_components.find((item) => {
                            return item.types.includes('postal_code')
                        })
                        country = addressData.results[0].address_components.find((item) => {
                            return item.types.includes('country')
                        })

                        //street address
                        let street = addressData.results[0].address_components.find((item) => {
                            return item.types.includes('street_number')
                        })
                        let route = addressData.results[0].address_components.find((item) => {
                            return item.types.includes('route')
                        })
                        if (street && route) {
                            address = `${street.short_name} ${route.short_name}`
                        }

                    }

                    let data = {}
                    let countryId = 254;
                    if (country)  countryId = mapping[country.short_name.toUpperCase()]
                    let stateId;
                    if(state) stateId = await getState(state.short_name, countryId);
                    let cityId;

                    if(city) cityId = await getOrCreateCity(city.long_name, stateId, result.mb_lat, result.mb_long)
                    if (cityId && updateCity) {

                        data.mb_city = city.long_name;
                    }
                    if (stateId && updateState) {
                        data.mb_state = stateId;
                    }
                    if (zip && updateZip) {
                        data.mb_zip_code = zip.long_name;
                    }
                    if (updateCountry) {
                        data.mb_country = countryId;
                    }
                    if (name && updateName) {
                        data.sml_layer_name = name;
                    }
                    if (address && updateAddress) {
                        data.mb_address1 = address;
                    }
                    if (Object.keys(data).length) {
                        await updateObj('maptile_building', data, ['mb_id'], [result.mb_id])
                    }
                }
            } catch (ex) {
                console.log(ex)
            }
        }
    }
}
//This method will be expanded in the future, currently will only do contractor, client, zone, trades modification
const modifySite = async (vendorID, siteID, siteData) =>{

    let site = await awaitQuery(`select * from maptile_building where mb_id=?`, [siteID])

    if(site && site[0]){
        site = site[0]
        let siteData2 = {}
        let manager = siteData['manager'] ?? null
        if (manager) {
            let managerResults = await awaitSafeQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [manager, vendorID])
            if (managerResults.length > 0) {
                siteData2['mb_manager'] = manager;
            } else {
                siteData2['mb_manager'] = null;
            }
        }
        if (siteData['lat'])
            siteData2['mb_lat'] = siteData['lat']
        if(siteData['lng'])
            siteData2['mb_long'] = siteData['lng']
        if(siteData['address'])
            siteData2['mb_address1'] = siteData['address']
        if(siteData['name']) {
            if(siteData['nickname']) {
                siteData2['mb_name'] = siteData['name'];
                siteData2['mb_nickname'] = siteData['nickname'];
            } else {
                siteData2['mb_nickname'] = siteData['name'];
            }
        }
        let countryId = null;
        if(siteData['country']) {
            //If user specifies it then only update it.
            let country = siteData['country']
            if (mapping.hasOwnProperty(country.toString().toUpperCase())) {
                countryId = mapping[country.toUpperCase()]
            } else {
                throw new Error(`Country should be one of the following: USA, Canada, Mexico. You sent ${country}`)
            }
            siteData2['mb_country'] = countryId
        } else {
            //Use the existing one.
            countryId = site.mb_country;
        }
        let stateId
        if(siteData['state']) {
            stateId = await getState(siteData['state'], countryId);
            if(!stateId) {
                throw new Error("State does not exist, please send a valid state abbreviation.");
            }
            siteData2['mb_state'] = stateId
        } else {
            stateId = site.mb_state;
        }
        if(siteData['city']) {
            let lat = siteData['lat'] || site['mb_lat']
            let lng = siteData['lng'] || site['mb_long']
            let cityId = await getOrCreateCity(siteData['city'], stateId, lat, lng)
            siteData2['mb_city'] = cityId
        }
        if(siteData['zip']) siteData2['mb_zip_code'] = siteData['zip']
        if(siteData['ohours'] != null)siteData2['mb_ohours'] = siteData['ohours']
        if(siteData['notes'] != null) siteData2['mb_notes'] = siteData['notes']
        if(siteData['geo']) siteData2['mb_geo'] = serialize(siteData['geo']);
        if(siteData['maps']) {
            let maps = siteData['maps']
            if (maps) {
                if (!Array.isArray(maps))
                {
                    if(typeof maps == 'string') {
                        try {
                            maps = JSON.parse(maps)
                        } catch (e) {
                            maps = maps.split(',')
                        }
                    }
                }
                if(maps.length>0) siteData2['mb_maps'] = JSON.stringify(maps.map(a=>a.toString()))
            }
        }
        let contractors = siteData['contractors'] ?? siteData['contractor'] ?? null;
        if (contractors) {
            if(typeof contractors == 'string') {
                try {
                    contractors = JSON.parse(contractors)
                } catch (e) {
                    contractors = contractors.split(',')
                }
            }

            let contractorVids = []
            if (contractors && contractors.length > 0) {
                //NOTE: Making a single loop by fetching all contractor in one request from DB using in operator.
                let contractorResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id in (?) AND sf_contact_vendorid=?`, [contractors, vendorID])
                if (contractorResults || contractorResults.length !== 0) {
                    for (let contractorResult of contractorResults) {
                        const cAccessCode = contractorResult['sf_contact_contractorid']
                        const cVendor = await validateVendorAccessCode(cAccessCode)
                        if (cVendor['userType'] === UserPrivilege.InvalidAccessCode)
                            continue
                        else
                            contractorVids.push(cVendor['vendorId'])
                    }
                } else {
                    throw new Error("Contractors not found")
                }

                contractors = contractors.length > 0 ? JSON.stringify(contractors.map(a => a.toString())) : '[]'
                contractorVids = contractorVids.length > 0 ? JSON.stringify(contractorVids.map(a => a.toString())) : '[]'
            }
            if(contractors && contractors.length>0){
                siteData2['mb_contractor'] = contractors
                siteData2['mb_contractor_vid'] = contractorVids
            }
        }
        if (siteData['client']) {
            let client = siteData['client']
            if(typeof client !== 'string')
                client = client.toString();
            if (client && client.length > 0) {
                let clientResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [client, vendorID])
                if (!clientResults || clientResults.length === 0)
                    client = null
            }
            else {
                client = null
            }
            if(client){
                siteData2['mb_client'] = client
            }
        }
        if (siteData['trades']) {
            let trades = siteData['trades']
            if (trades) {
                if (!Array.isArray(trades))
                {
                    if(typeof trades == 'string') {
                        try {
                            trades = JSON.parse(trades)
                        } catch (e) {
                            trades = trades.split(',')
                        }
                    }
                }

                const query = "SELECT TRIM(TRAILING ')' FROM SUBSTRING(COLUMN_TYPE,6))  as enumVal  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'maptile_building' AND COLUMN_NAME = 'mb_contract_type'"
                let enumVal = (await awaitSafeQuery(query))[0]['enumVal']
                let result = enumVal.replace(/\'/g, '')
                let allowedTrades = result.split(',')
                let checker = (arr, target) => target.every(v => arr.includes(v))
                if (!checker(allowedTrades, trades))
                    throw new Error("Invalid Trades Specified")
                trades = trades.toString()
                if(trades && trades.length>0){
                    siteData2['mb_contract_type'] = trades
                }
            }

        }
        if(siteData['zone'])  siteData2['mb_zone_id'] = siteData['zone'];


        let externalSource = siteData['externalSource'] ||  siteData['externalsrc'] || null
        let externalID = siteData['externalID'] || siteData['externalpropertyid'] || null

        if(externalSource)
        {
            if(!['BossLM', 'EMCOR', 'ServiceChannel', 'Aspire', 'Corrigopro', 'Command7','FMPilot', 'CaseFMS'].includes(externalSource))
                throw new Error("Invalid External Source Specified")
        }
        if(externalID)
        {
            if(!externalSource)
                throw new Error("External Source is required when External ID is specified")
            const checkExternalID = await awaitQuery(`SELECT * from maptile_building where mb_user_id=? AND mb_external_id=? AND mb_external_src=?`, [vendorID, externalID, externalSource])
            if(checkExternalID && checkExternalID.length > 0) {
                throw new Error(`External ID ${externalID} already exists for External Source ${externalSource} for vendor ${vendorID}, mb_id: ${checkExternalID[0]['mb_id']}`)
            }
            siteData2['mb_external_src'] = externalSource;
            siteData2['mb_external_id'] = externalID;
        }

        if(Object.keys(siteData2).length > 0){
            await updateObj('maptile_building', siteData2, ['mb_id'], [siteID])
        }
        redisClient.del(`getapptm5:buildings:${vendorID}`);
    }
}

const getTimezone = (mb_lat, mb_lng) => {
    const lat = parseFloat(mb_lat);
    const lng = parseFloat(mb_lng);

    if (!isFinite(lat) || !isFinite(lng)) return '';

    try {
        const tzRaw = find(lat, lng);
        return Array.isArray(tzRaw) ? tzRaw[0] : tzRaw || 'UTC';
    } catch (err) {
        console.warn(`Timezone failed at sites page`);
    }
}

const createSite = async (vendorID, siteData) => {
    const siteLimit = await checkSiteLimit(vendorID);
    if (siteLimit.error == true)
       return siteLimit;

    let address1 = siteData['address1'] ||  siteData['address'] || null

    let zip = siteData['zip'] || null
    let name = siteData['name'] || null
    let city = siteData['city'] || null
    let state = siteData['state'] || null
    let emails = siteData['emails'] || null
    let sqft = siteData['sqft'] || null
    let metadata = siteData['metadata'] || null
    let autoShare = siteData['autoshare'] ?? '2'
    let mbStatus = siteData['mb_status'] || '1'; //Setting 3 for site leads.
    //defensive coding
    if(zip == 'null' || zip == 'undefined' || zip == 'NaN' || zip == '0')
        zip = null;
    if(autoShare == 'null' || autoShare == 'undefined' || autoShare == 'NaN' || autoShare == '0')
        autoShare = '2'
    if(sqft == 'null' || sqft == 'undefined' || sqft == 'NaN' || sqft == '0')
        sqft = null;
    if (!address1 || !city || !state || !name)
        throw new Error("Required fields missing while creating sites")

    let address2 = siteData['address2'] || ''
    let nickname = siteData['nickname'] || name
    let lat = siteData['lat'] || null
    let lng = siteData['lng'] || null
    let country = siteData['country'] || 'USA'
    let countryId = 254
    if (!lat || !lng) {
        const geo = await geocoder.geocode(address1 + " " + address2 + " " + city + " " + state + " " + country)
        if (geo[0]) {
            if (geo[0]['latitude']) {
                lat = geo[0]['latitude']
                lng = geo[0]['longitude']
            }
            else {
                throw new Error("Could not geocode address, please provide valid latitude and longitude.")
            }
        }
        else {
            throw new Error("Could not geocode address, please provide valid latitude and longitude.")
        }
    }
    let mbTimezone = getTimezone(lat, lng);
    let cachedGeoData;
    if(!zip)
    {
        cachedGeoData = await geocoder.reverse({ lat: lat, lon: lng });
        if(cachedGeoData)
        {
            if(cachedGeoData[0])
            {
                if(cachedGeoData[0]['zipcode'])
                {
                    zip = cachedGeoData[0]['zipcode']
                }
            }
        }
    }

    if (mapping.hasOwnProperty(country.toString().toUpperCase())) {
        countryId = mapping[country.toUpperCase()]
    } else {
        throw new Error(`Country should be one of the following: USA, Canada, Mexico. You sent ${country}`)
    }

    let ohours = siteData['ohours'] ?? null
    let notes = siteData['notes'] ?? null
    let zone = siteData['zone'] ?? null
    let client = siteData['client'] || null
    let clientSecondary = siteData['client_secondary'] || null
    let clientThird = siteData['client_third'] || null

    if (client) {
        let clientResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [client, vendorID])
        if (!clientResults || clientResults.length === 0)
            throw new Error("Internal client was specified but not found.")
    }
    else {
        client = null
    }

    if (clientSecondary) {
        let clientResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [clientSecondary, vendorID])
        if (!clientResults || clientResults.length === 0)
            throw new Error("Internal client secondary was specified but not found.")
    }
    else {
        clientSecondary = null
    }

    if (clientThird) {
        let clientResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [clientThird, vendorID])
        if (!clientResults || clientResults.length === 0)
            throw new Error("Internal client third was specified but not found.")
    }
    else {
        clientThird = null
    }

    let manager = siteData['manager'] || null
    let managerSecondary = siteData['managersecondary'] || null
    let managerThird = siteData['managerthird'] || null

    if (manager) {
        let managerResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [manager, vendorID])
        if (!managerResults || managerResults.length === 0)
            throw new Error("Internal manager was specified but not found.")

    }
    else {
        manager = null
    }

    if (managerSecondary) {
        let managerResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [managerSecondary, vendorID])
        if (!managerResults || managerResults.length === 0)
            throw new Error("Internal manager secondary was specified but not found.")

    }
    else {
        managerSecondary = null
    }

    if (managerThird) {
        let managerResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id=? AND sf_contact_vendorid=?`, [managerThird, vendorID])
        if (!managerResults || managerResults.length === 0)
            throw new Error("Internal manager third was specified but not found.")

    }
    else {
        managerThird = null
    }

    let contractors = siteData['contractors'] ?? siteData['contractor'] ?? null
    let contractorVids = []
    if (contractors) {
        if(typeof contractors == 'string') {
            try {
                contractors = JSON.parse(contractors)
            } catch (e) {
                contractors = contractors.split(',')
            }
        }
        if (!Array.isArray(contractors))  {
            throw new Error("Contractors when specified need to be an array")
        }
        if (contractors.length > 0) {
            //NOTE: Making a single loop by fetching all contractor in one request from DB using in operator.
            let contractorResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id in (?) AND sf_contact_vendorid=?`, [contractors, vendorID])
            if (contractorResults || contractorResults.length !== 0) {
                for (let contractorResult of contractorResults) {
                    const cAccessCode = contractorResult['sf_contact_contractorid']
                    const cVendor = await validateVendorAccessCode(cAccessCode)
                    if (cVendor['userType'] === UserPrivilege.InvalidAccessCode)
                        continue
                    else
                        contractorVids.push(cVendor['vendorId'])
                }
            } else {
                contractors = [];
                contractorVids = [];
            }
        }

        contractors = contractors.length > 0 ? JSON.stringify(contractors.map(a => a.toString())) : '[]'
        contractorVids = contractorVids.length > 0 ? JSON.stringify(contractorVids.map(a => a.toString())) : '[]'
    } else {
        contractors = '[]'
        contractorVids = '[]'
    }

    let mb_kind = siteData['mb_kind'] || null;
    if (mb_kind) {
        if(typeof mb_kind == 'string') {
            try {
                mb_kind = JSON.parse(mb_kind)
            } catch (e) {
                mb_kind = mb_kind.split(',')
            }
        }
        if (!Array.isArray(mb_kind))
            throw new Error("Kinds need to be sent as an array")

        mb_kind = mb_kind.toString()
    }

    let trades = siteData['trades'] || siteData['contracts'] || null

    if (trades) {
        if(typeof trades == 'string') {
            try {
                trades = JSON.parse(trades)
            } catch (e) {
                trades = trades.split(',')
            }
        }
        if (!Array.isArray(trades))
            throw new Error("Trades need to be sent as an array")

        const query = "SELECT TRIM(TRAILING ')' FROM SUBSTRING(COLUMN_TYPE,6))  as enumVal  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'maptile_building' AND COLUMN_NAME = 'mb_contract_type'"
        let enumVal = (await awaitSafeQuery(query))[0]['enumVal']
        let result = enumVal.replace(/\'/g, '')
        let allowedTrades = result.split(',')
        let checker = (arr, target) => target.every(v => arr.includes(v))
        if (!checker(allowedTrades, trades))
            throw new Error("Invalid Trades Specified")
        trades = trades.toString()
    }

    let externalSource = siteData['externalSource'] ||  siteData['externalsrc'] || null
    let externalID = siteData['externalID'] || siteData['externalpropertyid'] || null

    if(externalSource)
    {
        if(!['BossLM', 'EMCOR', 'ServiceChannel', 'Aspire', 'Corrigopro', 'Command7','FMPilot', 'CaseFMS', 'SMSOne'].includes(externalSource))
            throw new Error("Invalid External Source Specified")
    }
    if(externalID)
    {
        if(!externalSource)
            throw new Error("External Source is required when External ID is specified")
        const checkExternalID = await awaitQuery(`SELECT * from maptile_building where mb_user_id=? AND mb_external_id=? AND mb_external_src=?`, [vendorID, externalID, externalSource])
        if(checkExternalID && checkExternalID.length > 0)
            throw new Error("External ID already exists")
    } else {
        //Since id is not there it should not save the source as well.
        externalSource = null;
    }
    let quickBooksCustomerID = siteData['quickbooksexternalid'] || null
    let scPin = siteData['scpin'] || null
    let scStoreId = siteData['scstoreid'] || null
    //defensive coding
    if(scPin == "null" || scPin == "undefined")
        scPin = null
    if(scStoreId == "null" || scStoreId == "undefined")
        scStoreId = null
    if(quickBooksCustomerID == "null" || quickBooksCustomerID == "undefined")
        quickBooksCustomerID = null
    let geo = siteData['geo'] || null
    if(!geo) geo = await getParcel(lat, lng)

    let workerOrderSystemID = siteData['workerOrderSystemID'] || null
    let workerOrderNTE = siteData['workerOrderNTE'] || null
    let workerOrderSubscriberID = siteData['workerOrderSubscriberID'] || null

    let stateId = await getState(state, countryId)
    if(!stateId)
    {
        if(cachedGeoData && cachedGeoData[0])
        {
            country = cachedGeoData[0]['countryCode']
            if (mapping.hasOwnProperty(country.toString().toUpperCase())) {
                countryId = mapping[country.toUpperCase()]
            } else {
                throw new Error(`Country should be one of the following: USA, Canada, Mexico. Country sent: ${country}`)
            }
            stateId = await getState(state, countryId)
        }
        else
        {
            cachedGeoData = await geocoder.reverse({ lat: lat, lon: lng });
            if(cachedGeoData && cachedGeoData[0])
            {
                country = cachedGeoData[0]['countryCode']

                //if puerto rico change to usa
                if(country == 'PR')
                {
                    country = 'USA'
                    countryId = 254
                }
                if (mapping.hasOwnProperty(country.toString().toUpperCase())) {
                    countryId = mapping[country.toUpperCase()]
                } else {
                    throw new Error(`Country should be one of the following: USA, Canada, Mexico. Country sent: ${country}`)
                }
                stateId = await getState(state, countryId)
            }
        }

    }
    if(!stateId)
        throw new Error("State does not exist, please send a valid state abbreviation.")
    let cityId = await getOrCreateCity(city, stateId, lat, lng)

    let maps =  siteData['maps'] || '[]'
    if (maps) {
        if (!Array.isArray(maps)) {
            if (typeof maps == 'string') {
                try {
                    maps = JSON.parse(maps)
                } catch (e) {
                    maps = maps.split(',')
                }
            }
        }
        maps = maps.length > 0 ? maps = JSON.stringify(maps.map(a => a.toString())) : '[]'
    }

    let mb_source = siteData['mb_source'] ?? null;
    let mb_building_status = siteData['mb_building_status'] ?? null;

    if (siteData['contracts']) {
        let trades = siteData['contracts']
        if (trades) {
            if (!Array.isArray(trades))
            {
                if(typeof trades == 'string') {
                    try {
                        trades = JSON.parse(trades)
                    } catch (e) {
                        trades = trades.split(',')
                    }
                }
            }

            const query = "SELECT TRIM(TRAILING ')' FROM SUBSTRING(COLUMN_TYPE,6))  as enumVal  FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'maptile_building' AND COLUMN_NAME = 'mb_contract_type'"
            let enumVal = (await awaitSafeQuery(query))[0]['enumVal']
            let result = enumVal.replace(/\'/g, '')
            let allowedTrades = result.split(',')
            let checker = (arr, target) => target.every(v => arr.includes(v))
            if (!checker(allowedTrades, trades))
                throw new Error("Invalid Trades Specified")
            trades = trades.toString()
            if(trades && trades.length>0){
                siteData['mb_contract_type'] = trades
            }
        }

    }

    const bRadius = siteData['bradius'] || 0;
    let data = {
        mb_name: name,
        mb_nickname: nickname,
        mb_address1: address1,
        mb_address2: address2,
        mb_city: cityId,
        mb_zip_code: zip,
        mb_state: stateId,
        mb_lat: lat,
        mb_long: lng,
        mb_status: mbStatus,
        mb_user_id: vendorID,
        mb_geo: serialize(geo),
        mb_contractor_vid: contractorVids,
        mb_contractor: contractors,
        mb_contract_type: trades,
        mb_client: client,
        mb_client_secondary: clientSecondary,
        mb_client_third: clientThird,
        mb_manager: manager,
        mb_manager_secondary: managerSecondary,
        mb_manager_third: managerThird,
        mb_min_zoom: autoShare.toString(),
        mb_max_zoom: '4',
        mb_radius: bRadius,
        mb_zone_id: zone,
        mb_user_type: '1',
        mb_country: countryId,
        mb_external_src: externalSource,
        mb_external_id: externalID,
        mb_notes: notes,
        mb_maps: maps,
        mb_ohours: ohours,
        mb_wo_system_id: workerOrderSystemID,
        mb_wo_nte: workerOrderNTE,
        mb_wo_subscriber_id: workerOrderSubscriberID,
        mb_quickbooks_customer_id: quickBooksCustomerID,
        mb_source: mb_source,
        mb_building_status: mb_building_status,
        mb_kind: mb_kind,
        mb_sf: sqft,
        mb_external_metadata: metadata,
        mb_timezone: mbTimezone
    }
    let cluster = siteData['clusterSites'] ?? true
    const insertedRecord = await insertObj('maptile_building', data)
    redisClient.del(`getapptm5:buildings:${vendorID}`);
    updateVendorSitesChargify(vendorID)
    setupPhotoSharing(insertedRecord['insertId'], vendorID, emails) //TODO: Not implemented yet
    if (cluster)
        syncFleetServer(vendorID)
    bus.emit('SITE_CREATED', insertedRecord.insertId);
    return insertedRecord.insertId;
}

const updateMbStatus = async (vendorID, siteID, newStatus) => {

    // Add validation to ensure newStatus is one of the allowed string values
    if (!["0", "1", "2"].includes(newStatus)) {
        return 'Invalid status value';
    }

    let obj = {
        mb_status: newStatus
    }
    await updateObj('maptile_building', obj, ['mb_id', 'mb_user_id'], [siteID, vendorID]);
    updateVendorSitesChargify(vendorID);
    emitSiteStatus(newStatus, siteID);
    redisClient.del(`getapptm5:buildings:${vendorID}`);
    return true;
}

const getFallbackCoordinates = (lat, lon) => {
    lat = parseFloat(lat);
    const lng = parseFloat(lon);
    return `(${lat - 0.001}, ${lng + 0.0015}),(${lat + 0.001}, ${lng + 0.0015}),(${lat + 0.001}, ${lng - 0.0015}),(${lat - 0.001}, ${lng - 0.0015})`;
};
const shareWithClient = async (siteId, clientVendorId, clientInternalId, clientSiteName) => {
    try {
        //await awaitSafeQuery('call InsertOrUpdateClientSiteData(?, ?, ?, ?)', [siteId, clientVendorId, clientInternalId, clientSiteName]);
        await adminServices.rollupClientSite(siteId, clientVendorId, clientInternalId, clientSiteName);
    } catch (ex) {
        console.log(ex)
    }
}
const getParcel = async (lat, lon) => {

    if (!lat || !lon) {
        throw new Error('Latitude and Longitude are required.');
    }

    try {

        const response = await fetch(`https://parcels.sitefotos.com/1/getdataserver?lat=${lat}&lon=${lon}`, {
            method: 'GET',
        });

        let parsedPoly = [];
        if (response.status === 200) {
            const data = await response.json();
            if (data.type === "Polygon") {
                parsedPoly = data.coordinates[0];
            } else if (data.type === "MultiPolygon") {
                for (const polygon of data.coordinates) {
                    parsedPoly.push(...polygon[0]);
                }
            }
        }

        if (!parsedPoly.length) {
            return getFallbackCoordinates(lat, lon);
        }

        let mapLatLng = parsedPoly.map(coord => `(${coord[1]},${coord[0]})`).join(',');
        return mapLatLng;

    } catch (error) {
        console.error(error);
        return getFallbackCoordinates(lat, lon);
    }
};



const setupPhotoSharing = async (siteID, vendorID, emails) => {
    if (!emails || emails.length === 0)
        return;
    if (!Array.isArray(emails))
        emails = emails.split(',').map(a => a.trim())
    await deleteObj('maptile_building_share', ['mbs_building', 'mbs_owner'], [siteID, vendorID])

    for(let email of emails) {
        //validate email address
        if (isEmailValid(email)) {

            let vendor = await getVendorByEmail(email)
            if (vendor) {
                let data = {
                    'mbs_building': siteID,
                    'mbs_owner': vendorID,
                    'mbs_user': vendor['vendorID'],
                };
                await insertObj('maptile_building_share', data)
            }
            else {
                console.log(`setupPhotoSharing: Vendor with email ${email} not found. Creating new vendor for site ${siteID}`)
                let newVendor = await addPhotoVendor(email, vendorID)
                if (newVendor) {
                    let data = {
                        'mbs_building': siteID,
                        'mbs_owner': vendorID,
                        'mbs_user': newVendor,
                    };
                    await insertObj('maptile_building_share', data)
                }
            }
        }
    }
}

const addPhotoVendor = async (email, vendorID) => {
    let vendorData = await getVendorByID(vendorID)
    let pool = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    let password = "";
    for (var i = 0; i < 10; i++) {
        password += pool.charAt(Math.floor(Math.random() * pool.length));
    }
    if (vendorData) {
        let data = {
            'email': email,
            'companyName': `Created By ${vendorData['vendorCompany']}`,
            'password': password,
            'source': 'sitefotos - photo sharing',
        }
        let vendor = await createVendor(data)
        console.log(`addPhotoVendor: Created new vendor ${vendor} for email ${email}`)
        return vendor;
        // These vendors do not recieve any welcome emails, confirmed through Mike

    }
    else {
        return null
    }
}

const moveSite = async (data) => {
    const { sourceSiteID, destinationVendorID, sourceSiteFormIDs, copyExternalID = false, deactivateSource = false, mapExistingServicesMaterials = false } = data;
    const copiedForms = {};
    let connection;
    try {
        connection = await getConnection();
        await connection.beginTransaction();

        // Fetch the source site details
        const sourceSiteRows = (await connection.execute('SELECT * FROM maptile_building WHERE mb_id = ? FOR UPDATE', [sourceSiteID]))[0];
        if (!sourceSiteRows || sourceSiteRows.length === 0) {
            throw new Error(`Source site ${sourceSiteID} not found.`);
        }
        const sourceSite = sourceSiteRows[0];
        const sourceVendorID = sourceSiteRows[0]['mb_user_id'];
        if (sourceVendorID == destinationVendorID) {
            return;
        }
        // Prepare data for the new site (duplicate)
        // Exclude fields that should not be duplicated or must be unique
        //NOTE: Add other fields that you want to copy from source site to new site.
        const includedFields = [
            'mb_user_type', 'mb_name', 'mb_nickname', 'mb_address1', 'mb_address2', 'mb_city', 'mb_zip_code', 'mb_country', 'mb_notes', 'mb_ohours', 'mb_locked',
            'mb_state', 'mb_lat', 'mb_long', 'mb_status', 'mb_geo', 'mb_floor_no', 'mb_property_type', 'mb_built_year', 'mb_sf', 'mb_operation_hour', 'mb_min_zoom', 'mb_max_zoom', 'mb_checkout_radius', 'mb_radius'
        ];
        const newSiteData = {};
        for (const key in sourceSite) {
            if (includedFields.includes(key)) {
                newSiteData[key] = sourceSite[key];
            }
        }
        // Insert the new site
        const insertResult = await insertObj('maptile_building', newSiteData, { poolToUse: connection });
        const newSiteID = insertResult.insertId;
        for (const formID of sourceSiteFormIDs) {
            //NOTE: This method needs to use connection to copy forms. so if something goes wrong it can rollback.
            //NOTE: This needs to execute in a before copy_site_resources call else it blocks the locking and causes deadlock.
            const newFormID = (await formServices.copyForms({ formIDs: [formID], vendorID: destinationVendorID, mapExistingServicesMaterials: mapExistingServicesMaterials, connection }))[0];
            if (newFormID) {
                copiedForms[formID] = newFormID;
            }
        }
        // Copy resources from source to new site
        //NOTE: Copy site resources will copy all the resources from source site to new site.
        await connection.execute('call copy_site_resources(?, ?, ?)', [sourceSiteID, newSiteID, copyExternalID]);
        if (deactivateSource) {
            await connection.execute(`UPDATE maptile_building SET mb_user_id = ?, mb_status = '0' WHERE mb_id = ?`, [sourceVendorID, newSiteID]);
            bus.emit('SITE_DISCONNECTED', newSiteID);
        } else {
            await connection.execute('UPDATE maptile_building SET mb_user_id = ? WHERE mb_id = ?', [sourceVendorID, newSiteID]);
        }
        // Now update the source site vendor ID to the new vendor ID
        await connection.execute('UPDATE maptile_building SET mb_user_id = ? WHERE mb_id = ?', [destinationVendorID, sourceSiteID]);
        // Check if the source site is in client rollup.

        //Check if previous site is in rollup. we are using new site ID here because we are copied the original site id and it's data to new site in above copy_site_resources call.
        const rollupCheck = (await connection.execute('select * from sitefotos_site_client_mapping_extended_ungrouped where sscm_site_id = ? and sscm_vendor_id = ?', [newSiteID, sourceVendorID]))[0];
        if (rollupCheck && rollupCheck.length > 0) {
            // If it is, we need to update the client site data for the new vendor ID
            for (const row of rollupCheck) {
                const clientInternalId = row['scs_client_internal_id'];
                const clientVendorId = row['scs_client_vendor_id'];
                await connection.execute('call InsertOrUpdateClientSiteData(?,?, ?, ?)', [sourceSiteID, clientVendorId, clientInternalId, null]);
            }

        }
        for (const formID in copiedForms) {
            const newFormID = copiedForms[formID];

            // Update all related tables to point to the new form ID
            await connection.execute('UPDATE sitefotos_forms_submitted SET sfs_form_id = ? WHERE sfs_form_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_breadcrumb_data SET sbd_profile_id = ? WHERE sbd_profile_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_wp_material_data SET swmd_profile_id = ? WHERE swmd_profile_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_sites_sessions SET sss_form_id = ? WHERE sss_form_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_wp_services_data SET swsd_profile_id = ? WHERE swsd_profile_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE maptile_vendor_pics SET mvp_form_id = ? WHERE mvp_form_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_forms_contractor_data SET sfcd_form_id = ? WHERE sfcd_form_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_forms_contractor_share SET sfcs_form_id = ? WHERE sfcs_form_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_work_orders SET swo_form_id = ? WHERE swo_form_id = ?', [newFormID, formID]);
            await connection.execute('UPDATE sitefotos_work_orders SET swo_form_template_id = ? WHERE swo_form_template_id = ?', [newFormID, formID]);

            // Update the form with the new vendor ID and site
            const randomNegativeID = -1 * Math.floor(Math.random() * 1000000);
            await connection.execute('UPDATE sitefotos_forms SET sf_id = ? WHERE sf_id = ?', [randomNegativeID, newFormID]);
            await connection.execute('UPDATE sitefotos_forms SET sf_updated = ?, sf_id = ? WHERE sf_id = ?', [Math.floor(Date.now() / 1000), newFormID, formID]);

            await connection.execute(`UPDATE sitefotos_forms SET sf_form_site = ?, sf_updated = ?, sf_id = ? WHERE sf_id = ?`, [JSON.stringify([sourceSiteID.toString()]), Math.floor(Date.now() / 1000), formID, randomNegativeID]);
        }
        await connection.commit();
        return newSiteID;
    } catch (err) {
        if (connection) {
            try {
                await connection.rollback();
                for (const formID in copiedForms) {
                    const newFormID = copiedForms[formID];
                    await awaitSafeQuery('UPDATE sitefotos_forms SET sf_active = ? WHERE sf_id = ?', ['3', newFormID], {useMainPool: true});
                }
            } catch (e) {
                console.error('Rollback failed:', e);
            }
        }
        console.error('copySite error:', err);
        throw err;
    } finally {
        if (connection) connection.release();
    }
}

const copySiteResources = async (vendorID, sourceSiteID, destinationSiteID, deactivateDestination = false) => {
    console.log(`copySiteResources: Copying resources from site ${sourceSiteID} to site ${destinationSiteID}`);
    let sourceSite = await awaitSafeQuery(`select mb_id from maptile_building where mb_user_id = ? and mb_id=?`, [vendorID, sourceSiteID], {useMainPool: true});
    let destinationSite = await awaitSafeQuery(`select mb_id from maptile_building where mb_user_id = ? and mb_id=?`, [vendorID, destinationSiteID], {useMainPool: true});
    if (sourceSite.length == 0 || destinationSite.length == 0) {
        throw new Error(`Invalid source or destination sites`)
    }
    await awaitSafeQuery('call copy_site_resources(?, ?, ?)', [sourceSiteID, destinationSiteID, 1]);
    if (deactivateDestination) {
        await awaitSafeQuery(`UPDATE maptile_building SET mb_status = '0' WHERE mb_id = ?`, [sourceSiteID]);
        bus.emit('SITE_DISCONNECTED', sourceSiteID);
    }
    return true;
}

const fixSiteContractors = async (vendorId) => {
    let sites = await awaitQuery(`select * from maptile_building where mb_user_id = ? and mb_status='1'`, [vendorId]);

    let i = 1;
    for (const site of sites) {
        console.log(`${i++} of ${sites.length}`)
        let contractors  = site.mb_contractor
        let contractorVids = []
        if (contractors) {
            if(typeof contractors == 'string') {
                try {
                    contractors = JSON.parse(contractors)
                } catch (e) {
                    contractors = contractors.split(',')
                }
            }
            if (!Array.isArray(contractors))  {
                throw new Error("Contractors when specified need to be an array")
            }
            if (contractors.length > 0) {
                //NOTE: Making a single loop by fetching all contractor in one request from DB using in operator.
                let contractorResults = await awaitQuery(`SELECT * from sitefotos_contacts where sf_contact_id in (?) AND sf_contact_vendorid=?`, [contractors, vendorId])
                if (contractorResults || contractorResults.length !== 0) {
                    for (let contractorResult of contractorResults) {
                        const cAccessCode = contractorResult['sf_contact_contractorid']
                        const cVendor = await validateVendorAccessCode(cAccessCode)
                        if (cVendor['userType'] === UserPrivilege.InvalidAccessCode)
                            continue
                        else
                            contractorVids.push(cVendor['vendorId'])
                    }
                } else {
                    contractors = [];
                    contractorVids = [];
                }
            }

            contractors = contractors.length > 0 ? JSON.stringify(contractors.map(a => a.toString())) : '[]'
            contractorVids = contractorVids.length > 0 ? JSON.stringify(contractorVids.map(a => a.toString())) : '[]'
        } else {
            contractors = '[]'
            contractorVids = '[]'
        }


        await updateObj(
          "maptile_building",
          {
              mb_contractor: contractors,
              mb_contractor_vid: contractorVids
          },
          ["mb_id"],
          [site.mb_id]
        )
    }

}

const siteDuplicateCheck = async (siteId, vendorId) => {
    try {
        const duplicates = await fleetdb.awaitQuery(`SELECT b.sfb_building_id as bid 
        FROM sitefotos_buildings a 
        INNER JOIN sitefotos_buildings  b 
        ON ST_Contains(ST_Buffer(b.sfb_building_geo, ST_Area(b.sfb_building_geo) * 0.7), a.sfb_building_geo) 
        WHERE a.sfb_building_id = $1 and b.sfb_vid = $2 and b.sfb_active ='1'
        AND a.ctid != b.ctid`,[siteId.toString(), vendorId.toString()]);
        return duplicates.map(a=>a.bid);
    }
    catch (ex) {
        return [];
        console.log(ex)
    }
}



const fs = require('fs');
const { connectIntegration } = require('@sentry/node')
const {find} = require("geo-tz");


async function writeToFile(fileName, data) {
    try {
        await fs.promises.writeFile(fileName, JSON.stringify(data, null, 2), 'utf-8');
        console.log(`Data written successfully to ${fileName}`);
    } catch (error) {
        console.error('Error writing to file:', error);
    }
}


// Utility function to split an array into chunks
function chunkArray(array, size) {
    const chunkedArray = [];
    for (let i = 0; i < array.length; i += size) {
        chunkedArray.push(array.slice(i, i + size));
    }
    return chunkedArray;
}

// Revised test function
const test = async () => {
    try {
        await withLock(redisClient, `site4s4f445500000000-2054`, async () => {
            console.log("here")
            let allBuildings = await awaitQuery(`select * from maptile_building where mb_user_id=2054 and mb_status='1'`);
            let scBuildings = allBuildings.filter(a => a.mb_external_src == 'ServiceChannel');

            // Split the scBuildings array into chunks of 5
            const buildingChunks = chunkArray(scBuildings, 50);

            let duplicatesFixed = [];
            let multipleDuplicates = [];
            let NoDuplicates = [];

            for (let chunk of buildingChunks) {

                const promises = chunk.map(async (scBuilding) => {
                    let data = await siteDuplicateCheck(scBuilding.mb_id, 2054);


                    if (data.length == 0) {
                        NoDuplicates.push({ mb_id: scBuilding.mb_id, mb_external_id: scBuilding.mb_external_id, mb_external_src: scBuilding.mb_external_src });
                    } else if (data.length == 1) {

                        let siteDetails = allBuildings.find(a => a.mb_id == data[0]);
                        if(!siteDetails) return;
                        if(siteDetails.mb_external_src == 'ServiceChannel')
                        {
                            console.log(`Duplicate found ${scBuilding.mb_id} and ${data[0]} both are from ServiceChannel`)
                            return;
                        }
                        await awaitSafeQuery('call copy_site_resources(?, ?, ?)', [scBuilding.mb_id, data[0], 1]);
                        await awaitSafeQuery(`UPDATE maptile_building SET mb_status = '0' WHERE mb_id = ?`, [scBuilding.mb_id]);
                        duplicatesFixed.push({ mb_id: scBuilding.mb_id, mb_external_id: scBuilding.mb_external_id, mb_external_src: scBuilding.mb_external_src, duplicate_id: data[0] });
                        console.log(`Fixed ${scBuilding.mb_id} copied to ${data[0]}`);
                    } else {
                        //check if one of them is not service channel
                        let filtered = data.filter(a => allBuildings.find(b => b.mb_id == a)?.mb_external_src != 'ServiceChannel');
                        if(filtered.length == 1)
                        {
                            let siteDetails = allBuildings.find(a => a.mb_id == filtered[0]);
                            if(!siteDetails) return;
                            if(siteDetails.mb_external_src == 'ServiceChannel')
                            {
                                console.log(`Duplicate found ${scBuilding.mb_id} and ${filtered[0]} both are from ServiceChannel`)
                                return;
                            }
                            await awaitSafeQuery('call copy_site_resources(?, ?, ?)', [scBuilding.mb_id, filtered[0], 1]);
                            await awaitSafeQuery(`UPDATE maptile_building SET mb_status = '0' WHERE mb_id = ?`, [scBuilding.mb_id]);
                            duplicatesFixed.push({ mb_id: scBuilding.mb_id, mb_external_id: scBuilding.mb_external_id, mb_external_src: scBuilding.mb_external_src, duplicate_id: filtered[0] });
                            console.log(`Fixed ${scBuilding.mb_id} copied to ${filtered[0]}`);
                        }

                    }
                });


                await Promise.all(promises);
            }

            await writeToFile('duplicatesFixed.json', duplicatesFixed);
            await writeToFile('multipleDuplicates.json', multipleDuplicates);
            await writeToFile('NoDuplicates.json', NoDuplicates);
        }, { ttl: 60 * 30, randomTTL: 10, failImmediately: true });
    } catch (ex) {
        console.log(ex);
    }
};

const emitSiteStatus = async (newStatus, buildingId) => {
    if (newStatus == 0 || newStatus == 2){
        bus.emit('SITE_DISCONNECTED', buildingId);
    }
    if (newStatus == 1){
        bus.emit('SITE_CREATED', buildingId);
    }
}
const fixClientModelSitesCron = async () => {
    try {

        let vendors = await awaitQuery(`SELECT vendor_id FROM maptile_vendors WHERE vendor_client_view_enabled = 'Y'`);
        for (let vendor of vendors) {
            console.log(`Processing vendor ${vendor.vendor_id}`);

            let users = await awaitQuery(`SELECT supu_contact_id, supu_id FROM sitefotos_user_permissions_users WHERE supu_vendor_id = ?`, [vendor.vendor_id]);
            let buildings = await awaitSafeQuery(`SELECT sscm_site_id, scs_client_internal_id FROM sitefotos_site_client_mapping_extended_ungrouped WHERE scs_client_vendor_id = ?`, [vendor.vendor_id]);
            for (let user of users) {
                let userBuildings = await awaitQuery(`SELECT ssc_data FROM sitefotos_subuser_configuration WHERE ssc_user_id = ?`, [user.supu_id]);
                let userBuildingIds = userBuildings.map((item) => item.ssc_data)[0] || [];
                let mappedBuildings = userBuildingIds.map((siteId) =>
                    buildings.find((b) => b.sscm_site_id == siteId)
                );
                let uniqueClientInternalIds = [
                    ...new Set(mappedBuildings.map((mb) => mb?.scs_client_internal_id || null))
                ];
                let allRelevantSiteIds = buildings
                    .filter((b) => uniqueClientInternalIds.includes(b.scs_client_internal_id))
                    .map((b) => b.sscm_site_id.toString());
                let uniqueSiteIds = [...new Set(allRelevantSiteIds)];
                let data = {
                    ssc_data: JSON.stringify(uniqueSiteIds.map(a => a.toString()))
                }
                await updateObj('sitefotos_subuser_configuration', data, ['ssc_user_id'], [user.supu_id])
            }
        }
    } catch (ex) {
        console.log(ex);
    }
};



module.exports = {
    createSite,
    clusterSites,
    modifySite,
    disconnectSite,
    moveSite,
    copySiteResources,
    updateSiteDetails,
    shareWithClient,
    emitSiteStatus,
    updateMbStatus,
    fixClientModelSitesCron,
    getState,
    getOrCreateCity,
    getParcel,
    setupPhotoSharing
}
