const mysql = require('../utils/db')
const { serialize, unserialize } = require('php-serialize');
const {awaitSafeQuery} = require("../utils/db");
const moment = require("moment/moment");
const {addServiceHistoryAuditTrail} = require("./services-trades-equipment.services");

const createService = async (vendorID, serviceData) => {
    try {
        const name = serviceData.name || null;
        if(!name)
            throw new Error('Name is required');
        const description = serviceData.description || null;
        const provider = serviceData.provider || null;
        const providerID =  serviceData.providerID || null;
        const serviceType = serviceData.serviceType || null;
        const tradeID = serviceData.tradeID || null;
        const serviceTypeID = serviceData.serviceTypeID || null;
        const serviceCategory = serviceData.serviceCategory || null;
        const serviceStatus = serviceData.serviceStatus ?? '1';
        const equipmentID = serviceData.equipmentID || null;
        let serviceOptions = serviceData.serviceOptions || null;

        if(typeof serviceOptions === 'object' && serviceOptions !== null)
            serviceOptions = JSON.stringify(serviceOptions);

        const data = {
            vs_vendor_id: vendorID,
            vs_service_name: name,
            vs_service_description: description,
            vs_service_type: serviceType,
            vs_service_status: serviceStatus,
            vs_service_category: serviceCategory,
            vs_service_options: serialize(serviceOptions),
            vs_provider: provider,
            vs_provider_id: providerID,
            vs_service_provider_meta1: serviceData.providerMeta1 ?? null,
            vs_service_provider_meta2: serviceData.providerMeta2 ?? null,
            vs_trade_id: tradeID,
            vs_service_type_id: serviceTypeID,
            vs_equipment_id: equipmentID
        }

        const insertedRecord = await mysql.insertObj('vendor_services', data);
        return insertedRecord.insertId;


    } catch(ex) {
        throw ex
    }

}

const activateService = async (vendorID, serviceID) => {
    try {
        const data = {
            vs_service_status: '1'
        }
        const updatedRecord = await mysql.updateObj('vendor_services', data, ['vs_service_id', 'vs_vendor_id'], [serviceID, vendorID]);
        return serviceID

    } catch(ex) {
        throw ex
    }
}

const deactivateService = async (vendorID, serviceID) => {
    try {
        const data = {
            vs_service_status: '0'
        }
        const updatedRecord = await mysql.updateObj('vendor_services', data, ['vs_service_id', 'vs_vendor_id'], [serviceID, vendorID]);
        return serviceID
    } catch(ex) {
        throw ex
    }
}

const updateServiceStatusSitefotosWpTable = async (vendorID, serviceID, status, userContactId) => {
    try {
        let service = await awaitSafeQuery(`SELECT * FROM sitefotos_wp_services_data WHERE swsd_id=?`, [serviceID]);
        // If the service is found in the database
        if (service.length > 0) {
            const currentService = service[0];
            const previousStatus = currentService.swsd_service_status;
            const now = moment().unix();
            if (
              (status == 'SENT' || status == 'PENDING')
              &&
              (previousStatus != 'APPROVED' && previousStatus != 'REJECTED' && previousStatus != 'INVOICED' && previousStatus != 'BILLED' && previousStatus != 'INVOICED_BILLED' && previousStatus != 'LOCKED')
            ) {
                // Status is either 'SENT' or 'PENDING', and previousStatus is not one of the specified values.
                const data = {
                    swsd_service_status: status
                }
                await mysql.updateObj(
                  'sitefotos_wp_services_data',
                  data,
                  ['swsd_id', 'swsd_vendor_id'],
                  [serviceID, vendorID]
                );
                await addServiceHistoryAuditTrail(
                  vendorID,
                  userContactId,
                  serviceID,
                  'STATUS_UPDATE',
                  'swsd_service_status',
                  previousStatus,
                  status
                );
                return serviceID
            }
            else if (status != 'SENT' && status != 'PENDING' && previousStatus != 'INVOICED' && previousStatus != 'BILLED' && previousStatus != 'INVOICED_BILLED'){
                // if approved we cannot change status of service apart from changing it to quickbooks related status, which is happening in quickbooks.service.js
                const data = {
                    swsd_service_status: status,
                    swsd_status_updated_by: userContactId,
                    swsd_status_updated_at: now
                };
                await mysql.updateObj(
                  'sitefotos_wp_services_data',
                  data,
                  ['swsd_id', 'swsd_vendor_id'],
                  [serviceID, vendorID]
                );
                await addServiceHistoryAuditTrail(
                  vendorID,
                  userContactId,
                  serviceID,
                  'STATUS_UPDATE',
                  'swsd_service_status',
                  previousStatus,
                  status
                );
                return serviceID
            }
            else {
                return serviceID
            }
        }

        return serviceID
    } catch(ex) {
        throw ex
    }
}

const addServiceNotes = async (vendorID, serviceID, notes, userContactId) => {
    try {
        const data = {
            swsd_notes: notes
        }
        await mysql.updateObj(
          'sitefotos_wp_services_data',
          data,
          ['swsd_id', 'swsd_vendor_id'],
          [serviceID, vendorID]
        );
        await addServiceHistoryAuditTrail(
          vendorID,
          userContactId,
          serviceID,
          'NOTE',
          'swsd_notes',
          '',
          notes
        );
        return serviceID
    } catch(ex) {
        throw ex
    }
}

module.exports = {
    createService,
    activateService,
    deactivateService,
    updateServiceStatusSitefotosWpTable,
    addServiceNotes
}
