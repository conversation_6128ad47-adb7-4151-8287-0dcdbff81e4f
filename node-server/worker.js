const { Worker } = require('bullmq');
const { ioredisPersistentClient } = require('./utils/ioredis-persistent'); 
const { closePool } = require('./utils/db'); 


const processEmailJob = async (job) => {
  console.log(`[Worker] Processing email job #${job.id} with data:`, job.data);
 
  await new Promise(resolve => setTimeout(resolve, 1000)); 
  console.log(`[Worker] Finished email job #${job.id}`);
  return { status: 'Completed', jobId: job.id };
};

const processReportJob = async (job) => {
  console.log(`[Worker] Processing report job #${job.id} with data:`, job.data);
  await new Promise(resolve => setTimeout(resolve, 2000)); 
  console.log(`[Worker] Finished report job #${job.id}`);
  return { status: 'Completed', jobId: job.id };
};




let workers = [];

const initializeWorkers = () => {
  console.log('[Worker] Initializing BullMQ workers...');

  const connectionOpts = {
    connection: ioredisPersistentClient, 
  };

  const emailWorker = new Worker('email', processEmailJob, connectionOpts);
  const reportWorker = new Worker('reports', processReportJob, connectionOpts);

  workers = [emailWorker, reportWorker]; 


  workers.forEach(worker => {
    worker.on('completed', (job, result) => {
      console.log(`[Worker][${worker.name}] Job #${job.id} completed. Result:`, result);
    });

    worker.on('failed', (job, err) => {
      
      console.error(`[Worker][${worker.name}] Job #${job?.id ?? 'N/A'} failed:`, err);
    });

    worker.on('error', err => {
    
        console.error(`[Worker][${worker.name}] Error:`, err);
    });

    worker.on('active', job => {
        console.log(`[Worker][${worker.name}] Job #${job.id} started.`);
    });

    worker.on('stalled', jobId => {
        console.warn(`[Worker][${worker.name}] Job #${jobId} stalled.`);
    });

     worker.on('progress', (job, progress) => {
        console.log(`[Worker][${worker.name}] Job #${job.id} progress: ${progress}%`);
    });
  });
 

  console.log(`[Worker] Started workers for queues: ${workers.map(w => w.name).join(', ')}`);
};


const gracefulShutdown = async () => {
  console.log('[Worker] Received shutdown signal. Closing workers...');
  try {
    await Promise.all(workers.map(worker => worker.close()));
    console.log('[Worker] All workers closed.');
    await closePool();
    console.log('[Worker] Database pool closed.');
  } catch (error) {
    console.error('[Worker] Error during graceful shutdown:', error);
  } finally {
    process.exit(0);
  }
};

if (!ioredisPersistentClient) {
  console.error('[Worker] ioredis client is not configured or available. Cannot start workers. Exiting.');
  process.exit(1);
}


if (['connecting', 'ready', 'reconnecting'].includes(ioredisPersistentClient.status)) {
    console.log(`[Worker] ioredis client already ${ioredisPersistentClient.status}. Initializing workers.`);
    initializeWorkers();
} else if (ioredisPersistentClient.status === 'wait') {
   
    console.log('[Worker] ioredis client waiting for connection. Listening for "ready" event...');
    ioredisPersistentClient.once('ready', () => { 
        console.log('[Worker] ioredis client ready.');
        initializeWorkers();
    });
  
    ioredisPersistentClient.once('end', () => {
         console.error('[Worker] ioredis client connection ended before becoming ready. Exiting.');
         process.exit(1);
    });
}
 else {
   
    console.error(`[Worker] ioredis client is in unexpected state: ${ioredisPersistentClient.status}. Exiting.`);
    process.exit(1);
}



process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

console.log('[Worker] Worker process started. Waiting for jobs...');
