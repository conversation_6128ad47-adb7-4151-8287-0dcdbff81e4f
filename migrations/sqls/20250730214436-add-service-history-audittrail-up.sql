ALTER TABLE sitefotos_wp_services_data
    ADD COLUMN swsd_status_updated_by INT NULL,
    ADD COLUMN swsd_status_updated_at BIGINT;

DROP TABLE IF EXISTS sitefotos_service_history_audit_trail;

CREATE TABLE sitefotos_service_history_audit_trail (
    sshat_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key: unique ID for each audit entry',

    sshat_vendor_id INT NOT NULL COMMENT 'FK to maple_vendors.vendor_id (who owns the service)',
    sshat_user_id INT NOT NULL COMMENT 'User who performed the action',
    sshat_service_id INT NOT NULL COMMENT 'FK to sitefotos_wp_services_data.swsd_id (which service was affected)',

    sshat_action VARCHAR(50) NOT NULL COMMENT 'Action taken (e.g., APPROVED, SENT, BILLED)',
    sshat_field_changed VARCHAR(100) COMMENT 'Field that was changed (e.g., status, snow_total)',
    sshat_old_value TEXT COMMENT 'Previous value of the field (if applicable)',
    sshat_new_value TEXT COMMENT 'New value of the field (if applicable)',

    sshat_note TEXT COMMENT 'Optional note for context (e.g. <NAME_EMAIL>)',
    sshat_created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when action was performed',

    -- Indexes
    INDEX idx_shat_vendor_id (sshat_vendor_id),
    INDEX idx_shat_service_id (sshat_service_id),
    INDEX idx_shat_action (sshat_action),

    -- Foreign key constraints
    CONSTRAINT fk_shat_vendor FOREIGN KEY (sshat_vendor_id)
       REFERENCES maptile_vendors(vendor_id)
       ON DELETE CASCADE

) COMMENT='Audit trail for actions performed on service history (per row single service)';

