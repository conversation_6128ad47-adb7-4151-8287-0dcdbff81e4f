
import {
    EventBus
} from '../eventbus.js';
import { generateuuidv4 } from '../helpermethods.js';

export default {

    state: {
        equipments: [],
        vehicles: [],
        vendors: [],
        maintenanceTasks: [],
        chatAlreadyLoaded: false,
        mini: false,
        pageCounter: 0,
        formjson: {
            pages: []
        },
        integrations: null,
        selectedItem: null,
        selectedPage: 0,
        mapimages: null,
        filename: "",
        filestatus: 1,
        dataLoaded: false,
        fileid: null,
        questionCounter: 1,
        formcjson: {
            components: [{
                    title: "Text",
                    icon: "mdi-text",
                    type: "text",
                    name: "",
                    placeHolder: "Short Text",
                    isRequired: false,
                    group: "Text",
                    htext: "This is a simple text input field. If you want to get longer input it is preferable to use the Paragraph field.",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    position: 3,
                    inputType: "text"
                },      {
                title: "Paragraph",
                type: "comment",
                name: "",
                group: "Text",
                text: "Text Box field. Use this instead of the text input field if you want the user to enter longer text.",
                position: 12,
                placeHolder: "",
                formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                isRequired: false,
                icon: "mdi-text-subject"
            },{
                    title: "Email",
                    icon: "email",
                    type: "text",
                    group: "Text",
                    name: "",
                    placeHolder: "<EMAIL>",
                    isRequired: false,
                    htext: "Email Input Field. Use this field if you want the user to fill in an  email address.",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    position: 6,
                    inputType: "email"
                }, {
                    title: "Date",
                    icon: "mdi-calendar",
                    type: "text",
                    name: "",
                    group: "Text",
                    isRequired: false,
                    inputType: "date",
                    formTypes: ["generic",  "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Date Input Field. This field allows you to choose a date. You can also select the [auto] setting to have the date field be greyed out and automatically filled with the current date.",
                    position: 4,
                    visible: true,
                    auto: false,
                },
                {
                    title: "Date/Time",
                    icon: "mdi-calendar-clock",
                    type: "text",
                    name: "",
                    group: "Text",
                    htext: "DateTime field. This field allows you to choose a date AND time. You can also select the [auto] setting to have the date-time field be greyed out and automatically filled with the current date-time.",
                    position: 9,
                    isRequired: false,
                    formTypes: ["generic",  "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    visible: true,
                    inputType: "datetime-local",
                    auto: false
                },{
                    title: "Location",
                    icon: "location_on",
                    type: "geo",
                    group: "Text",
                    name: "",
                    formTypes: ["generic", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Geo Location Field. This field uses site detection to automatically determine which site the user is on when completing a form.",
                    position: 5,
                    isRequired: false,
                    visible: true,
                },{
                    title: "Address",
                    icon: "location_on",
                    type: "address",
                    group: "Text",
                    name: "",
                    formTypes: ["generic", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Address field. This will allows you to select address using droping a pin on map or searching for address.",
                    position: 5,
                    isRequired: false,
                    visible: true,
                }, {
                    title: "Password",
                    icon: "vpn_key",
                    type: "text",
                    group: "",
                    name: "",
                    placeHolder: "",
                    formTypes: ["generic", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Password field. Use this field if you want the user to fill in a password.",
                    position: 7,
                    isRequired: false,
                    inputType: "password"
                },
                {
                    title: "Number",
                    icon: "fa-sort-numeric-asc",
                    type: "text",
                    group: "Text",
                    name: "0",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "inspection", 'largeSite'],
                    placeHolder: "",
                    htext: "Number input field. Use this field if you want the user to fill in a number. This is similar to a text input however it only permits numbers to be entered.",
                    position: 8,
                    isRequired: false,
                    inputType: "number",
                    scroller: true,
                },
                {
                    title: "Phone",
                    icon: "phone",
                    type: "text",
                    name: "",
                    group: "",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Phone field. Use this field if you want the user to enter a phone number.",
                    position: 10,
                    placeHolder: "************",
                    isRequired: false,

                    inputType: "tel"
                },
                {
                    title: "URL",
                    icon: "http",
                    type: "url",
                    group: "Text",
                    name: "",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "URL field. This field allows you to put a live URL link inside your form that if clicked, will open the corresponding web page in the user browser.  ",
                    position: 11,
                    url: "http://www.example.com",
                },
                {
                    title: "Radio",
                    type: "radiogroup",
                    name: "",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    group: "Choices",
                    htext: "Radio Group field. If you want your users to select between two to three mutually exclusive choices then use this field. A good use of this field is for yes/no questions. You can also use this field to build conditional questions.  If you select the [segment] setting, than the radio group appears on the form as a segment bar.",
                    position: 13,
                    isRequired: false,

                    icon: "mdi-radiobox-marked",
                    segment: false,
                    choices: [
                        "Yes",
                        "No"
                    ]
                },
                {
                    title: "Score",
                    type: "radiogroup",
                    name: "",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection","inspection", 'largeSite'],
                    group: "Choices",
                    htext: "Scoring field.",
                    position: 13,
                    isRequired: false,
                    inputType: "scoring",
                    icon: "mdi-counter",
                    segment: true,
                    choices: [
                        "0",
                        "1",
                        "2",
                        "3",
                        "4",
                        "5",
                        "N/A"
                    ]
                },
                {
                    title: "Checkboxes",
                    type: "checkbox",
                    name: "",
                    isRequired: false,
                    formTypes: ["generic", "plotOnMap", "workorderTemplate", "fleetInspection", ,"inspection", 'largeSite'],
                    group: "Choices",
                    htext: "Checkbox field. Like the radio group field this field allows the user to select choices but in this case multiple choices can be selected by the user of the form.  You can also use this field to build conditional questions.</P>",
                    position: 14,
                    icon: "mdi-checkbox-marked",
                    choices: [
                        "Red",
                        "Blue"
                    ]
                }, {
                    title: "Select",
                    type: "dropdown",
                    name: "",
                    isRequired: false,
                    group: "Choices",
                    formTypes: ["generic", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Select field. This field should be used instead of the radio group field if the number of choices is greater than 3 or 4. It presents a dropdown to the user to select one of the choices. You can also use this field to build conditional questions.</P>",
                    position: 15,
                    icon: "mdi-arrow-down-drop-circle",
                    choices: [
                        "Red",
                        "Blue"
                    ]

                },

                {
                    title: "Select Multiple",
                    type: "dropdownmultiple",
                    name: "",
                    isRequired: false,
                    group: "Choices",
                    formTypes: ["generic", "plotOnMap", "workorderTemplate", "fleetInspection", "inspection", 'largeSite'],
                    htext: "Select Multiple field. This field should be used instead of the checkbox field if the number of choices is greater than five. Similar to the checkbox field multiple choices can be selected by the user.",
                    position: 16,
                    icon: "list",
                    choices: [
                        "Red",
                        "Blue"
                    ]
                }, {
                    title: "Label",
                    type: "textdisplay",
                    name: "",
                    group: "",
                    htext: "Label field. This field used to display static text on the form for the user to give him some guidance/instructions.",
                    text: 'Label',
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    position: 17,
                    icon: "title",

                }, {
                    title: "Header",
                    type: "subHeader",
                    name: "",
                    group: "Text",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    htext: "Subheader field. Use this field to divide your page into sections and display titles of each section. Keep in mind that this is not an input field, the user cannot input anything into it.",
                    position: 18,
                    icon: "fa-header"
                }, {
                    title: "Photo",
                    type: "file",
                    name: "",
                    htext: "Photo Upload field. Use this field if you want the user to upload photos from his camera with the form. This field will launch the sitefotos camera and then will link the resulting photos back to this section of the form.",
                    position: 19,
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    isRequired: false,
                    group: "Other",
                    icon: "insert_photo"
                },
                {
                    title: "Signature",
                    type: "signaturepad",
                    name: "",
                    htext: "Signature field. Use this field if you want the user to submit his/her signature along with the form.",
                    position: 20,
                    isRequired: false,
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    group: "Other",
                    icon: "fa-pencil",

                },
                {
                    title: "Sketch",
                    type: "sketch",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    name: "",
                    group: "Other",
                    htext: "Sketch field. Use this field if you want the user to submit a hand drawn sketch along with the form. ",
                    position: 21,
                    isRequired: false,
                    icon: "fa-paint-brush",

                },
                {
                    title: "Weather",
                    type: "comment",
                    isWeatherInput: true,
                    formTypes: ["generic", "service", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    name: "",
                    group: "Other",
                    htext: "Weather field. Use this field to get current weather information from the user at the time of submission of the form.",
                    position: 22,
                    placeHolder: "",
                    isRequired: false,
                    icon: "fa-cloud"

                },
                {
                    title: "Panel",
                    group: "Other",
                    type: "panel",
                    name: "",
                    formTypes: ["generic", "service", "plotOnMap", "workorderTemplate", "fleetInspection", "scoring","inspection", 'largeSite'],
                    repeatable: false,
                    htext: "Panel field. The panel field is a versatile field which allows you to group similar fields together. The panel field can also be repeateable. An example of using panel field in house inspections is to group similar questions for each room together and make the panel repeatable by changing its properties. It will allow the user to submit similar information about all the rooms in the house. ",
                    position: 23,
                    isRequired: false,

                    icon: "fa-object-group",
                    elements: []
                },
                {
                    title: "ServiceChannel",
                    group: "Workorder",
                    type: "panel",
                    serviceChannel: true,
                    name: "",
                    repeatable: false,
                    formTypes: [ "workorderTemplate"],
                    isRequired: false,
                    htext: "Service Channel",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "FMPilot",
                    group: "Workorder",
                    type: "panel",
                    fmpilot: true,
                    name: "",
                    formTypes: [ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "FMPilot",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "Corrigopro",
                    group: "Workorder",
                    type: "panel",
                    Corrigopro: true,
                    name: "",
                    repeatable: false,
                    isRequired: false,
                    formTypes: [ "workorderTemplate"],
                    htext: "Corrigopro",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },{
                    title: "Emcor",
                    group: "Workorder",
                    type: "panel",
                    emcor: true,
                    name: "",
                    repeatable: false,
                    isRequired: false,
                    formTypes: [ "workorderTemplate"],
                    htext: "Emcor",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "Wizard",
                    group: "Workorder",
                    type: "panel",
                    wizard: true,
                    name: "",
                    repeatable: false,
                    isRequired: false,
                    formTypes:[ "workorderTemplate"],
                    htext: "Wizard",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "Command7",
                    group: "Workorder",
                    type: "panel",
                    Command7: true,
                    name: "",
                    formTypes: [ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "Command7",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "Aspire",
                    group: "Workorder",
                    type: "panel",
                    Aspire: true,
                    name: "",
                    formTypes: [ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "Aspire",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "CaseFMS",
                    group: "Workorder",
                    type: "panel",
                    CaseFMS: true,
                    name: "",
                    formTypes: [ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "CaseFMS",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "SMSOne",
                    group: "Workorder",
                    type: "panel",
                    SMSOne: true,
                    name: "",
                    formTypes:[ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "SMSOne",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "Sitefotos",
                    group: "Workorder",
                    type: "panel",
                    sitefotos: true,
                    name: "",
                    formTypes:[ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "Sitefotos",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "API",
                    group: "Workorder",
                    type: "panel",
                    api: true,
                    name: "",
                    formTypes:[ "workorderTemplate"],
                    repeatable: false,
                    isRequired: false,
                    htext: "API",
                    position: 23,
                    elements: [],
                    icon: "assignment_turned_in"
                },
                {
                    title: "Service",
                    group: "TM",
                    type: "service",
                    name: "",
                    formTypes: ["generic", "service", , "workorderTemplate", "inspection", 'largeSite'],
                    isRequired: false,
                    htext: "Service Field. This field is used to log services performed in the field. To use this field, first configure some services in your account and then select them below. Add multiple services for multiple services.",
                    position: 23,
                    icon: "assignment_turned_in",
                    service: -1,
                    serviceType: 'AsTask'
                },
                {
                    title: "Material",
                    group: "TM",
                    type: "material",
                    name: "",
                    formTypes: ["generic", "service",  "workorderTemplate", "inspection", 'largeSite'],
                    isRequired: false,
                    htext: "Material Field. This field is used to log materials used in the field. To use this field first configure some materials in your account and then select them below.",
                    position: 23,
                    icon: "build",
                    material: -1
                },
                {
                    title: "Add Materials",
                    group: "TM",
                    type: "material",
                    formTypes: ["generic", "service",  "workorderTemplate", "inspection", 'largeSite'],
                    name: "",
                    isRequired: false,
                    htext: "Material Field. This field is used to log materials used in the field. To use this field first configure some materials in your account and then select them below. You can select either multiple material in single item or add Multiple fields for multiple materials. When you use same item and select multiple materials, it will allow the user to create fields on the fly. If no item is selected in dialogue, it will allow to select from any materials in the account.",
                    position: 23,
                    icon: "build",
                    material: []
                },
                {
                    title: "New Issue",
                    group: "TM",
                    type: "issues",
                    formTypes: ["generic", "service", "workorderTemplate", "fleetInspection", "inspection", 'largeSite'],
                    name: "",
                    htext: "Issue Field. This field is used to log issues in the field. It is better to use this field instead of text fields for logging issues.",
                    position: 23,
                    icon: "note_add",

                },
                {
                    title: "Crew",
                    group: "TM",
                    type: "crew",
                    formTypes: ["generic", "service",  "workorderTemplate", "fleetInspection", "inspection"],
                    name: "",
                    htext: "Crew Selector Field. This field allows selection of crew in the field.",
                    position: 23,
                    icon: "person_add",
                },
                {
                    title: "Crew Lead",
                    type: "manageCrew",
                    name: "",
                    group: "TM",
                    elements: [
                        {
                            title: "Employee",
                            type: "dynamicDropdown",
                            name: "",
                            isRequired: true,
                            dropdownValueType: "Employee",
                            listItems: [],
                            isManageCrewField: true
                        },
                    ],
                    formTypes: ["largeSite"],
                    htext: "Employee manage field on site. App user can check in/out for the employees on field.",
                    position: 23,
                    dropdownValueType: "Employee",
                    icon: "person_add",
                },
                {
                    title: "Site Info",
                    group: "TM",
                    type: "siteinfo",
                    formTypes: ["generic", "service",  "workorderTemplate", "inspection", 'largeSite'],
                    name: "",
                    htext: "SiteInfo Field: This field displays site information for the site on which the form is being filled, including notes, operating hours and maps etc that have been configured for the site.",
                    position: 23,
                    icon: "mdi-information",
                },
                {
                    title: "Vehicle",
                    type: "dropdown",
                    name: "",
                    isRequired: false,
                    group: "FleetInspection",
                    formTypes: ["fleetInspection"],
                    htext: "Vehicle field. It presents a dropdown to the user to select one of the choices for the vehicles they are inspecting. You can also use this field to build conditional questions.</P>",
                    isFleetVehicleList: true,
                    selectFromAllItems: true,
                    position: 15,
                    icon: "mdi-arrow-down-drop-circle",
                    choices: []
                },
                {
                    title: "Equipment",
                    type: "dropdown",
                    name: "",
                    isRequired: false,
                    group: "FleetInspection",
                    formTypes: ["fleetInspection"],
                    htext: "Equipment field. It presents a dropdown to the user to select one of the choices for the equipment. You can also use this field to build conditional questions.</P>",
                    isFleetEquipmentList: true,
                    selectFromAllItems: true,
                    position: 15,
                    icon: "mdi-arrow-down-drop-circle",
                    choices: []
                },
                {
                    title: "Meter Entry",
                    icon: "mdi-counter",
                    type: "text",
                    group: "FleetInspection",
                    formTypes: ["fleetInspection"],
                    name: "",
                    placeHolder: "",
                    htext: "Meter Entry field. Use this field if you want the user to fill in a meter entry for inspection form. This is similar to a text input however it only permits numbers to be entered.",
                    position: 8,
                    isMeterEntry: true,
                    isRequired: false,
                    inputType: "number",
                    scroller: true,
                },
                {
                    title: "Pass/Fail",
                    type: "radiogroup",
                    name: "",
                    group: "FleetInspection",
                    formTypes: ["fleetInspection"],
                    htext: "Pass/Fail field. Use this field if you want the user to select between two mutually exclusive choices. When user selects Fail for the item, it will create issue for the selected vehicle. You can also use this field to build conditional questions.  If you select the [segment] setting, than the radio group appears on the form as a segment bar.",
                    position: 13,
                    isFleetPassFail: true,
                    isRequired: false,
                    isNAEnabled: false,
                    icon: "mdi-radiobox-marked",
                    segment: true,
                    choices: [
                        "Pass",
                        "Fail"
                    ]
                },
                {
                    title: "Assign to",
                    type: "dynamicDropdown",
                    isRequired: false,
                    name: "",
                    group: "FleetInspection",
                    formTypes: ["fleetInspection"],
                    htext: "Employee Selector Field. This field allows selection of employees in the field for fleet vehicle or equipment assignment.",
                    position: 13,
                    listItems: [],
                    dropdownValueType: "Employee",
                    icon: "person_add",
                },
            ]
        },
        tempprofile: {
            id: 0,
            aid: 0,
            crew: false,
            checkinout: false,
            photos: true,
            notes: true,
            cat: '',
            name: '',
            bid: 0,
            maps: '',
            forms: '',
            profiletype: '1',
            routeserve: 'Assign Later',
            routecontact: null,
            selectedcategory: null,
        },
        tempactive: {
            id: 0,
            aid: 0,
            maps: '',
            bid: '',
            routeserve: 'Assign Later',
            routecontact: null
        },
        defactive: {
            id: 0,
            aid: 0,
            maps: '',
            bid: '',
            routeserve: 'Assign Later',
            routecontact: null
        },
        accountflags: {
            ag: false,
            bc: false,
            tm: false
        },
        defprofile: {
            id: 0,
            aid: 0,
            crew: false,
            checkinout: false,
            name: '',
            cat: '',
            maps: '',
            forms: '',
            bid: 0,
            photos: true,
            notes: true,
            profiletype: '1',
            routeserve: 'Assign Later',
            routecontact: null,
            selectedcategory: null,
        },
        vendorPaymentLock: 0,
        count: 1,
        step: 1,
        buildings: null,
        buildingdetailed: null,
        employees: [],
        contacts: [],
        allContacts: [],
        clientLeads: [],
        materials: [],
        initialpicsloaded: false,
        allpicsloaded: false,
        services: [],
        ownFontEndServices: [],
        subContractedServices: [],
        maps: null,
        pics: [],
        dispatches: [],
        routes: [],
        forms: [],
        activityTaskType: [],

        timetracking: null,
        accessCode: null,
        photocount: 0,
        extendedbuilding: null,
        extendedkeys: null,
        vendorSettings: {
            svs_distance_unit: 'mi',
            svs_volume_unit: 'gallon(us)',
            svs_overtime_hours: 40,
            svs_overtime_rate_multiplier: 1.5
        },
        userUDID: null,
        currentPage: "Photos",
        apikey: 'AIzaSyCHffyE3zbfCDeezpIT1pt8-mdgwYCmYvY',
        dataURL: myBaseURL+"/node/vpics/bodata",
        addUpdateProfileAssignURL: myBaseURL +"/vpics/addupdateactiveprofile",
        getProfileAssignedURL: myBaseURL +"/vpics/getprofiles2",
        getProfileURL: myBaseURL +"/vpics/getprofiles",
        getUserURL: myBaseURL +"/vpics/getuserid",
        addUpdateMaterialURL: myBaseURL +"/vpics/addupdatematerial",
        addUpdateServiceURL: myBaseURL +"/vpics/addupdateservice",
        addUpdateProfileURL: myBaseURL +"/vpics/addupdateprofile",
        addUpdateContact: myBaseURL + "/node/contact/contact",
        getBuildingURL: myBaseURL + "/myaccount/getbuildinginfo2",
        deleteBuildingURL: myBaseURL + "/myaccount/deletebuilding",
        saveBuildingURL: myBaseURL + "/myaccount/savebuildingbo",
        saveNewBuildingURL: myBaseURL + "/node/sites/save-site",
        copySiteResourcesURL: myBaseURL + "/node/sites/copy-site-resources",
        getMaps: myBaseURL + "/node/maps/map-layers",
        getPicturesURL: myBaseURL + "/vpics/bogetphotos",


        getSharedPicturesURL: myBaseURL + "/vpics/getsharedpics2",
        addUpdateDispatchURL: myBaseURL + "/vpics/addupdatedispatch",
        addUpdateRouteURL: myBaseURL + "/vpics/addupdateroutes",
        getFormsURL: myBaseURL + "/node/forms/forms2",
        openFormURL: myBaseURL + "/vpics/getformsbyid",
        saveFormURL: myBaseURL + "/node/forms/save-form",
        submitForm: myBaseURL + "/vpics/uploadformv4",
        breadcrumb: myBaseURL + "/vpics/uploadbreadcrumb",
        uploadissuev3: myBaseURL + "/vpics/uploadissuev3",
        formStatusURL: myBaseURL + "/node/forms/change-status",
        boSEmailGetURL: myBaseURL + "/vpics/bosemailsget",
        boSitesGetURL: myBaseURL + "/vpics/bositesget",
        siteByIDURL: myBaseURL + "/node/sites/id",
        boContactsGetURL: myBaseURL + "/vpics/bocontactsget",
        boServicesGetURL: myBaseURL + "/node/services/boservicesget",
        boMaterialsGetURL: myBaseURL + "/vpics/bomaterialsget",
        getAppResultsURL: myBaseURL + "/vpics/gettmdata",
        getAppIssueResultsURL: myBaseURL + "/vpics/getissuedata",
        getBreadCrumbsURL: myBaseURL + "/vpics/breadcrumbs2",
        getSettingsURL: myBaseURL + "/vpics/getvendorsettings",
        setSettingsURL: myBaseURL + "/vpics/setvendorsettings",
        getVehiclesURL: `${myBaseURL}/node/fleet/vehicles`,
        getEquipmentsURL: `${myBaseURL}/node/fleet/equipment`,
        getVendorsURL: `${myBaseURL}/node/fleet/vendors`,
        getMaintenanceTasksURL: `${myBaseURL}/node/fleet/maintenanceTask`,
    },

    actions: {
        async submitIssue(context, params) {
            let request = await fetch(context.getters.getUploadissuev3, {
                "headers": {
                    "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
                },
                "body": params,
                "method": "POST",
            });
            return await request.json();
        },
        async sendBreadcrumb(context, params) {
            const {type, accessCode, email, date, siteid, formid, formSubmissionID} = params;
            params = new URLSearchParams({
                'type': type,
                'accessCode': accessCode,
                'email': email,
                'dt': date,
                'deviceType': context.getters.getDeviceType,
                'deviceModel': context.getters.getDeviceModel,
                'appVersion': '1.0.1',
                'lat': 0,
                'lon': 0,
                'uuid': generateuuidv4(),
                'formSubmissionID': formSubmissionID,
                'siteid': siteid,
                'profileID': formid,
            });
            const sessionID = context.getters.getUserUDID;
            if (sessionID) {
                params.append('udid', sessionID);
            }
            let request = await fetch(context.getters.getSubmitBreadcrumbURL, {
                "headers": {
                    "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
                },
                "body": params,
                "method": "POST",
            });
            return await request.json();
        },
        async loadVehicles(context, refresh = false){
            const state = context.state;
            if (state.vehicles.length > 0 && refresh === false) {
                return;
            }
            const response = await fetch(state.getVehiclesURL);
            if (!response.ok) return
            context.commit('setVehicles', await response.json());
        },
        async loadEquipments(context, refresh = false){
            const state = context.state;
            if (state.equipments.length > 0 && refresh === false) {
                return;
            }
            const response = await fetch(state.getEquipmentsURL);
            if (!response.ok) return
            context.commit('setEquipments', await response.json());
        },
        async loadVendors(context, refresh = false){
            const state = context.state;
            if (state.vendors.length > 0 && refresh === false) {
                return;
            }
            const response = await fetch(state.getVendorsURL);
            if (!response.ok) return
            context.commit('setVendors', await response.json());
        },
        async loadMaintenanceTasks(context, refresh = false){
            const state = context.state;
            if (state.maintenanceTasks.length > 0 && refresh === false) {
                return;
            }
            const response = await fetch(state.getMaintenanceTasksURL);
            if (!response.ok) return
            context.commit('setMaintenanceTasks', await response.json());
        },

        async addContact({ state, dispatch }, payload) {

            const data = {
                accessCode: state.accessCode,
                sfcontactid: 0,
                sfcontactprofilepic: payload.cprofilepic,
                sfcontactfname: payload.cfname,
                sfcontactlname: payload.clname,
                sfcontactaddress1: payload.cadd1,
                sfcontactaddress2: payload.cadd2,
                sfcontactcity: payload.ccity,
                sfcontactzip: payload.czip,
                sfcontactemail: payload.cemail,
                sfcontactphone: payload.cphone,
                sfcontactmobile: payload.cmobile,
                sfcontactstate: payload.cstate,
                sfcontacttype: payload.ctype,
                sfcontactcountry: payload.ccountry,
                sfcontactactive: payload.cactive,
                sfcontactbreadcrumbs: payload.bc,
                sfcontactnotes: payload.cnotes,
                sfcontractorgroups: payload.cgroups || [],
                sfcontacttrades: payload.ctrades,
                sfcontacttitle: payload.ctitle,
                sfcontactcompanyid: payload.ccompanyid,
                sfcontactcompanybranch: payload.ccompanybranch,
                sfcontactcid: payload.ccid,
                sfcontactcontractorid: payload.ccontractorid,
                sfcontactclockinself: payload.sfcontactclockinself,
                sfcontactclockinother: payload.sfcontactclockinother,
                sfcontactquickbooksid: payload.quickbooksid,
                sfcontactaspireemail: payload.aspireemail,
                sfcontactaspirepin: payload.aspirepin,
                sfcontactdefaultsiteslist:payload.sfcontactdefaultsiteslist,
                sfcontactsupervisor: payload.csupervisor,
            };

            try {

                const response = await fetch(state.addUpdateContact, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const createdContactId = await response.json();

                await dispatch('fetchContacts');
                EventBus.$emit('ContactAdded', createdContactId);

                return createdContactId;

            } catch (e) {
                EventBus.$emit('ContactAddError', e);
                return null;
            }
        },

        initialPicsAction(context, payload) {
            const state = context.state;
            return new Promise((resolve, reject) => {
                const params = new URLSearchParams();
                params.append('accessCode', state.accessCode);
                params.append('offset', 0);
                params.append('limit', 240);
                axios.post(state.getPicturesURL, params)
                    .then(response => {
                        context.commit('commitInitialPics', response.data);
                        resolve();

                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                        reject();
                    })
            });
        },


        saveActive(context, payoad) {
            const state = context.state;
            return new Promise((resolve, reject) => {

                var flagnewprofileactive = state.tempactive.aid == 0 ? true : false;
                var cat = "";

                var tcontact;
                if (state.tempactive.routeserve == 'Assign Later')
                    tcontact = -2;
                else if (state.tempactive.routeserve == 'All users on my account')
                    tcontact = -1;
                else
                    tcontact = state.tempactive.routecontact;

                var profileID = state.tempactive.id;
                const params2 = new URLSearchParams();
                params2.append('accessCode', state.accessCode);
                params2.append('mspacontact', tcontact);
                params2.append('mspamaps', state.tempactive.maps);
                params2.append('mspaforms', "");
                params2.append('mspaid', state.tempactive.aid);
                params2.append('mspabid', state.tempactive.bid);
                params2.append('mspapid', profileID);
                params2.append('mspaactive', 1);
                axios.post(state.addUpdateProfileAssignURL, params2).then(response => {
                        var profileAssignedID = state.tempprofile.aid;
                        const params3 = new URLSearchParams();
                        params3.append('accessCode', state.accessCode);
                        var p1 = axios.post(state.getProfileAssignedURL, params3);

                        Promise.all([p1]).then(function (values) {
                                if (typeof values[0].data !== 'undefined') {
                                    context.commit('commitAssignedProfiles', values[0].data);
                                }
                                resolve();
                            })
                            .catch(function (err) {
                                console.log('A promise failed to resolve', err);
                            });

                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                        reject();
                    })






            });
        },
        initialDataAction(context, payload) {
            const state = context.state;
            return new Promise((resolve, reject) => {

                axios.get(`${state.dataURL}?accessCode=${state.accessCode}`)
                    .then(response => {
                        if (typeof response.data !== 'undefined') {
                            context.commit('commitInitialData', response.data);
                            resolve();
                        } else {
                            reject();
                        }
                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                        reject();
                    })
            });
        },
        copySiteResources(context, payload) {
            const state = context.state;
            return new Promise((resolve, reject) => {
            const params = new URLSearchParams();
            params.append('sourcebid', payload.sourcebid);
            params.append('destinationbid', payload.destinationbid);
            params.append('accessCode', state.accessCode);
            axios.post(state.copySiteResourcesURL, params)
                .then(response => {
                    axios.get(`${state.dataURL}?accessCode=${state.accessCode}`)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {
                                context.commit('commitInitialData', response.data);
                                resolve();
                            } else {
                                reject();
                            }
                        })
                        .catch(e => {
                            reject();
                            EventBus.$emit('AjaxError', e);
                        });
                })
                .catch(e => {
                    reject();
                    EventBus.$emit('AjaxError', e);
                });
            });
        },

        saveBuilding(context, payload) {
            const state = context.state;

            return new Promise((resolve, reject) => {
                const params = new URLSearchParams();
                params.append('accessCode', state.accessCode);

                params.append('lat', payload.lat);
                params.append('lng', payload.lng);
                params.append('name', payload.name);
                params.append('address', payload.address);
                params.append('city', payload.city);
                params.append('state', payload.state);
                params.append('externalpropertyid', payload.externalpropertyid ?? '');
                params.append('externalsrc', payload.externalprovider ?? '');
                params.append('mb_status', payload.mb_status ?? '1')
                params.append('bradius', payload.bradius);

                if(payload.scpin)
                    params.append('scpin', payload.scpin);
                if(payload.scstoreid)
                    params.append('scstoreid', payload.scstoreid);
                if(payload.emails)
                    params.append('emails', payload.emails);
                params.append('zip', payload.zip);
                if(payload.geo)
                    params.append('geo', payload.geo);
                if(payload.autoshare)
                    params.append('autoshare', payload.autoshare);
                if(payload.zone)
                    params.append('zone', payload.zone);
                if(payload.client)
                    params.append('client', payload.client);
                if(payload.client_secondary)
                    params.append('client_secondary', payload.client_secondary);
                if(payload.mb_source)
                    params.append('mb_source', payload.mb_source);
                if(payload.mb_building_status)
                    params.append('mb_building_status', payload.mb_building_status);
                if(payload.client_third)
                    params.append('client_third', payload.client_third);
                if(payload.contractor)
                    params.append('contractor', payload.contractor);
                if(payload.manager)
                    params.append('manager', payload.manager);
                if(payload.managersecondary)
                    params.append('managersecondary', payload.managersecondary);
                if(payload.managerthird)
                    params.append('managerthird', payload.managerthird);
                if(payload.maps)
                    params.append('maps', payload.maps);
                if(payload.ohours || payload.ohours == '') // need to allow empty notes
                    params.append('ohours', payload.ohours);
                if(payload.notes || payload.notes == '') // need to allow empty notes
                    params.append('notes', payload.notes);
                if(payload.contracts)
                    params.append('contracts', payload.contracts);
                if(payload.mb_kind)
                    params.append('mb_kind', payload.mb_kind)
                if(payload.country)
                    params.append('country', payload.country)
                if(payload.sqft)
                    params.append('sqft', payload.sqft)
                if (payload.externalquickbooksid)
                    params.append('quickbooksexternalid', payload.externalquickbooksid);
                if (payload.bid) {
                    params.append('bid', payload.bid);
                    axios.post(state.saveBuildingURL, params)
                        .then(response => {
                            let mybuildid;
                            if (response.data == "max") {
                                alert('You have reached the maximum number of buildings allowed per account during the trial period. Please sign up for a paid membership (Settings > Subscription) to add more properties.');
                            } else if (response.data == 'join') {
                                alert('Your trial period has expired and you have not signed up for a paid membership. Please sign up for a paid membership (Settings > Subscription) to add properties.');
                            } else if (response.data == 'past') {
                                alert('Your account is past due. Please update your payment information (Settings > Subscription) to add properties.');
                            } else if (response.data != 0) {
                                mybuildid = response.data;
                            } else {
                                alert("Building has been not saved,Please try again.");
                            }
                            if (mybuildid) {
                                fetch(`${state.siteByIDURL}?id=${mybuildid}`).then(response => response.json()).then(data => {
                                    context.commit('commitSiteByID', data);
                                    resolve(mybuildid);

                                }).catch(e => {
                                    EventBus.$emit('AjaxError', e);
                                    reject();
                                })
                            } else {
                                //Some how building did not got saved and spinner won't stop due to reject not being handled.
                                reject();
                            }
                        }).catch(e => {
                            console.log(e)
                            EventBus.$emit('AjaxError', e);
                            reject()
                        })



                } else {
                    axios.post(state.saveNewBuildingURL, params)
                        .then(response => {
                            let mybuildid;
                            if (response.data == "max") {
                                alert('You have reached the maximum number of buildings allowed per account during the trial period. Please sign up for a paid membership (Settings > Subscription) to add more properties.');
                            } else if (response.data == 'join') {
                                alert('Your trial period has expired and you have not signed up for a paid membership. Please sign up for a paid membership (Settings > Subscription) to add properties.');
                            } else if (response.data == 'past') {
                                alert('Your account is past due. Please update your payment information (Settings > Subscription) to add properties.');
                            } else if (response.data != 0) {
                                mybuildid = response.data;
                            } else {
                                alert("Building has been not saved,Please try again.");
                            }

                            if (mybuildid) {
                                fetch(`${state.siteByIDURL}?id=${mybuildid}`).then(response => response.json()).then(data => {
                                    context.commit('commitSiteByID', data);
                                    resolve(mybuildid);

                                }).catch(e => {
                                    EventBus.$emit('AjaxError', e);
                                    reject();
                                })
                            } else {
                                //Some how building did not got saved and spinner won't stop due to reject not being handled.
                                reject();
                            }
                        })
                        .catch(e => {
                            console.log(e)
                            EventBus.$emit('AjaxError', e);
                            reject()
                        })
                }



            })
        },
        updateFields(state, payload) {

            var currentItem = state.selectedItem;
            var whatChanged;
            var toChange;

            if (payload.item.inputType == "date" || payload.item.inputType == 'datetime-local') {
                if (payload.key == 'auto' && payload.value == true) {

                    Vue.set(currentItem, 'isRequired', false)
                } else if (payload.key == 'auto' && payload.value == false) {
                    Vue.set(currentItem, 'visible', 'true')

                }
            }


            Vue.set(currentItem, payload.key, payload.value)

            //  payload.item[payload.key] = payload.value;
            //  state.form.pages[currentPage].elements[currentItem] = payload
        },
        saveKeyValue(context, payload) {
            const state = context.state;
            return new Promise((resolve, reject) => {
                const params = new URLSearchParams();
                params.append('accessCode', state.accessCode);
                params.append('mblabel', payload.label);
                params.append('mbid', payload.kid);
                params.append('mbkey', payload.key);
                params.append('mbfilter', "0");
                params.append('mbtype', "text");
                axios.post(myBaseURL + "/vpics/addupdatekey", params)
                    .then(response => {
                        const params2 = new URLSearchParams();
                        params2.append('accessCode', state.accessCode);
                        params2.append('mbvid', payload.vid);
                        params2.append('mbbid', payload.bid);
                        if (payload.kid == 0)
                            params2.append('mbkid', response.data);
                        else
                            params2.append('mbkid', payload.kid);
                        params2.append('mbvalue', payload.value);
                        axios.post(myBaseURL + "/vpics/addupdatevalue", params2)
                            .then(response => {
                                resolve();
                            })
                            .catch(e => {
                                EventBus.$emit('AjaxError', e);
                                reject();
                            })
                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                        reject();
                    })
            })

        },

        async uploadFile(state, {file, source}) {
            const params = new URLSearchParams({ 'type': file.type, 'source': source });
            let response = await fetch(`${myBaseURL}/node/file/presignedPostURL?` + params.toString());
            if (response.ok) {
                const presignedPost = await response.json();
                const formData = new FormData();
                formData.append("Content-Type", file.type);
                Object.entries(presignedPost.uploadInfo.fields).forEach(([key, value]) => {
                    formData.append(key, value);
                });
                formData.append("file", file);
                response = await fetch(presignedPost.uploadInfo.url, {
                    method: "POST",
                    body: formData,
                });
                if (response.ok) {
                    return presignedPost.destinationURL;
                } else {
                    throw new Error("Upload failed");
                }
            } else if (response.status == 400) {
                const responseJSON = await response.json();
                throw new Error(responseJSON.error);
            } else {
                throw new Error("Unknown error");
            }
        },
        async saveFileInfo(state, info) {
            const response = await fetch(`${myBaseURL}/node/file/saveFileInfo`, {
                method: "POST",
                body: JSON.stringify(info),
                headers: {
                    "Content-Type": "application/json",
                },
            });
            if (response.ok) {
                return response.json();
            } else if (response.status == 400) {
                const responseJSON = await response.json();
                throw new Error(responseJSON.error);
            } else {
                throw new Error("Unknown error");
            }
        },
        async fetchActivityTaskTypes(context, refresh = false) {
            const state = context.state;
            if (state.activityTaskType.length > 0 && refresh === false) {
                return;
            }
            const response = await fetch(myBaseURL + '/node/activity/types');
            if (response.ok) {
                context.commit('setActivityTaskTypes', await response.json());
            }
        },
        async fetchContacts(context) {
            const state = context.state;
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            const response = await fetch(myBaseURL + '/vpics/bocontactsget?' + params.toString());

            if (response.ok) {
                context.commit('setContacts', await response.json());
            } else {
                EventBus.$emit('AjaxError', e);
            }
        },
    },
    mutations: {
        updateSiteStatus(state,  payload) {
            const {id, status} = payload;
            const building = state.buildings.find(b => b.mb_id == id);
            building.mb_status = status;
        },
        setChatLoaded(state){
            state.chatAlreadyLoaded = true;
        },
        setVehicles(state, payload){
            state.vehicles = payload;
        },
        setEquipments(state, payload){
            state.equipments = payload;
        },
        setVendors(state, payload){
            state.vendors = payload;
        },
        setMaintenanceTasks(state, payload){
            state.maintenanceTasks = payload;
        },
        setActivityTaskTypes(state, payload) {
            state.activityTaskType = payload;
        },
        setContacts(state, payload) {

            let fixedData =payload.map(contact => {
                if (!isNaN(contact.sf_contact_id)) {
                    contact.sf_contact_id = String(contact.sf_contact_id);
                }
                if (!isNaN(contact.sf_contact_clockinself)) {
                    contact.sf_contact_clockinself = String(contact.sf_contact_clockinself);
                }
                if (!isNaN(contact.sf_contact_clockinother)) {
                    contact.sf_contact_clockinother = String(contact.sf_contact_clockinother);
                }
                return contact;
            });

            state.contacts = fixedData.filter(a => a.sf_contact_active == '1')
            state.clientLeads = fixedData.filter(a => a.sf_contact_active == '3' && a.sf_contact_type == 'Client')
            state.allContacts = state.contacts.concat(state.clientLeads);
        },
        addUpdateActivityTaskTypes(state, payload) {
            const index = state.activityTaskType.findIndex(item => item.sat_id == payload.sat_id);
            if (index != -1) {
                state.activityTaskType.splice(index, 1);
            }
            state.activityTaskType.push(payload);
        },
        deleteActivityTaskTypes(state, payload) {
            const index = state.activityTaskType.findIndex(item => item.sat_id == payload);
            if (index > -1) {
                state.activityTaskType.splice(index, 1);
            }
        },
        setStep(state, payload) {
            state.step = payload;
        },
        newFile(state, payload) {
            state.selectedItem = -1
            state.selectedPage = 0

            state.formjson.pages = []
            state.filename = ""
            state.filestatus = 1
            state.fileid = null
            state.pageCounter = 0
            state.questionCounter = 0;
            state.pageCounter++
            var newPage = {
                name: "Page",
                title: "",
                elements: []
            };





            state.formjson.pages.push(newPage);

        },

        deletePage(state, payload) {
            if (state.pageCounter > 1) {
                state.formjson.pages.splice(payload.id, 1)
                state.pageCounter--;
                if (state.selectedPage > 0)
                    state.selectedPage--;
                state.selectedItem = -1;
            }

        },
        addPage(state, payload) {
            state.pageCounter++
            var newPage = {
                name: payload.name,
                title: "",
                elements: []
            };
            state.formjson.pages.push(newPage);
        },

        commitInitialData(state, payload) {
            var response = {};
            response.data = payload;
            //REMEMBER: These workarounds were put in place because of php/node migrations, they need to be removed once everything starts working without them
            try {
                state.vendorSettings = response.data.vendorSettings
            if (typeof response.data.buildings !== 'undefined')
                state.buildings = response.data.buildings.map(building => {
                    if (!isNaN(building.mb_id)) {
                        building.mb_id = String(building.mb_id);
                    }
                    if(!isNaN(building.mb_manager))
                    {
                        building.mb_manager = String(building.mb_manager);
                    }
                    if(!isNaN(building.mb_client))
                    {
                        building.mb_client = String(building.mb_client);
                    }
                    if(!isNaN(building.mb_min_zoom)) {
                        building.mb_min_zoom = String(building.mb_min_zoom);
                    }
                    return building;
                });
            if (typeof response.data.materials !== 'undefined')
                state.materials = response.data.materials.map(material => {
                    if (!isNaN(material.mat_id)) {
                        material.mat_id = String(material.mat_id);
                    }
                    return material;
                });
            if (typeof response.data.services !== 'undefined') {
                state.subContractedServices = [];
                state.ownFontEndServices = [];
                state.services = response.data.services.map(service => {
                    if (!isNaN(service.vs_service_id)) {
                        service.vs_service_id = String(service.vs_service_id);
                    }
                    if (!isNaN(service.vs_trade_id)) {
                        service.vs_trade_id = String(service.vs_trade_id);
                    }
                    if (!isNaN(service.vs_equipment_id)) {
                        service.vs_equipment_id = String(service.vs_equipment_id);
                    }
                    if (service.service_own == '1' && (service.vs_provider == null || service.vs_provider == '')) {
                        state.ownFontEndServices.push(service);
                    } else if (service.service_own != '1'){
                        state.subContractedServices.push(service);
                    }
                    return service;
                });
            }
            if (typeof response.data.contacts !== 'undefined') {
                let fixedData = response.data.contacts.map(contact => {
                    if (!isNaN(contact.sf_contact_id)) {
                        contact.sf_contact_id = String(contact.sf_contact_id);
                    }
                    if (!isNaN(contact.sf_contact_clockinself)) {
                        contact.sf_contact_clockinself = String(contact.sf_contact_clockinself);
                    }
                    if (!isNaN(contact.sf_contact_clockinother)) {
                        contact.sf_contact_clockinother = String(contact.sf_contact_clockinother);
                    }
                    return contact;
                });

                state.contacts = fixedData.filter(a => a.sf_contact_active == '1')
                state.clientLeads = fixedData.filter(a => a.sf_contact_active == '3' && a.sf_contact_type == 'Client')
                state.allContacts = state.contacts.concat(state.clientLeads);
                state.vendorPaymentLock = response.data.vendorPaymentLock;
            }
            if (typeof response.data.extendedbuilding !== 'undefined')
                state.extendedbuilding = response.data.extendedbuilding
            if (typeof response.data.extendedkeys !== 'undefined')
                state.extendedkeys = response.data.extendedkeys
            }
            catch(ex)
            {
                console.log(ex)
            }

            state.dataLoaded = true;


        },
        updateAccountFlags(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', myvid);
            params.append('gp', payload.ag);
            params.append('ts', payload.tm);
            params.append('bs', payload.bc);
            axios.post(state.setSettingsURL, params)
                .then(response => {
                    axios.post(state.getSettingsURL, params)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {
                                var temp = {
                                    ag: response.data[0].vendor_group_permissions,
                                    bc: response.data[0].vendor_breadcrumb_status,
                                    tm: response.data[0].vendor_tm_status
                                }
                                state.accountflags = temp;
                            } else {
                                EventBus.$emit('AjaxError', e);
                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        });

                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                });

        },
        setForm(state, payload) {
            state.selectedItem = -1

            state.selectedPage = 0

            state.formjson.pages = payload.json.pages
            state.filename = payload.filename
            state.fileid = payload.fileid
            state.filestatus = payload.filestatus
            state.pageCounter = state.formjson.pages.length + 1;


        },
        setFlags(state, payoad) {
            const params = new URLSearchParams();
            params.append('accessCode', myvid);
            axios.post(state.getSettingsURL, params)
                .then(response => {
                    if (typeof response.data !== 'undefined') {
                        var temp = {
                            ag: response.data[0].vendor_group_permissions,
                            bc: response.data[0].vendor_breadcrumb_status,
                            tm: response.data[0].vendor_tm_status
                        }
                        state.accountflags = temp;
                        // Vue.set(state.accountflags, 'ag', response.data[0].vendor_group_permissions)
                        // state.accountflags.ag = response.data[0].vendor_group_permissions;
                        // state.accountflags.bc = response.data[0].vendor_breadcrumb_status;
                        // state.accountflags.tm = response.data[0].vendor_tm_status;

                    } else {
                        EventBus.$emit('AjaxError', e);
                    }
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                });
        },
        setAccessCode(state, payload) {
            state.accessCode = myvid;
        },
        setVendorPaymentLock(state, payload) {
            state.vendorPaymentLock = payload;
        },
        setMini(state, payload) {
            state.mini = payload;
        },
        setMapImages(state, payload) {
            Vue.set(state, "mapimages", payload);
        },
        commitInitialPics(state, payload) {
            Array.prototype.push.apply(state.pics, payload);
            state.initialpicsloaded = true;
            state.photocount = state.pics.length;

        },
        commitExtendedData(state, payload) {
            state.extendedbuilding = payload.extendedbuilding;
            state.extendedkeys = payload.extendedkeys;
        },
        commitSiteByID(state, payload) {
            let existing = state.buildings.find(a => a.mb_id == payload.mb_id);
            if (typeof existing !== 'undefined') {
                let index = state.buildings.indexOf(existing);
                Vue.set(state.buildings, index, payload);
            } else {
                state.buildings.push(payload);
            }

        },

        increment(state) {
            state.count++
        },
        getRemainingData(state, payload) {
            // Fetch only the forms data
            fetch(`${state.getFormsURL}?accessCode=${state.accessCode}`)
                .then(response => response.json())
                .then(data => {
                    // Assign the fetched data to forms
                    state.forms = data;
                    // Assign an empty array to maps
                    state.maps = [];
                })
                .catch(error => console.error(error));


        },
        getInitialPics(state, payload) {
            state.accessCode = myvid;
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('offset', 0);
            params.append('limit', 240);
            axios.post(state.getPicturesURL, params)
                .then(response => {
                    Array.prototype.push.apply(state.pics, response.data);

                    state.initialpicsloaded = true;
                    state.photocount = state.pics.length;
                    EventBus.$emit('InitialPicsLoaded', "nothing");

                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        getSharedPics(state, payload) {

            if (mysharedincount > 0) {
                var p = Math.ceil(mysharedincount / 2000);
                var t = 2000;
                var done = [];
                for (var i = 0; i < p; i++) {
                    const params = new URLSearchParams();
                    params.append('accessCode', state.accessCode);
                    params.append('offset', (i * t));
                    params.append('iter', i);
                    params.append('limit', t);

                    axios.post(state.getSharedPicturesURL, params)
                        .then(response => {
                            if (typeof response.data.isError == 'undefined') {
                                Array.prototype.push.apply(state.pics, response.data);

                            }

                            //state.pics = _.reverse(_.sortBy(state.pics, ['created']));
                            done.push(1);
                            if (done.length == p) {

                                setTimeout(function () {
                                    state.pics.sort((a, b) => {
                                        if (parseInt(a.created) < parseInt(b.created)) {
                                            return 1
                                        }

                                        if (parseInt(a.created) > parseInt(b.created)) {
                                            return -1
                                        }

                                        return 0
                                    })

                                    state.photocount = state.pics.length;

                                }, 100);

                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                }
            }
        },
        getAllPics(state, payload) {
            var p = Math.ceil(mypiccount / 2000);
            var t = 2000;
            var done = [];
            for (var i = 0; i < p; i++) {
                const params = new URLSearchParams();
                params.append('accessCode', state.accessCode);
                params.append('offset', (i * t) + 240);
                params.append('iter', i);
                params.append('limit', t);

                axios.post(state.getPicturesURL, params)
                    .then(response => {
                        Array.prototype.push.apply(state.pics, response.data);
                        //state.pics = _.reverse(_.sortBy(state.pics, ['created']));
                        done.push(1);
                        if (done.length == p) {

                            setTimeout(function () {
                                // state.pics = _.reverse(_.sortBy(state.pics, ['created']));
                                // (dateFns.parse(a.mvp_added_date).getTime() / 1000).toFixed(0)
                                //state.pics.sort((a, b) => parseInt(a.created) > parseInt(b.created))
                                state.pics.sort((a, b) => {
                                    if (parseInt(a.created) < parseInt(b.created)) {
                                        return 1
                                    }

                                    if (parseInt(a.created) > parseInt(b.created)) {
                                        return -1
                                    }

                                    return 0
                                })
                                // state.pics = _.orderBy(k, ['created'], ['desc']);
                                state.allpicsloaded = true;
                                state.photocount = state.pics.length;
                                // EventBus.$emit('AllPicsLoaded', "");
                            }, 100);

                        }
                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                    })
            }
        },
        photoUpdate(state, payload) {
            var k = _.find(state.pics, {
                'mvp_id': payload.pid
            });
            Vue.set(k, 'mvp_disc', payload.desc);
            Vue.set(k, 'mvp_building_id', payload.bid);
        },
        photoDelete(state, payload) {
            state.pics = _.filter(state.pics, function (n) {
                return n.mvp_id != payload.delid;
            });
            state.photocount--;
        },
        photoMove(state, payload) {
            for (var i = 0; i < payload.picIDs.length; i++) {
                var k = state.pics.findIndex(x => x.mvp_id == payload.picIDs[i]);
                Vue.set(state.pics[k], "mvp_building_id", payload.bid);
            }
        },
        selectMaterial(state, payload) {

            if (typeof payload.selected === 'undefined')
                Vue.set(payload, "selected", true);
            else if (payload.selected === true)
                Vue.set(payload, "selected", false);
            else
                Vue.set(payload, "selected", true);


        },
        addUpdateKey(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('mblabel', payload.label);
            params.append('mbid', payload.bid);
            params.append('mbkey', payload.key);
            params.append('mbfilter', "0");
            params.append('mbtype', "text");
            axios.post(myBaseURL + "/vpics/addupdatekey", params)
                .then(response => {

                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },

        updatePage(state, payload) {
            state.currentPage = payload;
        },
        addMaterial(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('materialid', 0);
            params.append('materialname', payload.mname);
            params.append('materialunit', payload.munit);
            params.append('materialprovider', payload.mextsrc ?? '');
            params.append('materialproviderid', payload.mextid ?? '');
            params.append('materialstatus', 1);
            axios.post(state.addUpdateMaterialURL, params)
                .then(response => {
                    const params = new URLSearchParams();
                    const responsedata = response.data;
                    params.append('accessCode', state.accessCode);

                    axios.post(state.boMaterialsGetURL, params)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {
                                var mat = [];
                                for (var i = 0; i < response.data.length; i++) {
                                    if (response.data[i].mat_material_status == '1') {
                                        var index = state.materials.findIndex(x => x.mat_id == response.data[i].mat_id);
                                        if (index != -1) {
                                            if (state.materials[index].selected == true)
                                                Vue.set(response.data[i], "selected", true);
                                        }
                                        mat.push(response.data[i]);
                                    }
                                }
                                state.materials = mat;
                                EventBus.$emit('MaterialAdded', responsedata);
                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        addContact(state, payload) {
            const url = state.addUpdateContact;
            const data = {
                accessCode: state.accessCode,
                sfcontactid: 0,
                sfcontactprofilepic: payload.cprofilepic,
                sfcontactfname: payload.cfname,
                sfcontactlname: payload.clname,
                sfcontactaddress1: payload.cadd1,
                sfcontactaddress2: payload.cadd2,
                sfcontactcity: payload.ccity,
                sfcontactzip: payload.czip,
                sfcontactemail: payload.cemail,
                sfcontactphone: payload.cphone,
                sfcontactmobile: payload.cmobile,
                sfcontactstate: payload.cstate,
                sfcontacttype: payload.ctype,
                sfcontactcountry: payload.ccountry,
                sfcontactactive: payload.cactive,
                sfcontactbreadcrumbs: payload.bc,
                sfcontactnotes: payload.cnotes,
                sfcontractorgroups: payload.cgroups || [],
                sfcontacttrades: payload.ctrades,
                sfcontacttitle: payload.ctitle,
                sfcontactcompanyid: payload.ccompanyid,
                sfcontactcompanybranch: payload.ccompanybranch,
                sfcontactcid: payload.ccid,
                sfcontactcontractorid: payload.ccontractorid,
                sfcontactclockinself: payload.sfcontactclockinself,
                sfcontactclockinother: payload.sfcontactclockinother,
                sfcontactquickbooksid: payload.quickbooksid,
                sfcontactaspireemail: payload.aspireemail,
                sfcontactaspirepin: payload.aspirepin,
                sfcontactdefaultsiteslist:payload.sfcontactdefaultsiteslist,
                sfcontactsupervisor: payload.csupervisor,
            };

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(async responsedata => {
                await this.dispatch('fetchContacts');
                EventBus.$emit('ContactAdded', responsedata);
            })
            .catch(e => {
                EventBus.$.emit('ContactAddError', e);
            });
        },



        updateContact(state, payload) {
            const url = state.addUpdateContact;
            const data = {
                accessCode: state.accessCode,
                sfcontactid: payload.cid,
                sfcontactprofilepic: payload.cprofilepic,
                sfcontactfname: payload.cfname,
                sfcontactlname: payload.clname,
                sfcontactaddress1: payload.cadd1,
                sfcontactaddress2: payload.cadd2,
                sfcontactcity: payload.ccity,
                sfcontactzip: payload.czip,
                sfcontactemail: payload.cemail,
                sfcontactphone: payload.cphone,
                sfcontactmobile: payload.cmobile,
                sfcontactstate: payload.cstate,
                sfcontacttype: payload.ctype,
                sfcontactcountry: payload.ccountry,
                sfcontactactive: payload.cactive,
                sfcontactnotes: payload.cnotes,
                sfcontractorgroups: payload.cgroups || [],
                sfcontacttitle: payload.ctitle,
                sfcontacttrades: payload.ctrades,
                sfcontactbranchid: payload.cbranchid,
                sfcontactcompanyid: payload.ccompanyid,
                sfcontactcid: payload.ccid,
                sfcontactbreadcrumbs: payload.bc,
                sfcontactcontractorid: payload.ccontractorid,
                sfcontactclockinself: payload.sfcontactclockinself,
                sfcontactclockinother: payload.sfcontactclockinother,
                sfcontactquickbooksid: payload.quickbooksid,
                sfcontactaspireemail: payload.aspireemail,
                sfcontactaspirepin: payload.aspirepin,
                sfcontactdefaultsiteslist:payload.sfcontactdefaultsiteslist,
                sfcontactsupervisor: payload.csupervisor,
            };

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(async responsedata => {
                await this.dispatch('fetchContacts');
                EventBus.$emit('ContactUpdated', responsedata);
            })
            .catch(e => {
                EventBus.$emit('AjaxError', e);
            });
        },

        deleteContact(state, payload) {
            const url = state.addUpdateContact;
            const data = {
                accessCode: state.accessCode,
                sfcontactid: payload.cid,
                sfcontactactive: 2
            };

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(async responsedata => {
                await this.dispatch('fetchContacts');
                EventBus.$emit('ContactUpdated', responsedata);
            })
            .catch(e => {
                EventBus.$emit('AjaxError', e);
            });
        },

        updateMaterial(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('materialid', payload.mid);
            params.append('materialname', payload.mname);
            params.append('materialunit', payload.munit);
            params.append('materialstatus', 1);
            params.append('materialprovider', payload.mextsrc ?? '');
            params.append('materialproviderid', payload.mextid ?? '');

            axios.post(state.addUpdateMaterialURL, params)
                .then(response => {
                    const params = new URLSearchParams();
                    params.append('accessCode', state.accessCode);

                    axios.post(state.boMaterialsGetURL, params)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {

                                var mat = [];
                                for (var i = 0; i < response.data.length; i++) {
                                    if (response.data[i].mat_material_status == '1') {
                                        var index = state.materials.findIndex(x => x.mat_id == response.data[i].mat_id);
                                        if (index != -1) {
                                            if (state.materials[index].selected == true)
                                                Vue.set(response.data[i], "selected", true);
                                        }
                                        mat.push(response.data[i]);
                                    }
                                }
                                state.materials = mat;

                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        setCurrentPage(state, payload) {
            state.selectedPage = payload

        },
        deleteMaterial(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('materialid', payload.mid);
            params.append('materialname', payload.mname);
            params.append('materialunit', payload.munit);
            params.append('materialprovider', payload.mextsrc ?? '');
            params.append('materialproviderid', payload.mextid ?? '');
            params.append('materialstatus', 0);
            axios.post(state.addUpdateMaterialURL, params)
                .then(response => {
                    const params = new URLSearchParams();
                    params.append('accessCode', state.accessCode);

                    axios.post(state.boMaterialsGetURL, params)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {

                                var mat = [];
                                for (var i = 0; i < response.data.length; i++) {
                                    if (response.data[i].mat_material_status == '1')
                                        mat.push(response.data[i]);
                                }
                                state.materials = mat;

                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        addService(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('serviceid', 0);
            params.append('servicename', payload.sname);
            params.append('serviceoptions', payload.sopt);
            params.append('servicecategory', payload.scat);
            params.append('servicestatus', 1);
            params.append('servicequickbooks',payload.squickbooks)
            params.append('serviceequipment',payload.sequip)
            params.append('serviceproviderid',payload.sextid ?? '')
            params.append('serviceprovidername',payload.sextname ?? '')
            params.append('servicetypeid', payload.servicetypeid)
            params.append('servicetradeid', payload.servicetradeid)
            params.append('smaterial', payload.smaterial);
            axios.post(state.addUpdateServiceURL, params)
                .then(response => {
                    const responsedata = response.data;
                    const url = state.boServicesGetURL + '?accessCode='+state.accessCode;
                    console.log(url);

                    axios.get(url)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {
                                state.subContractedServices = [];
                                state.ownFontEndServices = [];
                                state.services = response.data.map(service => {
                                    if (!isNaN(service.vs_service_id)) {
                                        service.vs_service_id = String(service.vs_service_id);
                                    }
                                    if (!isNaN(service.vs_trade_id)) {
                                        service.vs_trade_id = String(service.vs_trade_id);
                                    }
                                    if (!isNaN(service.vs_equipment_id)) {
                                        service.vs_equipment_id = String(service.vs_equipment_id);
                                    }
                                    if (service.service_own == '1' && (service.vs_provider == null || service.vs_provider == '')) {
                                        state.ownFontEndServices.push(service);
                                    } else if (service.service_own != '1'){
                                        state.subContractedServices.push(service);
                                    }
                                    return service;
                                });
                                EventBus.$emit('ServiceAdded', responsedata);
                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },



        updateList(state, payload) {

            var previousElement;
            var updateAllowed = true;
            var itemNumber = 0;
            var newquestion = false;
            payload.items.forEach(function (element) {

                for (var key in element) {
                    if (key == "name" && (element[key] == "" || element[key] == "0")) {
                        element[key] = "Q" + state.questionCounter++;

                        newquestion = true;
                    }
                }

                previousElement = element;
                itemNumber++;

            });

            if (updateAllowed == true) {
                Vue.set(state.formjson.pages[state.selectedPage], "elements", payload.items)
                if (newquestion)
                    EventBus.$emit('addedItem', this);

            }

            // state.form.pages[payload.currentPage].elements= payload.items
        },
        setQuestionCounter(state, payload) {
            state.questionCounter = payload;
        },





        updateService(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('serviceid', payload.sid);
            params.append('servicename', payload.sname);
            params.append('serviceoptions', payload.sopt);
            params.append('servicecategory', payload.scat);
            params.append('servicestatus', 1);
            params.append('servicequickbooks',payload.squickbooks)
            params.append('serviceequipment',payload.sequip)
            params.append('serviceproviderid',payload.sextid ?? '')
            params.append('serviceprovidername',payload.sextname ?? '')
            params.append('servicetypeid', payload.servicetypeid)
            params.append('servicetradeid', payload.servicetradeid);
            params.append('smaterial', payload.smaterial);
            axios.post(state.addUpdateServiceURL, params)
                .then(response => {
                    const url = state.boServicesGetURL + '?accessCode='+state.accessCode;
                    axios.get(url)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {
                                state.subContractedServices = [];
                                state.ownFontEndServices = [];
                                state.services = response.data.map(service => {
                                    if (!isNaN(service.vs_service_id)) {
                                        service.vs_service_id = String(service.vs_service_id);
                                    }
                                    if (!isNaN(service.vs_trade_id)) {
                                        service.vs_trade_id = String(service.vs_trade_id);
                                    }
                                    if (!isNaN(service.vs_equipment_id)) {
                                        service.vs_equipment_id = String(service.vs_equipment_id);
                                    }
                                    if (service.service_own == '1' && (service.vs_provider == null || service.vs_provider == '')) {
                                        state.ownFontEndServices.push(service);
                                    } else if (service.service_own != '1'){
                                        state.subContractedServices.push(service);
                                    }
                                    return service;
                                });
                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        saveBuilding(state, payload) {


        },
        deactivateSite(state, payload) {
             //set mb_status to 2 for building
             const site = state.buildings.find(a => a.mb_id == payload);
        if(site) {
            Vue.set(site, "mb_status", "2");
        }

        },
        reactivateSite(state, payload) {
            //set mb_status to 2 for building
            const site = state.buildings.find(a => a.mb_id == payload);
        if(site) {
            Vue.set(site, "mb_status", "1");
        }

       },
        getBuildingDetailed(state, payload) {
            const params = new URLSearchParams();
            params.append('bid', payload.bid)
            fetch(`/node/sites/get-building-info/${payload.bid}`)
            .then(response => response.json())
            .then(data => {
              if (data.buildingInfo) {
                const buildingInfo = data.buildingInfo;
                const emails = data.emails;

                const bdata = {
                  propertyid: buildingInfo.mb_id,
                  propertyname: buildingInfo.mb_nickname,
                  propertystreet: buildingInfo.mb_address1,
                  pacinput: `${buildingInfo.mb_address1}, ${buildingInfo.cityname}, ${buildingInfo.statename}`,
                  propertycity: buildingInfo.cityname,
                  propertystate: buildingInfo.statename,
                  propertyzip: buildingInfo.mb_zip_code,
                  propertygeo: buildingInfo.mb_geo,
                  propertylat: buildingInfo.mb_lat,
                  propertylng: buildingInfo.mb_long,
                  propertynotes: buildingInfo.mb_notes ?? '',
                  propertymaps: buildingInfo.mb_maps,
                  propertyhours: buildingInfo.mb_ohours ?? '',
                  propertyexsrc: buildingInfo.mb_external_src,
                  propertyexid: buildingInfo.mb_external_id,
                  propertyman: buildingInfo.mb_manager ? buildingInfo.mb_manager.toString() : buildingInfo.mb_manager,
                  propertycon: buildingInfo.mb_contractor,
                  propertyzone: buildingInfo.mb_zone,
                  propertyzoneid: buildingInfo.mb_zone_id,
                  propertyclient: buildingInfo.mb_client ? buildingInfo.mb_client.toString() : buildingInfo.mb_client,
                  propertyclientsecondary: buildingInfo.mb_client_secondary ? buildingInfo.mb_client_secondary.toString() : buildingInfo.mb_client_secondary,
                  propertyclientthird: buildingInfo.mb_client_third ? buildingInfo.mb_client_third.toString() : buildingInfo.mb_client_third,
                  buildingsource: buildingInfo.mb_source,
                  mbbuildingstatus: buildingInfo.mb_building_status,
                  propertycountryid: buildingInfo.mb_country,
                  propertykind: buildingInfo.mb_kind ?? '',
                  propertymansecond: buildingInfo.mb_manager_secondary ? buildingInfo.mb_manager_secondary.toString() : buildingInfo.mb_manager_secondary,
                  propertymanthird: buildingInfo.mb_manager_third ? buildingInfo.mb_manager_third.toString() : buildingInfo.mb_manager_third,
                  sharing: buildingInfo.mb_min_zoom == 1,
                  propertycontracts: buildingInfo.mb_contract_type,
                  propertyqbid: buildingInfo.mb_quickbooks_customer_id,
                  propertyscpin: buildingInfo.mb_sc_pin,
                  propertyscstoreid: buildingInfo.mb_sc_store_id,
                  propertyemails: emails ?? '',
                  coords: buildingInfo.mb_geo.substr(1, buildingInfo.mb_geo.length - 2).split('),('),
                  sqft: buildingInfo.mb_sf,
                  bradius: buildingInfo.mb_radius,
                    btimezone: buildingInfo.mb_timezone
                };

                state.buildingdetailed = bdata;
                EventBus.$emit('BuildingDetailLoaded', state.buildingdetailed);
              } else {
                console.log(data.message);
              }
            })
            .catch(error => {
                console.log(error)
              EventBus.$emit('AjaxError', error);
            });

        },
        setCrew(state, payload) {
            state.tempprofile.crew = payload;
        },
        setRouteServe(state, payload) {
            state.tempactive.routeserve = payload;
        },
        setRouteContact(state, payload) {
            state.tempactive.routecontact = payload;
        },
        setProfileType(state, payload) {
            state.tempprofile.profiletype = payload;
        },
        setProfileName(state, payload) {
            state.tempprofile.name = payload;
        },
        setProfileCategory(state, payload) {
            state.tempprofile.cat = payload;
        },
        setProfileForms(state, payload) {
            state.tempprofile.forms = payload;
        },
        setProfileMaps(state, payload) {
            state.tempactive.maps = payload;
        },
        setProfileBuilding(state, payload) {
            state.tempprofile.bid = payload;
        },
        setNotes(state, payload) {
            state.tempprofile.notes = payload;
        },
        setSelectedCategory(state, payload) {
            state.tempprofile.selectedcategory = payload;
        },
        setPPhotos(state, payload) {
            state.tempprofile.photos = payload;
        },
        setCheckInOut(state, payload) {
            state.tempprofile.checkinout = payload;
        },
        cleanupActive(state, payload) {
            state.tempactive = Object.assign({}, state.defactive);
        },
        setFileStatus(state, payload) {
            state.filestatus = payload;
        },
        setFormStatus(state, payload) {
            var k = 1;
            if (payload.item == true) {
                k = 2
            } else if (payload.item == false) {
                k = 1
            }
            state.filestatus = k;
        },


        deleteService(state, payload) {
            const params = new URLSearchParams();
            params.append('accessCode', state.accessCode);
            params.append('serviceid', payload.sid);
            params.append('servicename', payload.sname);
            params.append('serviceproviderid',payload.sextid ?? '')
            params.append('serviceprovidername',payload.sextname ?? '')
            params.append('servicestatus', 0);
            axios.post(state.addUpdateServiceURL, params)
                .then(response => {
                    const url = state.boServicesGetURL + '?accessCode='+state.accessCode;
                    axios.get(url)
                        .then(response => {
                            if (typeof response.data !== 'undefined') {
                                state.subContractedServices = [];
                                state.ownFontEndServices = [];
                                state.services = response.data.map(service => {
                                    if (!isNaN(service.vs_service_id)) {
                                        service.vs_service_id = String(service.vs_service_id);
                                    }
                                    if (!isNaN(service.vs_trade_id)) {
                                        service.vs_trade_id = String(service.vs_trade_id);
                                    }
                                    if (!isNaN(service.vs_equipment_id)) {
                                        service.vs_equipment_id = String(service.vs_equipment_id);
                                    }
                                    if (service.service_own == '1' && (service.vs_provider == null || service.vs_provider == '')) {
                                        state.ownFontEndServices.push(service);
                                    } else if (service.service_own != '1'){
                                        state.subContractedServices.push(service);
                                    }
                                    return service;
                                });
                            }
                        })
                        .catch(e => {
                            EventBus.$emit('AjaxError', e);
                        })
                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        selectItem(state, payload) {
            state.selectedItem = payload

        },
        setVendorSettings(state, payload) {
            state.vendorSettings = payload;
        }
    },
    getters: {
        getDeviceModel() {
            return navigator.userAgent;
        },
        getDeviceType() {
            //Find operating system of the device using navigator.userAgent with os vertion
            const userAgent = window.navigator.userAgent;
            let os = "WEB-";

            if (userAgent.indexOf("Win") !== -1) os += "Windows";
            else if (userAgent.indexOf("Mac") !== -1) os += "MacOS";
            else if (userAgent.indexOf("X11") !== -1) os += "UNIX";
            else if (userAgent.indexOf("Linux") !== -1) os += "Linux";
            else if (/Android/.test(userAgent)) os += "Android";
            else if (/iPhone|iPad|iPod/.test(userAgent)) os += "iOS";
            else os += "Unknown";

            return os;
        },
        getIfChatIsAlreadyLoaded(state){
            return state.chatAlreadyLoaded
        },
        getActivityTaskTypes(state) {
            return state.activityTaskType;
        },
        getContractorsForReports(state) {
            var customers = state.contacts.filter(x => x.sf_contact_contractorid !== '' && x.sf_contact_type === 'Contractor')
            // var customers = _.filter(state.contacts, {
            //     'sf_contact_type': 'Contractor'
            // });
            // console.log("aaa", customers[0].sf_contact_contractorid)
            var grouped = _.chain(customers)
              .groupBy("sf_contact_company_name")
              .toPairs()
              .map(function (currentItem) {
                  return _.zipObject(["sf_contact_company_name", "users"], currentItem);
              })
              .value();
            var items = [];

            for (var i = 0; i < grouped.length; i++) {

                for (var j = 0; j < grouped[i].users.length; j++) {
                    var item = {}
                    item.value = grouped[i].users[j].sf_contact_id
                    item.text = grouped[i].users[j].sf_contact_fname + " " + grouped[i].users[j].sf_contact_lname
                    item.disabled = false;
                    item.avatar = "";
                    item.group = grouped[i].sf_contact_company_name;
                    item.contractor = grouped[i].users[j].sf_contact_contractorid;
                    items.push(item);
                }
            }


            return _.sortBy(items, [function(o) { return o.text; }]);

        },
        getCount(state) {
            return state.count;
        },
        getPage(state) {
            return state.currentPage;
        },
        getMaterials(state) {
            return state.materials;
        },
        getServices(state) {
            return state.services;
        },
        getSubContractedServices(state) {
            return state.subContractedServices;
        },
        getOwnFontEndServices(state) {
            return state.ownFontEndServices;
        },
        getAllContacts: (state) => {
            return state.allContacts;
        },
        getContacts(state) {
            return state.contacts;
        },
        getClientLeads(state) {
            return state.clientLeads;
        },
        getBuildingData(state) {
            var t = _.filter(state.buildings, {
                'mb_status': "1"
              });
              return t.map((item) => {return {value: item.mb_id, text: item.mb_nickname}});
        },
        getBuildingLeadData(state) {
            var t = _.filter(state.buildings, {
                'mb_status': "3"
            });
            return t.map((item) => {return {value: item.mb_id, text: item.mb_nickname}});
        },
        getEmployeesData(state) {
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            var items = [];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.value = employees[j].sf_contact_id
                item.email = employees[j].sf_contact_email
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                items.push(item);
            }
            return _.sortBy(items, [function(o) { return o.text; }]);

        },
        getEmployees(state) {
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            var items = [];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.value = employees[j].sf_contact_id
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                item.fname = employees[j].sf_contact_fname;
                item.lname = employees[j].sf_contact_lname;
                item.email = employees[j].sf_contact_email;
                item.cell = employees[j].sf_contact_mobile;
                items.push(item);
            }
            return _.sortBy(items, [function(o) { return o.text; }]);

        },
        getContractorsInternal(state) {
            const users = state.contacts.filter(x => !x.sf_contact_contractorid && x.sf_contact_type === 'Contractor');
            var items = [];
            for (var j = 0; j < users.length; j++) {
                var item = {}
                item.value = users[j].sf_contact_id
                item.text = users[j].sf_contact_fname + " " + users[j].sf_contact_lname
                items.push(item);
            }
            return _.sortBy(items, [function(o) { return o.text; }]);
        },
        getContractors3(state) {
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Contractor'
            });
            var items = [];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.email = employees[j].sf_contact_email;
                item.value = employees[j].sf_contact_id
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                items.push(item);
            }
            return items;
        },



        getEmpCon2(state) {
            var items = [];




            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            if (employees.length > 0) {
                var item = {
                    header: 'Employees'
                };
                items.push(item);
                for (var j = 0; j < employees.length; j++) {
                    var item = {}
                    item.value = employees[j].sf_contact_email
                    item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                    item.grp = "-2"
                    items.push(item);
                }

            }

            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Contractor'
            });
            if (employees.length > 0) {
                var item = {
                    divider: true
                };
                items.push(item);
                var item = {
                    header: 'Contractors'
                };
                items.push(item);
                for (var j = 0; j < employees.length; j++) {
                    var item = {}
                    item.value = employees[j].sf_contact_email
                    item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                    item.grp = "-3"
                    items.push(item);

                }
            }
            return items;



        },
        getEmp(state)
        {
            var items = [];
            var item = {
                text: "Everyone",
                value: "-1",
                grp: "-1"
            }
            items.push(item);
            var item = {
                text: "All Employees",
                value: "-2",
                grp: "-2"
            }
            items.push(item);

            var item = {
                divider: true
            };
            items.push(item);
            var item = {
                header: 'Employees'
            };
            items.push(item);
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            let ee=[];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.value = employees[j].sf_contact_id
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                item.grp = "-2"
                ee.push(item);
            }
            ee.sort(function(a, b){
                if(a.text < b.text) { return -1; }
                if(a.text > b.text) { return 1; }
                return 0;
            })
            items = items.concat(ee);

            return items;



        },
        getEmpCon(state) {
            var items = [];
            var item = {
                text: "Everyone",
                value: "-1",
                grp: "-1"
            }
            items.push(item);
            var item = {
                text: "All Employees",
                value: "-2",
                grp: "-2"
            }
            items.push(item);
            var item = {
                text: "All Contractors",
                value: "-3",
                grp: "-3"

            }
            items.push(item);
            var item = {
                divider: true
            };
            items.push(item);
            var item = {
                header: 'Employees'
            };
            items.push(item);
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            let ee=[];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.value = employees[j].sf_contact_id
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                item.grp = "-2"
                ee.push(item);
            }
            ee.sort(function(a, b){
                if(a.text < b.text) { return -1; }
                if(a.text > b.text) { return 1; }
                return 0;
            })
            items = items.concat(ee);
            var item = {
                divider: true
            };
            items.push(item);
            var item = {
                header: 'Contractors'
            };
            items.push(item);
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Contractor'
            });
            let fe=[];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.value = employees[j].sf_contact_id
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                item.grp = "-3"
                fe.push(item);

            }
            fe.sort(function(a, b){
                if(a.text < b.text) { return -1; }
                if(a.text > b.text) { return 1; }
                return 0;
            })
            items = items.concat(fe);
            return items;
        },


        getEmpConWithCompany(state) {
            var items = [];
            var item = {
                text: "Everyone",
                value: "-1",
                grp: "-1"
            }
            items.push(item);
            var item = {
                text: "All Employees",
                value: "-2",
                grp: "-2"
            }
            items.push(item);
            var item = {
                text: "All Contractors",
                value: "-3",
                grp: "-3"

            }
            items.push(item);
            var item = {
                divider: true
            };
            items.push(item);
            var item = {
                header: 'Employees'
            };
            items.push(item);
            var employees = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            let ee=[];
            for (var j = 0; j < employees.length; j++) {
                var item = {}
                item.value = employees[j].sf_contact_id
                item.text = employees[j].sf_contact_fname + " " + employees[j].sf_contact_lname
                item.grp = "-2"
                ee.push(item);
            }
            ee.sort(function(a, b){
                if(a.text < b.text) { return -1; }
                if(a.text > b.text) { return 1; }
                return 0;
            })
            items = items.concat(ee);
            var item = {
                divider: true
            };
            items.push(item);
            var item = {
                header: 'Contractors'
            };
            items.push(item);
            var contractors = _.filter(state.contacts, { // Changed variable name for clarity
                'sf_contact_type': 'Contractor'
            });
            let fe=[];
            for (var j = 0; j < contractors.length; j++) { // Use contractors variable
                var item = {}
                item.value = contractors[j].sf_contact_id // Use contractors variable
                // Format text: "CompanyName - FirstName LastName" or "FirstName LastName" if no company
                var companyName = contractors[j].sf_contact_company_name; // Use contractors variable
                var firstName = contractors[j].sf_contact_fname; // Use contractors variable
                var lastName = contractors[j].sf_contact_lname; // Use contractors variable
                item.text = (companyName && companyName !== 'null' && companyName.trim() !== '' ? companyName.trim() + ' - ' : '') + firstName + " " + lastName;
                item.grp = "-3"
                fe.push(item);

            }
            fe.sort(function(a, b){
                // Sort by company name first, then by person name
                const textA = a.text.toUpperCase();
                const textB = b.text.toUpperCase();
                if (textA < textB) { return -1; }
                if (textA > textB) { return 1; }
                return 0;
            })
            items = items.concat(fe);
            return items;
        },


        getContractors(state) {
            var customers = _.filter(state.contacts, {
                'sf_contact_type': 'Contractor'
            });
            var grouped = _.chain(customers)
                .groupBy("sf_contact_company_name")
                .toPairs()
                .map(function (currentItem) {
                    return _.zipObject(["sf_contact_company_name", "users"], currentItem);
                })
                .value();
            var items = [];

            for (var i = 0; i < grouped.length; i++) {

                for (var j = 0; j < grouped[i].users.length; j++) {
                    var item = {}
                        item.value = grouped[i].users[j].sf_contact_id
                        item.text = grouped[i].users[j].sf_contact_fname + " " + grouped[i].users[j].sf_contact_lname
                        item.disabled = false;
                        item.avatar = "";
                        item.group = grouped[i].sf_contact_company_name;
                        item.contractor = grouped[i].users[j].sf_contact_contractorid;
                        item.fname = grouped[i].users[j].sf_contact_fname;
                        item.lname = grouped[i].users[j].sf_contact_lname;
                        item.email = grouped[i].users[j].sf_contact_email;
                        item.cell = grouped[i].users[j].sf_contact_mobile;
                        item.company_name = grouped[i].sf_contact_company_name == null || grouped[i].sf_contact_company_name == 'null' ? '' : grouped[i].sf_contact_company_name;
                        items.push(item);
                }
            }


            return _.sortBy(items, [function(o) { return o.text; }]);

        },
        getContractorsCompanies(state) {
            var customers = _.filter(state.contacts, {
                'sf_contact_type': 'Contractor'
            });
            var grouped = _.chain(customers)
                .groupBy("sf_contact_company_name")
                .toPairs()
                .map(function (currentItem) {
                    return _.zipObject(["sf_contact_company_name", "users"], currentItem);
                })
                .value();
            var items = [];

            for (var i = 0; i < grouped.length; i++) {

                for (var j = 0; j < grouped[i].users.length; j++) {
                    var item = {}
                        item.value = grouped[i].users[j].sf_contact_id
                        item.text = grouped[i].users[j].sf_contact_company_name + " - " + grouped[i].users[j].sf_contact_fname + " " + grouped[i].users[j].sf_contact_lname
                        item.disabled = false;
                        item.avatar = "";
                        item.group = grouped[i].sf_contact_company_name;
                        item.contractor = grouped[i].users[j].sf_contact_contractorid;
                        item.fname = grouped[i].users[j].sf_contact_fname;
                        item.lname = grouped[i].users[j].sf_contact_lname;
                        item.email = grouped[i].users[j].sf_contact_email;
                        item.cell = grouped[i].users[j].sf_contact_mobile;
                        items.push(item);
                }
            }


            return _.sortBy(items, [function(o) { return o.text; }]);

        },
        getContractors2(state) {
            var customers = _.filter(state.contacts, {
                'sf_contact_type': 'Contractor'
            });
            var grouped = _.chain(customers)
                .groupBy("sf_contact_company_name")
                .toPairs()
                .map(function (currentItem) {
                    return _.zipObject(["sf_contact_company_name", "users"], currentItem);
                })
                .value();
            var items = [];

            for (var i = 0; i < grouped.length; i++) {
                var grp = {}
                grp.value = -(i + 1);
                grp.text = grouped[i].sf_contact_company_name;
                grp.disabled = true;
                grp.avatar = "group"
                items.push(grp);
                for (var j = 0; j < grouped[i].users.length; j++) {
                    var item = {}
                    item.value = grouped[i].users[j].sf_contact_id
                    item.text = grouped[i].users[j].sf_contact_fname + " " + grouped[i].users[j].sf_contact_lname
                    item.disabled = false;
                    item.avatar = "";
                    items.push(item);
                }
            }


            return items;

        },
        getCustomers(state) {
            var customers = _.filter(state.contacts, {
                'sf_contact_type': 'Client'
            });
            var grouped = _.chain(customers)
                .groupBy("sf_contact_company_name")
                .toPairs()
                .map(function (currentItem) {
                    return _.zipObject(["sf_contact_company_name", "users"], currentItem);
                })
                .value();
            var items = []
            for (var i = 0; i < grouped.length; i++) {

                for (var j = 0; j < grouped[i].users.length; j++) {
                    var item = {}
                    item.value = grouped[i].users[j].sf_contact_id;
                    item.text = grouped[i].users[j].sf_contact_fname + " " + grouped[i].users[j].sf_contact_lname;
                    item.company_name = grouped[i].users[j].sf_contact_company_name;
                    item.company_address = grouped[i].users[j].sf_contact_company_address1;
                    item.company_state_zip = `${grouped[i].users[j].sf_contact_company_address1} ${grouped[i].users[j].sf_contact_company_city}, ${grouped[i].users[j].sf_contact_company_state} ${grouped[i].users[j].sf_contact_company_zip} `
                    item.fname = grouped[i].users[j].sf_contact_fname;
                    item.lname = grouped[i].users[j].sf_contact_lname;
                    item.email = grouped[i].users[j].sf_contact_email;
                    item.cell = grouped[i].users[j].sf_contact_mobile
                    item.disabled = false;
                    item.avatar = "";
                    item.group = item.text + ' from ' + item.company_name;
                    items.push(item);
                }
            }

            return  _.sortBy(items, [function(o) { return o.text; }]);

        },
        getManagers(state) {
            var managers = _.filter(state.contacts, {
                'sf_contact_type': 'Employee'
            });
            let items = [];
            for (var i = 0; i < managers.length; i++) {
                var item = {}
                item.value = managers[i].sf_contact_id;
                item.text = managers[i].sf_contact_fname + " " + managers[i].sf_contact_lname;
                items.push(item);
            }
            return items;
        },
        getCustomersCompanies(state) {
            var customers = _.filter(state.contacts, {
                'sf_contact_type': 'Client'
            });
            var grouped = _.chain(customers)
                .groupBy("sf_contact_company_name")
                .toPairs()
                .map(function (currentItem) {
                    return _.zipObject(["sf_contact_company_name", "users"], currentItem);
                })
                .value();
            var items = []
            for (var i = 0; i < grouped.length; i++) {

                for (var j = 0; j < grouped[i].users.length; j++) {
                    var item = {}
                    item.value = grouped[i].users[j].sf_contact_id;
                    item.text = grouped[i].users[j].sf_contact_company_name + " - " + grouped[i].users[j].sf_contact_fname + " " + grouped[i].users[j].sf_contact_lname;
                    item.company_name = grouped[i].users[j].sf_contact_company_name;
                    item.company_address = grouped[i].users[j].sf_contact_company_address1;
                    item.company_state_zip = `${grouped[i].users[j].sf_contact_company_address1} ${grouped[i].users[j].sf_contact_company_city}, ${grouped[i].users[j].sf_contact_company_state} ${grouped[i].users[j].sf_contact_company_zip} `
                    item.fname = grouped[i].users[j].sf_contact_fname;
                    item.lname = grouped[i].users[j].sf_contact_lname;
                    item.email = grouped[i].users[j].sf_contact_email;
                    item.cell = grouped[i].users[j].sf_contact_mobile
                    item.disabled = false;
                    item.avatar = "";
                    item.group = item.text + ' from ' + item.company_name;
                    items.push(item);
                }
            }

            return  _.sortBy(items, [function(o) { return o.text; }]);

        },
        getBuildings(state) {
            return state.buildings;
        },
        getQuestionCounter(state) {
            return state.questionCounter;
        },
        getRoutes(state) {
            return state.routes;
        },
        getAPIKey(state) {
            return state.apikey;
        },
        getMaps(state) {
            return state.maps;
        },
        getExtendedProps(state) {
            return state.extendedbuilding;
        },
        getExtendedKeys(state) {
            return state.extendedkeys;
        },
        getDetailed(state) {
            return state.buildingdetailed;
        },
        getPhotos(state) {
            return state.pics;
        },
        getPhotosCount(state) {
            return state.photocount;
        },
        getPhotosStatus2(state) {
            return state.allpicsloaded;
        },
        getPhotoStatus(state) {
            return state.initialpicsloaded;
        },
        getFormComponents(state) {
            return state.formcjson.components;
        },
        getRapidRHStatus(state) {
            return typeof myrapidrh !== 'undefined'
        },
        getForms(state) {
            return state.forms;
        },
        getDispatches(state) {
            return state.dispatches;
        },
        getAccessCode(state) {
            return state.accessCode;
        },
        getStep(state) {
            return state.step;
        },
        getCrew(state) {
            return state.tempprofile.crew;
        },
        getRouteServe(state) {
            return state.tempactive.routeserve;
        },
        getRouteContact(state) {
            return state.tempactive.routecontact;
        },
        getProfileType(state) {
            return state.tempprofile.profiletype;
        },
        getProfileForms(state) {
            return state.tempprofile.forms;
        },
        getProfileMaps(state) {
            return state.tempactive.maps;
        },
        getProfileName(state) {
            return state.tempprofile.name;
        },
        getProfileCategory(state) {
            return state.tempprofile.cat;
        },
        getActiveProfileID(state) {
            return state.tempactive.aid;
        },
        getProfileBuilding(state) {
            return state.tempprofile.bid;
        },
        getNotes(state) {
            return state.tempprofile.notes;
        },
        getMini(state) {
            return state.mini;
        },
        getDataLoaded(state)
        {
            return state.dataLoaded;
        },
        getVendorPaymentLock(state)
        {
            return state.vendorPaymentLock;
        },
        getMapImages(state) {
            return state.mapimages;
        },
        getTotalPages(state) {
            return state.formjson.pages.length;
        },
        getPPhotos(state) {
            return state.tempprofile.photos;
        },
        getSelectedCategory(state) {
            return state.tempprofile.selectedcategory;
        },
        getCheckInOut(state) {
            return state.tempprofile.checkinout;
        },
        getAccountFlags(state) {
            return state.accountflags;
        },
        getPhotosURL(state) {
            return state.getPicturesURL;
        },
        getFileID(state) {
            return state.fileid
        },
        getFileStatus(state) {
            return state.filestatus
        },
        getFormJson(state) {
            return state.formjson;
        },
        getCurrentPage(state) {
            return state.formjson.pages[state.selectedPage]
        },
        getSelectedItem(state) {
            return state.selectedItem;
        },
        getOpenFormUrl(state) {
            return state.openFormURL;
        },
        getSubmitForm(state) {
            return state.submitForm;
        },
        getSubmitBreadcrumbURL(state) {
            return state.breadcrumb;
        },
        getUploadissuev3(state) {
            return state.uploadissuev3;
        },
        getVehicles(state){
            return state.vehicles;
        },
        getEquipments(state){
            return state.equipments;
        },
        getVendors(state){
            return state.vendors;
        },
        getMaintenanceTasks(state){
            return state.maintenanceTasks;
        },
        getVendorSettings(state) {
            return state.vendorSettings
        },
        getUserUDID(state) {
            if (state.userUDID == null && globalThis.sessionId && globalThis.sessionId != '') {
                state.userUDID = globalThis.sessionId;
            }
            return state.userUDID;
        },
    }
}
