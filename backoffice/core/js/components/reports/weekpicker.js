export default {
  template: /*html*/`
    <div style="position: relative;">
    <span @click="toggleDialog" style="cursor: pointer; display: flex; align-items: center;">
        <v-icon>mdi-calendar</v-icon>
        <span style="margin-left: 5px; font-size: 14px;">{{ formattedWeekRange }}</span>
      </span>

    <div v-if="dialogVisible" style="position: absolute; top: 100%; left: 0; border: 1px solid #ccc; background: white; padding: 10px; z-index: 10; width: 260px; box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); border-radius: 8px;">
      <div style="text-align: center; font-size: 14px; font-weight: bold; margin-bottom: 10px;">
        {{ currentDate.format('MMMM YYYY') }}
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
        <button @click="prevMonth" style="font-size: 12px; padding: 5px 10px; background-color: #007bff; color: #fff; border: none; border-radius: 4px; cursor: pointer;">Previous</button>
        <button @click="nextMonth" style="font-size: 12px; padding: 5px 10px; background-color: #007bff; color: #fff; border: none; border-radius: 4px; cursor: pointer;">Next</button>
      </div>
      <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px;">
        <div v-for="(day, index) in daysOfMonth"
             :key="index"
             :style="{ padding: '6px', fontSize: '12px', backgroundColor: isCurrentWeek(day) ? '#007bff' : '#f0f0f0', color: isCurrentWeek(day) ? '#fff' : '#000', borderRadius: '4px', textAlign: 'center', cursor: 'pointer' }"
             @click="selectWeek(day)">
          {{ formatDay(day) }}
        </div>
      </div>
      <!-- Cancel Button at the lower right corner -->
      <div style="text-align: right; margin-top: 10px;">
        <button @click="cancelDialog" style="font-size: 12px; padding: 5px 10px; background-color: #dc3545; color: #fff; border: none; border-radius: 4px; cursor: pointer;">
          Cancel
        </button>
      </div>
    </div>
    </div>
  `,

  data() {
    return {
      dialogVisible: false,
      currentDate: moment(), // Moment.js for date manipulation
      daysOfMonth: [] // Store the days to display in the calendar
    };
  },

  computed: {
    startDate() {
      return this.currentDate.clone().startOf('week'); // Start of the week (Sunday)
    },
    endDate() {
      return this.startDate.clone().endOf('week'); // End of the week (Saturday)
    },
    formattedWeekRange() {
      return `${this.formatDate(this.startDate)} - ${this.formatDate(this.endDate)}`;
    },
    formattedMonthYear() {
      return this.currentDate.format('MMMM YYYY'); // Month and Year in readable format
    }
  },

  methods: {
    toggleDialog() {
      this.dialogVisible = !this.dialogVisible;
      if (this.dialogVisible) {
        this.updateDaysOfMonth(); // Update the days when the dialog opens
      }
    },
    cancelDialog() {
      this.dialogVisible = false; // Close the dialog without any selection
    },
    formatDate(date) {
      return date.format('MM/DD/YYYY'); // Format date using Moment.js
    },
    formatDay(date) {
      return date.format('ddd D'); // Format day using Moment.js
    },
    prevMonth() {
      this.currentDate.subtract(1, 'month'); // Move to the previous month
      this.updateDaysOfMonth(); // Update the days of the month to reflect the change
    },
    nextMonth() {
      this.currentDate.add(1, 'month'); // Move to the next month
      this.updateDaysOfMonth(); // Update the days of the month to reflect the change
    },
    isCurrentWeek(day) {
      return day.isBetween(this.startDate, this.endDate, 'day', '[]'); // Check if the day is in the current week
    },
    updateDaysOfMonth() {
      const startOfMonth = this.currentDate.clone().startOf('month'); // First day of the current month
      const firstVisibleDay = startOfMonth.clone().startOf('week'); // Adjust to the Sunday before the first day of the month
      const endOfMonth = this.currentDate.clone().endOf('month'); // Last day of the current month

      this.daysOfMonth = [];
      let currentDay = firstVisibleDay.clone();

      // Add days to the array until we surpass the end of the month
      while (currentDay.isBefore(endOfMonth) || currentDay.weekday() !== 0) {
        this.daysOfMonth.push(currentDay.clone());
        currentDay.add(1, 'day');
      }
    },
    selectWeek(day) {
      this.currentDate = day.clone().startOf('week'); // Align the selected date to the start of the week (Sunday)
      this.emitWeekRange();
      this.dialogVisible = false; // Close the dialog after selection
    },
    emitWeekRange() {
      const startUnix = this.startDate.unix(); // Start of the week in Unix timestamp
      const endUnix = this.endDate.unix(); // End of the week in Unix timestamp
      this.$emit('input', `${startUnix},${endUnix}`);
    }
  },

  watch: {
    currentDate(newDate) {
      this.updateDaysOfMonth(); // Ensure days are updated whenever the current date changes
    }
  },

  mounted() {
    this.currentDate = moment().startOf('week'); // Initialize to current week (Sunday start)
    this.updateDaysOfMonth();
    this.emitWeekRange(); // Emit the initial week range when the component mounts
  }
};
