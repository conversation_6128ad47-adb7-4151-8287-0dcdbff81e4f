import DatatableEditableItemTextField
  from "../../../mapbuilder/js/components/utility/datatableeditableitemtextfield.js";
import MediaPlayer from "../components/snowestimator/media-player.js";
import ForecastSelect from "../components/snowestimator/forecast-select.js";
import DefaultSnowLevels from "../components/snowestimator/default-snow-levels.js";
import { EventBus } from "../eventbus.js";
import TabulatorDatatable from "../components/tabulator/tabulator-datatable.js";
import { toProperCase } from "../helpermethods.js";

export default {
  template: /*HTML*/
    `
      <div style="background-color: white;">
      <v-row v-if="tableLoading" style="background-color: white;" justify="center">
        <v-col align="center">
          <v-progress-circular indeterminate></v-progress-circular>
        </v-col>
      </v-row>

      <div v-else color="lighten-1" style="position:relative; height: 80vh">
        <v-card v-if="!dialogEstimatesList" elevation="0">
          <v-card-title v-if="!tableLoading" style="padding-top: 16px; padding-bottom: 0px;">
            <v-btn v-if="pageNumber == 1" @click="dismissSureDialog=true" icon color="primary">
              <v-icon>mdi-close</v-icon>
            </v-btn>
            <v-btn icon color="black" v-if="pageNumber != 1" @click="pageNumber = pageNumber-1">
              <v-icon>mdi-arrow-left</v-icon>
            </v-btn>
            {{tabulatorTitle}}
            <v-col v-show="pageNumber == 1" style="padding-top: 2px; padding-bottom: 0px;">
              <v-row no-gutters>
                <v-spacer></v-spacer>
                <v-date-picker
                    v-if="pageNumber == 1"
                    is-range
                    mode="dateTime"
                    class="inline-block mr-1"
                    v-model="dateRangePicker.range"
                    :model-config="dateRangePicker.modelConfig"
                    :popover="{ positionFixed: true }">
                  <template v-slot="{ inputValue, inputEvents, togglePopover }">
                    <div class="flex items-center" style="font-size: 12px;">
                      <v-btn style="text-align: left; height: 36px;" text class="pa-0 v-label input-date theme--light" @click="togglePopover()">
                        <span style="margin-left: 6px; text-transform: initial; font-size: 0.8rem; font-weight: 500; letter-spacing: normal; border-bottom: 1px solid rgb(0 0 0 / 42%); padding-bottom: 10px; display: flex; justify-content: space-between; width: 100%;">
                          <span style="margin-top: 11px; " v-if="dateRangePicker.range.start == null && dateRangePicker.range.end == null">Contract Dates</span>
                          <span style="margin-top: 11px; color: rgba(0, 0, 0, 0.87); font-weight: 500" v-else>{{humanizedRange}}</span>
                          <v-icon class='mt-2'>arrow_drop_down</v-icon>
                        </span>
                      </v-btn>
                    </div>
                  </template>
                </v-date-picker>
                <v-autocomplete outlined dense :disabled="!copyModeVar" v-if="pageNumber == 1" class="col-2 mx-2" label="Role" :items="roles" hide-details v-model="roleSelected"></v-autocomplete>
                <v-autocomplete outlined dense :disabled="!copyModeVar" v-if="pageNumber == 1" class="col-2" style="margin-left: 1px;" label="Site" :items="allBuildings" hide-details item-text="mb_nickname" v-model="selectedBuilding" return-object></v-autocomplete>
                <v-spacer></v-spacer>
                <v-btn @click="savePricing" color="primary" depressed :loading="saveContractLoader">Save</v-btn>
              </v-row>
            </v-col>
            <v-col v-if="pageNumber == 3">
              <v-row no-gutters>
                <v-spacer></v-spacer>
                <v-btn @click="importComplete" class="text-center ml-5" rounded depressed color="primary">
                  Next
                </v-btn>
              </v-row>
            </v-col>
          </v-card-title>
          <v-card-text>
            <p v-if="copyModeVar" style="display: none; margin-left: 24px;font-size: 16px;color: black;margin-top: 24px;">
              Copying the contract will duplicate all the pricing and settings, allowing you to make changes to the copied version without affecting the original contract.
            </p>
            <div v-show="pageNumber == 4">
              <tabulator-datatable
                  id="pricing-imported-estimate-table"
                  ref="tabulatorImportedEstimateData"
                  :tabulator-configuration="tabulatorConfigurationImportedEstimateData"
              >
                <template v-slot:custom-header>
                  <v-spacer></v-spacer>
                  <v-btn @click="onEstimateImported" class="text-center ml-5" rounded depressed color="primary">
                    Complete Import
                  </v-btn>
                </template>
              </tabulator-datatable>
            </div>

            <div v-show="pageNumber == 2">
              <tabulator-datatable
                  :show-clear-all-filters-button="true"
                  id="pricing-estimates-table"
                  ref="tabulatorEstimates"
                  :tabulator-configuration="tabulatorConfigurationEstimates"
              >
              </tabulator-datatable>
            </div>

            <v-card v-show="pageNumber == 1" :disabled="saveContractLoader" class="elevation-0">
              <tabulator-datatable
                  class="mt-2 mb-2"
                  id="pricing-table"
                  ref="tabulatorPricing"
                  @tabulatorInitialized="handleTabulatorPricingReady"
                  :name-of-file-export="exportSingleFileName"
                  :tabulator-configuration="tabulatorConfiguration"
                  :show-download-excel-button="showDownloadExcelButton"
                  :show-download-pdf-button="showDownloadPdfButton"
              >
                <template v-slot:custom-header>
                  <v-autocomplete outlined dense v-if="pageNumber == 1" class="col-2" @change="updatePricingIfDropDownChanges" label="Trade" :items="userTrades" hide-details :item-text="parseUserTradesToContractTitle" v-model="selectedTrade" return-object></v-autocomplete>
                  <v-autocomplete outlined dense v-if="pageNumber == 1 && selectedTrade.st_id == 1" class="col-2 mx-2" label="Service Levels" :items="serviceLevelsDbVersion" v-model="selectedServiceLevelDbVersion" hide-details item-text="sesl_name" return-object></v-autocomplete>
<!--                  <v-text-field outlined dense v-if="pageNumber == 1 && selectedTrade.st_id == 1" class="col-2" label="Trigger Depth(in)" type="number" hide-details v-model="triggerDepth"></v-text-field>-->
                  <v-switch dense v-model="seasonal_switch" label="Seasonal Tiers"></v-switch>
<!--                  <v-btn-toggle color="primary" v-model="toggle_seasonal_pricing_tier">-->
<!--                    <v-btn style="max-height: 36px!important;">-->
<!--                      <span>Seasonal Pricing Off</span>-->
<!--                      <v-icon>mdi-snowflake-off</v-icon>-->
<!--                    </v-btn>-->

<!--                    <v-btn style="max-height: 36px!important;">-->
<!--                      <span>Seasonal Pricing On</span>-->
<!--                      <v-icon>mdi-snowflake</v-icon>-->
<!--                    </v-btn>-->
<!--                  </v-btn-toggle>-->
                  <v-spacer></v-spacer>

                  <v-row class="ma-0 col-2" no-gutters v-if="pageNumber == 1 && roleSelected == 'CONTRACTOR'">
                    <v-col>
                      <v-btn style="text-align: left; width: 100%" text class="pa-0 v-label theme--light" @click="preOpenContractorsDialog()">
                        <span style="margin-left: 6px; text-transform: initial; color: rgba(0, 0, 0, 0.87); font-size: 0.8rem; letter-spacing: normal; border: 1px solid #e5e7eb; padding: 9px 7px 6px; border-radius: 8px; display: flex; justify-content: space-between; width: 100%;">{{parseTitle}}
                          <v-icon>arrow_drop_down</v-icon>
                        </span>
                      </v-btn>
                    </v-col>
                    <v-col md="1" justify="end">
<!--                      <v-btn icon @click="addcontractor">-->
<!--                        <v-icon>add</v-icon>-->
<!--                      </v-btn>-->
                    </v-col>
                  </v-row>
<!--                  <v-btn v-if="pageNumber == 1" class="col-1" @click="addNewRow" text color="primary" title="Add New Row">-->
<!--                    <v-icon>mdi-plus</v-icon>-->
<!--                    Row-->
<!--                  </v-btn>-->
                </template>
                <template v-slot:custom-icon-buttons>
                  <v-btn label="Add Equipment" @click="pricingNewDialog = true" icon color="primary">
                    <v-icon>
                      mdi-cog-outline
                    </v-icon>
                  </v-btn>
                  <v-btn label="Import from Estimates" @click="primaryButtonCallback" icon color="primary">
                    <v-icon>
                      mdi-import
                    </v-icon>
                  </v-btn>
                </template>
              </tabulator-datatable>
            </v-card>

            <div :style="{
                    display: pageNumber === 3 ? 'flex' : 'none',
                    flexDirection: 'column',
                    alignItems: 'center',
                    marginTop: '24px'
                  }">

              <v-simple-table id="pricing-layermatching-table" dense style="width: 50%;">
                <template v-slot:default>
                  <thead>
                  <tr>
                    <th style="width: 40%; font-size: 1.1em; font-weight: 600; color: #497cb1" class="text-center">Layers</th>
                    <th style="width: 40%; font-size: 1.1em; font-weight: 600; color: #497cb1" class="text-center">Services</th>
                    <th style="width: 20%;" class="text-right">
                      <v-btn v-show="false" @click="userServicesMatchWithImported = []" icon>
                        <v-icon>mdi-close</v-icon>
                      </v-btn>
                    </th>
                  </tr>
                  </thead>
                  <tbody style="font-size: 0.875rem; -webkit-font-smoothing: antialiased; font-family: Roboto !important; letter-spacing: 0.2px; text-shadow: none;">
                  <tr v-for="(preset,index) in presets" :key="index">
                    <td style="font-size: 16px" class="text-center">{{ preset.name }}</td>
                    <td v-show="false">
                      <v-row no-gutters>
                        <v-col v-if="importedPresetsMatching[index] !== undefined" class="text-center">
                          <span style="font-size: 16px">{{ importedPresetsMatching[index] }}</span>
                        </v-col>
                        <v-col class="text-center" v-else>
                          <v-autocomplete outlined dense :items="computedImportedPresets" v-model="importedPresetsMatching[preset.name]" item-text="name" item-value="name" hide-details></v-autocomplete>
                        </v-col>
                      </v-row>
                    </td>
                    <td>
                      <v-row align="center" justify="center">
                        <v-col>
                          <v-autocomplete outlined dense :items="userServices" v-model="userServicesMatchWithImported[index]" item-text="sst_service_type" return-object hide-details></v-autocomplete>
                        </v-col>
                      </v-row>
                    </td>
                    <td></td>
                  </tr>
                  </tbody>
                </template>
              </v-simple-table>
              
            </div>
          </v-card-text>
        </v-card>
      </div>

      <v-dialog absolute v-model="gridSelectContractor.dialog" :fullscreen="$vuetify.breakpoint.smAndDown" width="70vw" :hide-overlay="$vuetify.breakpoint.smAndDown" transition="dialog-bottom-transition">
      <v-card flat height="62vh">

          <v-card-text style="padding-left:0px;padding-right:0px">



                <tabulator-datatable
                  id="pricing-contractor-table"
                  ref="pricingContractorTable"
                  :tabulator-configuration="pricingContractorTable"
                  in-dialog
                  title="Assign to Contractor"
              >

              </tabulator-datatable>


          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text @click="cancelContractorDialog()" color="primary"> Cancel </v-btn>
            <v-btn @click="saveContractors()" color="primary">Save</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog  v-model="pricingNewDialog" persistent max-width="600px">

        <v-card>
          <v-card-title>
            Add New Service Pricing
          </v-card-title>
          <v-card-text>
            <v-autocomplete
                :items="this.uniqueServices"
                :returnObject="true"
                v-model="newItem.serviceName"
                item-text="name"
                width="350"
                label="Select Service"
                class='mb-3'
                dense  outlined hide-details>
            </v-autocomplete>

            <v-text-field outlined hide-details class='mb-3' type="text" list="existingequipment" label="Equipment Name" v-model="newItem.equipmentName"></v-text-field>
            <datalist id="existingequipment">
              <template v-for="(item,key) in uniqueEquipment" >
                <option :key="key">{{item.sesed_equipment_label}}</option>
              </template>
            </datalist>
            
            <v-text-field outlined class='mb-3' hide-details type="number" label="Selling price per Hour" v-model="newItem.sellingPrice"></v-text-field>
            <v-text-field outlined class='mb-3' hide-details type="number" label="Cost price per Hour" v-model="newItem.costPrice"></v-text-field>
            <v-text-field outlined class='mb-3' hide-details type="number" label="SF per Hour(100% Prod)" v-model="newItem.squareFoot"></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" text @click="pricingNewDialog=false">Cancel</v-btn>
            <v-btn color="primary" @click="newServicePricingItem" depressed :loading="saveLoading">Submit</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="showDeleteDialog" max-width="500">
        <v-card>
          <v-card-title>Confirm Deletion</v-card-title>
          <v-card-text>Are you sure you want to delete this row?</v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn @click="showDeleteDialog=false" text color="primary">Cancel</v-btn>
            <v-btn @click="deleteRow" color="primary" depressed :loading="deleteRowLoader">Delete</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <v-dialog v-model="dismissSureDialog" max-width="500">
        <v-card>
          <v-card-title>Dismiss</v-card-title>
          <v-card-text>Are you sure? Any unsaved changes to the pricing contracts will be lost.</v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn @click="dismissSureDialog=false" text color="primary">Cancel</v-btn>
            <v-btn @click="dismissDialog" color="primary" depressed>OK</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <v-dialog v-model="dialogInfoSeasonalTurnOff" max-width="500" persistent>
        <v-card>
          <v-card-title>Seasonal Tier</v-card-title>
          <v-card-text>Are you sure? Any changes to the Seasonal Tier values will be lost.</v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn @click="cancelTurnOffSeasonal" text color="primary">Cancel</v-btn>
            <v-btn @click="turnOffSeasonal" color="primary" depressed>OK</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      </div>`,
  watch: {
    toggle_seasonal_pricing_tier(val){
      const table = this.$refs.tabulatorPricing.tabulator;
      if (val === 0){
        this.showInfoAlertSeasonalPricesWillGoAway();
      }
      else {
        table.showColumn("seasonal_hi");
        table.showColumn("seasonal_lo");
        table.redraw(true);
      }
    },
    selectedForecast(val) {
      this.historicalSeasons = [0, this.getTotalSnowInches()]
    },
    //To reset human values
    selectedRange(newVal, oldVal) {
      if (oldVal != undefined) {
        this.calculatedPricingFromDb = {}
      }
      this.$emit('clearOldData')
    },
    "gridSelectContractor.dialog": function (val) {
      if (val) {
        this.$nextTick(() => {
          const maxWaitTime = 5000;
          let elapsedTime = 0;
          const intervalTime = 100;

          const intervalId = setInterval(() => {
            elapsedTime += intervalTime;
            if (this.$refs.pricingContractorTable && this.$refs.pricingContractorTable.tabulator) {
              clearInterval(intervalId);

              this.pricingContractorTable.data = this.contractors;
              this.$refs.pricingContractorTable.tabulator.setData(this.contractors);

              let ids = this.gridSelectContractor.tempSelection.map(a => a.value);


              this.$refs.pricingContractorTable.tabulator.selectRow(ids);
              this.$refs.pricingContractorTable.tabulator.redraw(true);

            } else if (elapsedTime >= maxWaitTime) {
              clearInterval(intervalId);
              console.warn('Timeout reached: Element not available for tabulator update.');
            }
          }, intervalTime);

        });
      }
    },
    pageNumber(val) {
      if (val == 1) {
        this.tabulatorTitle = "Pricing Contract";
        this.showPrimaryButton = true;
        setTimeout(() => {
          // Cancel any active edit before setting columns/data to prevent validation errors
          if (this.$refs.tabulatorPricing.tabulator &&
              this.$refs.tabulatorPricing.tabulator.modules &&
              this.$refs.tabulatorPricing.tabulator.modules.edit &&
              this.$refs.tabulatorPricing.tabulator.modules.edit.currentCell) {
            this.$refs.tabulatorPricing.tabulator.cancelEdit();
          }

          this.$refs.tabulatorPricing.tabulator.setColumns(this.pricingColumns);
          this.$refs.tabulatorPricing.tabulator.setData(this.pricingData);
        }, 500);
      } else if (val == 2) {
        this.tabulatorTitle = "Estimates";
        this.$refs.tabulatorEstimates.clearAllHeaderFilters();
        setTimeout(() => {
          this.$refs.tabulatorEstimates.tabulator.setColumns(this.estimateColumns);
          this.$refs.tabulatorEstimates.tabulator.setData(this.estimates);
        }, 500);
      } else if (val == 3) {
        this.tabulatorTitle = "Select Layers";
      } else if (val == 4) {
        this.tabulatorTitle = "Import Estimate";
        //tabulatorImportedEstimateData
        this.$refs.tabulatorImportedEstimateData.tabulator.setColumns([]);
        this.$refs.tabulatorImportedEstimateData.tabulator.setData([]);
        this.$refs.tabulatorImportedEstimateData.tabulator.setColumns(this.importedEstimateColumns);
        this.$refs.tabulatorImportedEstimateData.tabulator.setData(this.importedEstimateData);
        this.$refs.tabulatorImportedEstimateData.tabulator.selectRow();
        this.$refs.tabulatorImportedEstimateData.tabulator.redraw(true);
      }
    }
  },
  components: { DatatableEditableItemTextField, 'media-player': MediaPlayer, ForecastSelect, TabulatorDatatable },
  props: [
    //new props
    'copyMode',
    'parentId',
    'tradeId',
    'contractType', //Snow Removal or other types
    //old props and not tested if they are useful for new flow
    'slaData',
    'customForecasts',
    'pricingType',
    'calculatedPricingFromDb',
    //old but useful props
    'buildingId',
    'disableRoleSelector'
  ],
  data: function () {
    return {
      dialogInfoSeasonalTurnOff: false,
      toggle_seasonal_pricing_tier: 0,
      tabulatorPricingInstance: null, // To store the Tabulator instance
      deleteRowLoader: false,
      parentIdVar: this.parentId,
      copyModeVar: this.copyMode,
      dismissSureDialog: false,
      rowToDeleteForPricingContract: {},
      showDeleteDialog: false,
      saveLoading: false,
      pricingNewDialog: false,
      newItem: {
        serviceName: "",
        equipmentName: "",
        sellingPrice: "",
        squareFoot: "",
        costPrice: "",
      },
      pricingTabulatorEditMode: false,
      triggerDepth: 0,
      gridSelectContractor: {
        dialog: false,
        filter: {
          group: '',
          contractor: '',
          lname: '',
          fname: '',
          cell: ''
        },
        pagination: { itemsPerPage: -1, sortBy: ["value"] },
        headers: [
          {
            text: 'Company',
            align: 'start',
            sortable: false,
            value: 'group',
            width: '25%'
          },
          {
            text: 'First',
            align: 'start',
            sortable: false,
            value: 'fname',
            width: '23%'
          },
          {
            text: 'Last',
            align: 'start',
            sortable: false,
            value: 'lname',
            width: '22%'
          },
          {
            text: 'Email',
            align: 'start',
            sortable: false,
            value: 'email',
            width: '20%'
          },
          {
            text: 'Mobile Phone',
            align: 'start',
            sortable: false,
            value: 'cell',
            width: '20%'
          }
        ],
        showSelected: false,
        search: '',
        tempSelection: [],
        selected: []
      },
      dateRangePicker: {
        modelConfig: {
          start: {
            type: 'string',
            mask: 'iso'
          },
          end: {
            type: 'string',
            mask: 'iso'
          },
        },

        range: {
          start: null,
          end: null,
        },
        masks: {
          input: 'YYYY-MM-DD h:mm A',
        },
      },
      tabulatorConfigurationEstimates: {},
      tabulatorConfigurationImportedEstimateData: {},
      showDownloadPdfButton: true,
      showDownloadExcelButton: true,
      selectedEstimate: undefined,
      showPrimaryButton: true,
      tabulatorTitle: "Pricing Contract",
      pageNumber: 1, //1 show pricing, 2 show estimates, 3 show estimate details
      tabulatorTableEstimatesListConfiguration: undefined,
      selectedSla: undefined,
      serviceLevelsDbVersion: undefined,
      serviceLevels: undefined,
      tabulatorConfigurationImportedEstimate: undefined,
      pricingParent: {},
      pricingsmount: [],
      selectedBuildingId: null,
      selectedTrade: null,
      roles: [
        'CLIENT', 'CONTRACTOR'
      ],
      roleSelected: undefined,
      pricingTypeMatching: [
        {
          key: 'HOURS',
          value: 'Per Hour'
        },
        {
          key: 'DAY',
          value: 'Per Day'
        },
        {
          key: 'WEEK',
          value: 'Per Week'
        },
        {
          key: 'MONTH',
          value: 'Per Month'
        },
        {
          key: 'SEASON',
          value: 'Per Season'
        },
        {
          key: 'YEAR',
          value: 'Per Year'
        },
        {
          key: 'EVENT',
          value: 'Per Event'
        },
        {
          key: 'SERVICE',
          value: 'Per Service'
        },
        {
          key: 'PUSH',
          value: 'Per Push'
        },
        {
          key: 'VISIT',
          value: 'Per Visit'
        },
        {
          key: 'PARTIAL_SERVICE',
          value: 'Partial Service'
        },
        {
          key: 'ITEM',
          value: 'Per Item'
        },
        {
          key: 'INCH',
          value: 'Per Inch'
        }
      ],
      saveContractLoader: false,
      userEquipments: [],
      allServicesUnfiltered: [],
      userServices: [],
      userTrades: [],
      selectedBuildingForContract: undefined,
      contractStartDate: undefined,
      contractEndDate: undefined,
      pricing: {},
      tableLoading: true,
      tabulatorConfiguration: {},
      estimateSearch: "",
      shouldAddTandmEvenIfNotSelectedFromDropDown: false,
      tAndMEquip: [],
      tAndMMaterials: [],
      vendorServices: [],
      btnImportClientContractLoader: false,
      productionRateDecline: [],
      btnSaveLoader: false,
      globalImportedDataObj: {},
      importedPresetsMatching: [],
      importedPresets: [],
      selectedServiceLevelDbVersion: [],
      selectedEventRange: [],
      estimateImportStage: 1,
      estimates: [],
      estimateColumns: [
        {
          title: "Estimate",
          field: "see_name",
          headerFilter: "input",
          headerHozAlign: "center",
          frozen: true,
          width: 200,
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "Site Map",
          field: "sml_layer_name",
          headerFilter: "input",
          headerHozAlign: "center",
          headerFilterParams: { valuesLookup: true, clearable: true },
          width: 200,
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "City",
          field: "sml_city",
          headerFilter: "list",
          headerHozAlign: "center",
          width: 150,
          headerFilterParams: { valuesLookup: true, clearable: true },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "State",
          field: "sml_state",
          headerFilter: "list",
          headerHozAlign: "center",
          width: 100,
          headerFilterParams: { valuesLookup: true, clearable: true },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "Created By",
          field: "vendor_name",
          headerFilter: "list",
          headerHozAlign: "center",
          width: 200,
          headerFilterParams: { valuesLookup: true, clearable: true },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "Updated By",
          field: "updator_name",
          headerFilter: "list",
          headerHozAlign: "center",
          width: 200,
          headerFilterParams: { valuesLookup: true, clearable: true },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "Created",
          field: "see_created",
          headerHozAlign: "center",
          width: 150,
          frozen: true,
          formatter: function (cell, formatterParams, onRendered) {
            let dateUnParsed = cell.getValue();
            const date = moment.utc(dateUnParsed);
            return date.format('MM-DD-YYYY');
          },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        },
        {
          title: "Modified",
          field: "see_timestamp",
          headerHozAlign: "center",
          width: 150,
          frozen: true,
          formatter: function (cell, formatterParams, onRendered) {
            let dateUnParsed = cell.getValue();
            const date = moment.utc(dateUnParsed);
            return date.format('MM-DD-YYYY');
          },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.estimatesTableRowOnClick(rowData);
          }
        }
      ],
      estimateheaders: [
        { text: 'Estimate Name', align: 'left', sortable: false, value: "see_name", width: '30%' },
        { text: 'Site Map', align: 'left', value: 'sml_layer_name', width: '30%' },
        { text: 'Created', align: 'left', value: "see_created", width: '20%' },
        { text: 'Modified', align: 'left', value: "see_timestamp", width: '20%' },
      ],
      sortBy: 'see_timestamp',
      dialogEstimatesList: false,
      btnImportEstimatorLoader: false,
      historicalSeasons: [0, 96],
      serviceTypeEnums: ['PUSH_WALKS', 'PUSH_LOTS', 'DEICE_WALKS', 'DEICE_LOTS', 'PUSH_PUBLIC_WALKS', 'DEICE_PUBLIC_WALKS'],
      seasonalContractSelection: 'su',
      clientRatesSelectModel: undefined,
      clientRatesItems: [
        { key: 1, title: 'PerPush/PerInch/PerApp', value: 'pppa', contract: 'PER_PUSH' },
        { key: 2, title: 'PerEvent/PerInch/PerApp', value: 'pepipa', contract: 'PER_EVENT' },
        { key: 3, title: 'Seasonal Unlimited', value: 'su', contract: 'SEASONAL' },
        { key: 4, title: 'Seasonal Unlimited Pushes', value: 'sh', contract: 'SEASONAL_HYBRID' },
        { key: 5, title: 'Seasonal Floor Cap', value: 'sfc', contract: 'SEASONAL_FLOOR_CAP' },
        { key: 6, title: 'Time & Materials', value: 'tm', contract: 'PER_HOUR' },
        { key: 7, title: 'Per Occurrence', value: 'po', contract: 'PER_APPLICATION' },
      ],
      showAdjuster: false,
      presets: [],
      eventRanges: [],
      materialPricing: [],
      servicePricing: [],
      mainLoading: true,
      proposalCount: -1,
      proposaladbanner: true,
      dummyProposalDialog: false,
      radioButtonPerPush: 'pi',
      radioButtonPerEvent: 'pi',
      activeServicePricingModel: undefined,
      activeMaterialPricingModel: undefined,
      getPerEventMaterialPriceFromDb: true,
      calculatedData: [],
      userServicesMatchWithImported: [],
      pricingColumns: [],
      pricingData: [],
      importedEstimateColumns: [],
      importedEstimateData: [],

      pricingContractorTable: {
        placeholder: "No Data Available",
        placeholderHeaderFilter: "No Matching Data",
        reactiveData: false,
        paginationMode: "local",
        paginationSize: true,
        paginationSizeSelector: [100, 250, 500, 1000, true],
        paginationCounter: "rows",
        filterMode: "local",
        sortMode: "local",
        data: [],
        height: "100%",
        index: "value",
        layout: "fitColumns",
        virtualDom: true,
        movableColumns: false,
        selectableRows: true,
        columns: [
          {
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            titleFormatterParams: {
              rowRange: "active"
            },
            headerHozAlign: "center",
            headerSort: true,
            sorter: function (a, b, aRow, bRow, column, dir, sorterParams) {
              let aSelected = aRow.isSelected();
              let bSelected = bRow.isSelected();


              if (aSelected && !bSelected) {
                return -1;
              } else if (!aSelected && bSelected) {
                return 1;
              } else {
                return 0;
              }
            },
            frozen: true,
            hozAlign: "center",
            width: 50,
            cellClick: function (e, cell) {
              cell.getRow().toggleSelect();
            }
          },
          { title: "Company", field: "group", headerFilter: "input" },
          { title: "First Name", field: "fname", headerFilter: "input" },
          { title: "Last Name", field: "lname", headerFilter: "input" },
          { title: "Email", field: "email", headerFilter: "input" },
          { title: "Cell", field: "cell", headerFilter: "input" }
        ]
      }
    };
  },
  errorCaptured(err, vm, info) {
    console.log(err.stack, vm, info);
  },
  created() {
    this.selectedBuildingId = this.$props.buildingId;
    this.roleSelected = this.$props.pricingType;
  },
  async mounted() {
    await this.fetchEstimatorData();
    let config = {
      height: "60vh",
      data: [], //link data to table
      reactiveData: false, //enable data reactivity
      movableColumns: true,
      movableRows: true,
      persistence: {
        sort: false,
        filter: false,
        columns: false
      },
      layout: "fitColumns",
      pagination: "local",
      paginationSize: 100,
      paginationSizeSelector: [100, 200, 300, 500],
      paginationCounter: "rows",
      columns: []
    };
    this.tabulatorConfigurationEstimates = config

    let configForImportedEstimates = {
      height: "60vh",
      data: [], //link data to table
      reactiveData: false, //enable data reactivity
      movableColumns: true,
      movableRows: true,
      groupBy: "custom_group_title",
      groupStartOpen: false,
      persistence: {
        sort: false,
        filter: false,
        columns: false
      },
      layout: "fitColumns",
      pagination: "local",
      paginationSize: 100,
      paginationSizeSelector: [100, 200, 300, 500],
      paginationCounter: "rows",
      columns: []
    };
    this.tabulatorConfigurationImportedEstimateData = configForImportedEstimates;
  },
  methods: {
    showInfoAlertSeasonalPricesWillGoAway(){
      this.dialogInfoSeasonalTurnOff = true;
    },
    turnOffSeasonal(){
      const table = this.$refs.tabulatorPricing.tabulator;
      // Clear values and reset defaults
      const dt = table.getData();
      table.hideColumn("seasonal_hi");
      table.hideColumn("seasonal_lo");
      table.replaceData(dt.map( d => {
        return {
          ...d,
          seasonal_hi: 999,
          seasonal_lo: 1
        }
      }));
      table.redraw(true);
      this.dialogInfoSeasonalTurnOff = false;
    },
    cancelTurnOffSeasonal(){
      this.seasonal_switch = true;
      this.dialogInfoSeasonalTurnOff = false;
    },
    handleTabulatorPricingReady() {
      if (this.$refs.tabulatorPricing && this.$refs.tabulatorPricing.tabulator) {
        this.tabulatorPricingInstance = this.$refs.tabulatorPricing.tabulator;

        this.applyPricingTableConfiguration(false);
      } else {
        console.error("Tabulator instance not found in handleTabulatorPricingReady");
      }
    },
    applyPricingTableConfiguration(update = false) {
      if (!this.tabulatorPricingInstance) {
        console.warn("applyPricingTableConfiguration called before instance is ready.");
        return;
      }


      const services = this.allServicesUnfiltered.filter(s => s.sst_trade_id === this.selectedTrade.st_id);
      const equipments = this.userEquipments;
      const materials = this.materials;

      const pricingColumns = this.initTabulatorForPricing(this.pricingParent, services, equipments, materials); // Get columns

      // Cancel any active edit before setting columns to prevent validation errors
      if (this.tabulatorPricingInstance &&
          this.tabulatorPricingInstance.modules &&
          this.tabulatorPricingInstance.modules.edit &&
          this.tabulatorPricingInstance.modules.edit.currentCell) {
        this.tabulatorPricingInstance.cancelEdit();
      }

      this.tabulatorPricingInstance.setColumns(pricingColumns);


      const onRenderCompleteCallback = () => {
        console.log("renderComplete triggered");

        this.tabulatorPricingInstance.off("renderComplete", onRenderCompleteCallback);


        if (update) {
          console.log("Applying column update for equipment");

          this.tabulatorPricingInstance.updateColumnDefinition("equipment", {
            editorParams: {
              values: equipments.map((ewp) => {
                return {
                  label: ewp.set_equipment_name,
                  value: ewp.set_id
                }
              })
            }
          });
        }

      };


      this.tabulatorPricingInstance.on("renderComplete", onRenderCompleteCallback);

      // Cancel any active edit before setting data to prevent validation errors
      if (this.tabulatorPricingInstance &&
          this.tabulatorPricingInstance.modules &&
          this.tabulatorPricingInstance.modules.edit &&
          this.tabulatorPricingInstance.modules.edit.currentCell) {
        this.tabulatorPricingInstance.cancelEdit();
      }

      this.tabulatorPricingInstance.setData(this.pricingData);
    },
    async loadContractorTable() {
      //wait for 50ms
      await new Promise(r => setTimeout(r, 50));
      this.$refs.pricingContractorTable.tabulator.setData(this.contractors);
      this.$refs.pricingContractorTable.tabulator.redraw(true);

    },
    async deleteRow() {
      let row = this.rowToDeleteForPricingContract;
      let rowData = row.getData();
      let sps_id = rowData.dbRowId;
      if (sps_id != 0 && sps_id != undefined) {
        this.deleteRowLoader = true;
        await fetch(`/node/pricing/delete-pricing-row`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            row_id: sps_id
          })
        });
      }
      this.$refs.tabulatorPricing.tabulator.deleteRow(row);
      this.deleteRowLoader = false;
      this.showDeleteDialog = false;
    },
    async newServicePricingItem() {
      if (this.newItem.serviceName.trim() == '' || this.newItem.equipmentName.trim() == '' || this.newItem.sellingPrice.trim() == '' || this.newItem.squareFoot.trim() == '' || this.newItem.costPrice.trim() == '') {
        this.snackbar.text = 'Please fill in all the four fields'
        this.snackbar.snackbar = true;
        return;
      }
      if (parseFloat(this.newItem.sellingPrice) == NaN || parseInt(this.newItem.squareFoot) == NaN || parseFloat(this.newItem.costPrice) == NaN) {
        this.snackbar.text = 'Please enter correct values in the fields'
        this.snackbar.snackbar = true;
        return;
      }
      this.saveLoading = true;
      const formBody = new URLSearchParams();
      formBody.append("accessCode", store.get('accessCode'));
      formBody.append("serviceName", this.newItem.serviceName);
      formBody.append("equipmentName", this.newItem.equipmentName);
      formBody.append("sellingPrice", this.newItem.sellingPrice);
      formBody.append("costPrice", this.newItem.costPrice);
      formBody.append("squareFoot", this.newItem.squareFoot);
      formBody.append("tradesArray", JSON.stringify([this.selectedTrade.st_id]));
      let response = await fetch(myBaseURL + '/vpics/estimatorservicenewitem', {
        method: 'POST',
        body: formBody,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (response.ok) {
        let data = await response.text();
        this.servicePricing.push({
          sesed_service_name: this.newItem.serviceName,
          sesed_equipment_label: this.newItem.equipmentName,
          sesed_price_hour: this.newItem.sellingPrice,
          sesed_cost_hour: this.newItem.costPrice,
          sesed_sf_hour: this.newItem.squareFoot,
          sesed_active: 'Active',

        })
        this.newItem = {
          serviceName: "",
          equipmentName: "",
          sellingPrice: "",
          squareFoot: "",
          costPrice: "",
        }
        this.$emit("refreshdata")
        this.$nextTick(() => {
          this.pricingNewDialog = false;
        });
      } else {
        this.snackbar.text = 'Failed. Are you still connected to the internet?'
        this.snackbar.snackbar = true;
      }
      //update already fetched equipments
      let services = this.allServicesUnfiltered.filter(s => s.sst_trade_id == this.selectedTrade.st_id);

      let eq = await fetch(`/node/services/equipment`);
      eq = await eq.json();
      this.userEquipments = eq;
      //Lets reload table
      this.applyPricingTableConfiguration(true);
      this.saveLoading = false;
    },
    cancelContractorDialog() {
      this.gridSelectContractor.dialog = false;
    },
    saveContractors() {
      //getselections from tabulator
      let selectedRows = this.$refs.pricingContractorTable.tabulator.getSelectedData();
      this.gridSelectContractor.selected = [...selectedRows];
      this.gridSelectContractor.dialog = false;
    },
    addcontractor() {
      EventBus.$emit('AddContact', "Contractor");
    },
    preOpenContractorsDialog() {
      this.gridSelectContractor.tempSelection = [...this.gridSelectContractor.selected];
      this.gridSelectContractor.dialog = true;
    },
    async onEstimateImported() {
      this.pricingData = await this.$refs.tabulatorImportedEstimateData.tabulator.getSelectedData();
      this.$nextTick(() => {
        this.pageNumber = 1;
      })
    },
    importComplete() {
      // let importedServices = this.userServices.filter(us => this.userServicesMatchWithImported.includes(us.sst_id));
      let matchedPresets = [];
      for (const [index, value] of this.userServicesMatchWithImported.entries()) {
        if (value != undefined) {
          let preset = this.presets[index];
          matchedPresets[preset.name] = value.sst_id
        }
      }
      this.onPresetSelectionComplete(matchedPresets)
    },
    onPresetSelectionComplete(matchedPresets) {
      let selectedRangeFromEstimates = this.selectedEventRange;
      let dataFinal = []
      if (selectedRangeFromEstimates == undefined) {
        this.$refs.tabulatorEstimates.showAlert("Estimate has no data for import.");
      }
      else {
        let services = this.allServicesUnfiltered.filter(s => s.sst_trade_id == this.selectedTrade.st_id);
        let equipments = this.userEquipments;
        this.selectedRange = selectedRangeFromEstimates;
        let estimate = this.selectedEstimate;

        const highestStartAndEnd = this.currentPRDData.reduce((result, item) => {
          if (
            item.sesp_snow_trigger_start > result.sesp_snow_trigger_start ||
            (item.sesp_snow_trigger_start === result.sesp_snow_trigger_start &&
              item.sesp_snow_trigger_end > result.sesp_snow_trigger_end)
          ) {
            return item;
          } else {
            return result;
          }
        });


        estimate = estimate.map(e => {
          if (e.group == 'Per Push') {
            let service = 0;
            //added full flag to know if it is deicing full or partial
            //added unit flag, should not worry nasir's code
            if (e.unit == 'Per Service' && e.full) {
              service = services.find(s => s.sst_service_type == 'Deice Walks').sst_id;
            } else if (e.unit == 'Per Service' && !e.full) {
              service = services.find(s => s.sst_service_type == 'Deice Walks (partial)').sst_id;
            } else {
              service = matchedPresets[e.title] == undefined ? (e.title.toLowerCase().includes("lots") ? services.find(s => s.sst_service_type == 'Push Lots').sst_id : services.find(s => s.sst_service_type == 'Push Walks').sst_id) : matchedPresets[e.title];
            }

            return {
              service: service,
              lo: e.low.toFixed(2),
              hi: e.high.toFixed(2),
              unit: e.unit,
              price: parseFloat(e.value.replace(/[^0-9.-]/g, '')),
              custom_group_title: e.group,
              equipment: 0
            }

          }
          else if (e.group == 'Per Event') {
            let service = 0;
            if (e.unit == 'Per Service' && e.full) {
              service = services.find(s => s.sst_service_type == 'Deice Lots').sst_id;
            } else if (e.unit == 'Per Service' && !e.full) {
              service = services.find(s => s.sst_service_type == 'Deice Lots (partial)').sst_id;
            } else {
              service = matchedPresets[e.title] == undefined ? (e.title.toLowerCase().includes("lots") ? services.find(s => s.sst_service_type == 'Push Lots').sst_id : services.find(s => s.sst_service_type == 'Push Walks').sst_id) : matchedPresets[e.title];
            }
            return {
              service: service,
              lo: e.low.toFixed(2),
              hi: e.high.toFixed(2),
              unit: e.unit,
              price: parseFloat(e.value.replace(/[^0-9.-]/g, '')),
              custom_group_title: e.group,
              equipment: 0
            }
          } else if (e.group == 'Seasonal') {
            let service = 0;
            if (e.title == 'Unlimited') {
              service = services.find(s => s.sst_service_type == "Push Lots").sst_id;
            } else if (e.title == 'Unlimited with Pushes') {
              service = services.find(s => s.sst_service_type == "Push Lots").sst_id;
            } else if (e.title == 'Deicing Full Per App') {
              service = services.find(s => s.sst_service_type == "Deice Lots").sst_id;
            } else if (e.title == 'Seasonal Deicing partial') {
              service = services.find(s => s.sst_service_type == "Deice Lots (partial)").sst_id;
            } else if (e.title == 'w/ Floor & Cap') {
              service = services.find(s => s.sst_service_type == "Deice Lots (partial)").sst_id;
            } else {
              service = services.find(s => s.sst_service_type == "Push Lots").sst_id;
            }
            return {
              service: service,
              lo: 0,
              hi: 0,
              unit: e.unit,
              price: parseFloat(e.value.replace(/[^0-9.-]/g, '')),
              custom_group_title: e.group,
              equipment: 0
            }
          }
        })
        //TandM import Equipment
        for (const pricing of this.activeServicePricing) {
          estimate.push({
            service: pricing.sesed_service_name.toLowerCase().includes("lot") ? services.find(s => s.sst_service_type == "Push Lots").sst_id : services.find(s => s.sst_service_type == "Push Walks").sst_id,
            lo: highestStartAndEnd.sesp_snow_trigger_start,
            hi: highestStartAndEnd.sesp_snow_trigger_end,
            unit: "Per Hour",
            price: pricing.sesed_adjusted_price_hour,
            custom_group_title: "Time & Materials",
            equipment: equipments.find(e => e.set_equipment_name.toLowerCase() == pricing.sesed_equipment_label.toLowerCase()) == undefined ? 0 : equipments.find(e => e.set_equipment_name.toLowerCase() == pricing.sesed_equipment_label.toLowerCase()).set_id //TODO: ASK NASIR HOW THESE TWO TABLES ARE CONNECTED,sitefotos_estimator_services_equipment_data, sitefotos_equipment_types
          });
        }
        let self = this;
        this.importedEstimateColumns = [
          {
            title: "",
            field: "",
            formatter: "rowSelection",
            titleFormatter: "rowSelection",
            width: 50,
            titleFormatterParams: {
              rowRange: "active"
            },
            hozAlign: "center",
            headerSort: false,
            frozen: true,
            print: false,
            download: false
          },
          {
            title: "Service",
            field: "service",
            hozAlign: "center",
            headerHozAlign: "center",
            formatter: function (cell, formatterParams, onRendered) {
              let serviceDbId = cell.getValue();
              if (serviceDbId != '') {
                let selectedService = services.find((ss) => ss.sst_id == serviceDbId);
                return selectedService.sst_service_type;
              }
            }
          },
          {
            title: "Price",
            field: "price",
            hozAlign: "center",
            headerHozAlign: "center",
            formatter: function (cell, formatterParams, onRendered) {
              let price = cell.getValue();
              price = self.formatDollarAmount(price);
              return price;
            }
          },
          {
            title: "Unit",
            field: "unit",
            hozAlign: "center",
            headerHozAlign: "center",
          },
          {
            title: "Min (hours)",
            field: "minimum_minutes",
            hozAlign: "center",
            headerHozAlign: "center",
            editor: "list",
            editorParams: {
              values: [
                "30 minutes",
                "45 minutes",
                "1 hour",
                "1 hour 15 minutes",
                "1 hour 30 minutes",
                "1 hour 45 minutes",
                "2 hours",
                "2 hours 15 minutes",
                "2 hours 30 minutes",
                "2 hours 45 minutes",
                "3 hours"
              ]
            }
          },
          {
            title: "Equipment",
            field: "equipment",
            hozAlign: "center",
            headerHozAlign: "center",
            formatter: function (cell, formatterParams, onRendered) {
              let equipmentDbId = cell.getValue();
              if (equipmentDbId != '') {
                let selectedEquipment = equipments.find((eqp) => eqp.set_id == equipmentDbId);
                if (selectedEquipment) {
                  return selectedEquipment.set_equipment_name;
                } else {
                  return "";
                }
              }
            }
          },
          {
            title: "Lo (in)",
            field: "lo",
            hozAlign: "center",
            headerHozAlign: "center",
          },
          {
            title: "Hi (in)",
            field: "hi",
            hozAlign: "center",
            headerHozAlign: "center",
          }
        ];
        this.importedEstimateData = estimate;
        this.$nextTick(() => {
          this.pageNumber = 4;
        })
      }
    },
    parseUserTradesToContractTitle(item) {
      return `${toProperCase(item.st_trade)}`;
    },
    primaryButtonCallback() {
      this.startImportFromEstimator()
    },
    addNewRow() {
      this.$refs.tabulatorPricing.tabulator.addRow(
        {
          dbRowId: 0,
          service: undefined,
          price: 0,
          unit: '',
          equipment: "",
          lo: 0,
          hi: 1
        }
      );
    },
    async refreshParent() {
      let selectedSiteId = this.selectedBuildingId;
      let selectedPricingContractType = this.roleSelected;
      let selectedTradeId = this.selectedTrade.st_id;
      this.copyModeVar = false;

      let response = await fetch(`/node/pricing/site/${selectedSiteId}/${selectedPricingContractType}/${selectedTradeId}/${this.parentIdVar}`);
      response = await response.json();
      const parent = response.parent;
      this.pricingParent = parent;
      const pricingRows = response.pricingRows;
      this.pricingsmount = pricingRows;

      //Update id for rows
      let pricingContractConvertedForTabulator = [];
      for (const pricingContractRow of pricingRows) {
        //TODO: fix this
        let unit = this.pricingTypeMatching.find(ptm => ptm.key == pricingContractRow.pricingBaseOrUnit);
        if (unit == undefined) {
          unit = this.pricingTypeMatching.find(ptm => ptm.value == pricingContractRow.contractDropDownType);
        }
        let obj = {
          dbRowId: pricingContractRow.id,
          service: pricingContractRow.serviceTypeId,
          price: pricingContractRow.price,
          minimum_minutes: pricingContractRow.minimum_minutes,
          unit: unit == undefined ? "" : unit.value,
          equipment: pricingContractRow.equipmentServiceId,
          materials: `${pricingContractRow.materialId}`,
          lo: pricingContractRow.snowThresholdStart,
          hi: pricingContractRow.snowThresholdEnd,
          seasonal_lo: pricingContractRow.seasonal_lo,
          seasonal_hi: pricingContractRow.seasonal_hi,
          use_seasonal_values: pricingContractRow.use_seasonal_values
        };
        pricingContractConvertedForTabulator.push(obj);
      }

      // Cancel any active edit before setting data to prevent validation errors
      if (this.$refs.tabulatorPricing.tabulator &&
          this.$refs.tabulatorPricing.tabulator.modules &&
          this.$refs.tabulatorPricing.tabulator.modules.edit &&
          this.$refs.tabulatorPricing.tabulator.modules.edit.currentCell) {
        this.$refs.tabulatorPricing.tabulator.cancelEdit();
      }

      this.$refs.tabulatorPricing.tabulator.setData(pricingContractConvertedForTabulator);
    },
    minuteTextToValue(text){
      const arr = [
        {
          label: "30 minutes",
          value: 30  // 30 minutes
        },
        {
          label: "45 minutes",
          value: 45  // 45 minutes
        },
        {
          label: "1 hour",
          value: 60  // 1 hour in minutes
        },
        {
          label: "1 hour 15 minutes",
          value: 75  // 1 hour 15 minutes
        },
        {
          label: "1 hour 30 minutes",
          value: 90  // 1 hour 30 minutes
        },
        {
          label: "1 hour 45 minutes",
          value: 105  // 1 hour 45 minutes
        },
        {
          label: "2 hours",
          value: 120  // 2 hours in minutes
        },
        {
          label: "2 hours 15 minutes",
          value: 135  // 2 hours 15 minutes
        },
        {
          label: "2 hours 30 minutes",
          value: 150  // 2 hours 30 minutes
        },
        {
          label: "2 hours 45 minutes",
          value: 165  // 2 hours 45 minutes
        },
        {
          label: "3 hours",
          value: 180  // 3 hours in minutes
        }
      ].find( a => a.label == text);
      return arr;
    },
    minuteValueToText(value){
      const arr = [
        {
          label: "30 minutes",
          value: 30  // 30 minutes
        },
        {
          label: "45 minutes",
          value: 45  // 45 minutes
        },
        {
          label: "1 hour",
          value: 60  // 1 hour in minutes
        },
        {
          label: "1 hour 15 minutes",
          value: 75  // 1 hour 15 minutes
        },
        {
          label: "1 hour 30 minutes",
          value: 90  // 1 hour 30 minutes
        },
        {
          label: "1 hour 45 minutes",
          value: 105  // 1 hour 45 minutes
        },
        {
          label: "2 hours",
          value: 120  // 2 hours in minutes
        },
        {
          label: "2 hours 15 minutes",
          value: 135  // 2 hours 15 minutes
        },
        {
          label: "2 hours 30 minutes",
          value: 150  // 2 hours 30 minutes
        },
        {
          label: "2 hours 45 minutes",
          value: 165  // 2 hours 45 minutes
        },
        {
          label: "3 hours",
          value: 180  // 3 hours in minutes
        }
      ].find( a => a.value == value);
      return arr;
    },
    async savePricing() {
      //IMPORTANT: Following data for key service has sst_id from database instead of service text.
      //Same for equipment as well.
      let valid = await this.$refs.tabulatorPricing.tabulator.validate();
      if (!this.selectedBuilding) {
        this.$refs.tabulatorPricing.showAlert("Please select a site");
        return;
      }

      //This will return a value of true if every cell passes validation, if any cells fail, then it will return an array of Cell Components representing each cell in that row that has failed validation.
      if (valid === true) {
        let data = this.$refs.tabulatorPricing.getData();
        data = data.map(d => {
          return {
            ...d,
            unit: this.pricingTypeMatching.find(ptm => ptm.value == d.unit).key,
            use_seasonal_values: this.toggle_seasonal_pricing_tier === 1 ? d.use_seasonal_values:0,
            minimum_minutes_converted:
              this.minuteTextToValue(d.minimum_minutes) === undefined
                ? isNaN(d.minimum_minutes)
                  ? null:d.minimum_minutes
                : this.minuteTextToValue(d.minimum_minutes).value
          }
        });

        let selectedSiteId = this.selectedBuildingId;
        let selectedPricingContractType = this.roleSelected;
        let selectedTradeId = this.selectedTrade.st_id;

        let start = moment(this.dateRangePicker.range.start).unix();
        let end = moment(this.dateRangePicker.range.end).unix();

        if (data.length == 0) {
          this.$refs.tabulatorPricing.showAlert("Your Contract is Empty");
        } else if (isNaN(start)) {
          this.$refs.tabulatorPricing.showAlert("Please select Start Date");
        } else if (isNaN(end)) {
          this.$refs.tabulatorPricing.showAlert("Please select End Date");
        }
        else {
          this.saveContractLoader = true;


          let contract = {
            copy: this.copyModeVar,
            siteName: this.selectedBuilding.mb_nickname,
            clientName: this.selectedBuilding.clientName,
            parent: this.pricingParent,
            triggerDepth: parseFloat(this.triggerDepth).toFixed(2),
            serviceLevelId: this.selectedServiceLevelDbVersion.sesl_id,
            contractorsSelected: this.gridSelectContractor.selected.map(gc => gc.value),
            selectedTrade: selectedTradeId,
            contractStartDateInUnix: start,
            contractEndDateInUnix: end,
            siteId: selectedSiteId,
            contract: data,
            contractFor: selectedPricingContractType,
            tradeId: selectedTradeId
          };

          let storeContract = await fetch(`/node/pricing/add-pricing-contract`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify(contract)
          });

          storeContract = await storeContract.json();
          if (storeContract.success == 1) {
            this.parentIdVar = storeContract.id;
            await this.refreshParent();
            this.$refs.tabulatorPricing.showAlert(storeContract.message);
          } else {
            this.$refs.tabulatorPricing.showAlert("Something went wrong");
          }
          this.saveContractLoader = false;
        }
      } else {
        this.$refs.tabulatorPricing.showAlert("Please check and fix your pricing data.");
      }
    },
    async updatePricingIfDropDownChanges() {
      this.saveContractLoader = true;
      const selectedSiteId = this.selectedBuildingId;
      const selectedPricingContractType = this.roleSelected;
      const selectedTradeId = this.selectedTrade.st_id;
      if (selectedSiteId > 0 && this.parentIdVar != -1) {
        const response = await fetch(`/node/pricing/site/${selectedSiteId}/${selectedPricingContractType}/${selectedTradeId}/${this.parentIdVar}`);
        const data = await response.json();
        this.pricingParent = data.parent;
        this.pricingsmount = data.pricingRows;
      }

      this.saveContractLoader = false;

      const services = this.allServicesUnfiltered.filter(s => s.sst_trade_id === selectedTradeId);
      const equipments = this.userEquipments;
      const startDate = this.pricingParent.spc_season_start_date;
      const endDate = this.pricingParent.spc_season_stop_date;

      if (startDate && endDate) {
        this.dateRangePicker.range.start = new Date(startDate * 1000);
        this.dateRangePicker.range.end = new Date(endDate * 1000);
      }

      this.tabulatorConfiguration = {
        height: "60vh",
        data: this.pricingData,
        reactiveData: false,
        movableColumns: true,
        movableRows: true,
        persistence: {
          sort: false,
          filter: false,
          columns: false,
        },
        layout: "fitColumns",
        pagination: "local",
        paginationSize: 100,
        paginationSizeSelector: [100, 200, 300, 500],
        paginationCounter: "rows",
        columns: [],
      };
      this.applyPricingTableConfiguration(false);

    },
    async fetchEstimatorData() {
      this.tableLoading = true;
      const accessCode = store.get('accessCode');

      try {
        const response = await Promise.all([
          fetch(`/node/services/service-types`),
          fetch(`/node/services/equipment`),
          fetch(`/node/services/trades`),
          fetch(`/node/estimator/estimates`),
          fetch(`/node/estimator/getAssumptionsForVendor?accessCode=${accessCode}`)
        ]);

        const [services, equipments, trades, estimatorData, assumptionResponse] = await Promise.all(response.map(res => res.json()));

        this.userEquipments = equipments;
        //Filtering trades to 3 for now snow removal, landscaping and lot sweeping
        const filteredTradesDBIds = new Set([1, 2, 3, 25, 31, 56]);
        this.userTrades = trades.filter((td) => filteredTradesDBIds.has(td.st_id));
        //Setting default contract type to snow getting passed as prop
        this.selectedTrade = this.userTrades.find(ut => ut.st_id == this.$props.tradeId);

        this.allServicesUnfiltered = services;
        this.userServices = services.filter(s => s.sst_trade_id == this.selectedTrade.st_id);
        let es2 = estimatorData.sort((a, b) => new Date(b.see_created).getTime() - new Date(a.see_created).getTime());

        // const filteredArray = es2.filter(e => {
        //   try {
        //     const dataObj = JSON.parse(e.see_data);
        //     return dataObj.selectedEventRange !== undefined;
        //   } catch (error) {
        //     console.error("Error parsing JSON:", error);
        //     return false; // or true, depending on how you want to handle invalid JSON data
        //   }
        // });
        this.estimates = es2;
        //Asumptions
        this.productionRateDecline = assumptionResponse.depthProdRate;
        this.servicePricing = assumptionResponse.equipmentPricing.filter(service => service['sesed_status'] === 'Active');
        let serviceLevelData = {}
        this.serviceLevelsDbVersion = assumptionResponse.serviceLevels;
        assumptionResponse.serviceLevels.forEach(range => {
          serviceLevelData[range['sesl_name']] = { 'lot_pushes': JSON.parse(range['sesl_lot_pushes_range']), 'lot_deices': JSON.parse(range['sesl_lot_deices_range']), 'walk_pushes': JSON.parse(range['sesl_walk_pushes_range']), 'walk_deices': JSON.parse(range['sesl_walk_deices_range']), 'id': range['sesl_id'] }
        })
        this.serviceLevels = serviceLevelData;
        this.selectedSla = this.availableServiceLevels[0];

        await this.updatePricingIfDropDownChanges()
      } catch (error) {
        console.error('Error:', error);
      }
      this.tableLoading = false
      EventBus.$emit("onComponentFetchDone");
    },
    initTabulatorForPricing(parent, services, equipments, materials) {
      let self = this;
      const pricingColumns = [
        { rowHandle: true, formatter: "handle", headerSort: false, frozen: true, width: 30, minWidth: 30 },
        {
          title: "Service",
          field: "service",
          editor: "list",
          frozen: true,
          width: 150,
          hozAlign: "center",
          headerHozAlign: "center",
          validator: "required",
          formatter: function (cell, formatterParams, onRendered) {
            let serviceDbId = cell.getValue();
            let selectedService = services.find((ss) => ss.sst_id == serviceDbId);
            if (selectedService) {
              return selectedService.sst_service_type;
            }
          },
          editorParams: {
            values: services.map((ss) => {
              return {
                label: ss.sst_service_type,
                value: ss.sst_id
              }
            })
          },
          accessorDownload: function (value, data, type, params, column) {
            let serviceDbId = value;
            if (serviceDbId != '') {
              let selectedService = services.find((ss) => ss.sst_id == serviceDbId);
              return selectedService.sst_service_type;
            }
          }
        },
        {
          title: "Price",
          field: "price",
          editor: "input",
          hozAlign: "center",
          headerHozAlign: "center",
          width: 150,
          validator: "required",
          cellEditing: function (cell) {
            self.pricingTabulatorEditMode = true
          },
          cellEditCancelled: function (cell) {
            self.pricingTabulatorEditMode = false
          },
          cellEdited: function (cell) {
            self.pricingTabulatorEditMode = false
          },
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          },
          formatter: function (cell, formatterParams, onRendered) {
            let price = cell.getValue();
            price = self.formatDollarAmount(price);
            return price;
          },
          accessorDownload: function (value, data, type, params, column) {
            return self.formatDollarAmount(value);
          }
        },
        {
          title: "Unit",
          field: "unit",
          editor: "list",
          width: 150,
          headerHozAlign: "center",
          hozAlign: "center",
          validator: "required",
          editorParams: {
            values: [//enum types
              'Per Push',
              'Per Hour',
              'Per Service',
              'Partial Service',
              'Per Month',
              'Per Day',
              'Per Week',
              'Per Season',
              'Per Item',
              'Per Year',
              'Per Inch',
              'Per Event'
            ]
          }
        },
        {
          title: "Min (hours)",
          field: "minimum_minutes",
          hozAlign: "center",
          headerHozAlign: "center",
          editor: "list",
          formatter: (cell, formatterParams, onRendered) => {
            const value = cell.getValue();
            //TODO: Fix issue
            return isNaN(value) ? value : value === 0 ? null:this.minuteValueToText(value).label;
          },
          editorParams: {
            values: [
              "30 minutes",
              "45 minutes",
              "1 hour",
              "1 hour 15 minutes",
              "1 hour 30 minutes",
              "1 hour 45 minutes",
              "2 hours",
              "2 hours 15 minutes",
              "2 hours 30 minutes",
              "2 hours 45 minutes",
              "3 hours"
            ]
          }
        },
        {
          title: "Equipment",
          field: "equipment",
          editor: "list",
          headerHozAlign: "center",
          hozAlign: "center",
          cssClass: "custom-checkbox-cell",
          editable: function (cell) {
            const data = cell.getRow().getData();
            //Only enable equipments for per hour
            if (data.unit == "Per Hour") {
              return true;
            }
          },
          formatter: function (cell, formatterParams, onRendered) {
            let equipmentDbId = cell.getValue();
            if (equipmentDbId != '') {
              let selectedEquipment = equipments.find((eqp) => eqp.set_id == equipmentDbId);
              if (selectedEquipment) {
                return selectedEquipment.set_equipment_name;
              } else {
                return "";
              }
            }
          },
          editorParams: {
            values: equipments.map((ewp) => {
              return {
                label: ewp.set_equipment_name,
                value: ewp.set_id
              }
            })
          },
          accessorDownload: function (value, data, type, params, column) {
            let equipmentDbId = value;
            if (equipmentDbId != '') {
              let selectedEquipment = equipments.find((eqp) => eqp.set_id == equipmentDbId);
              if (selectedEquipment) {
                return selectedEquipment.set_equipment_name;
              } else {
                return "";
              }
            }
          }
        },
        {
          title: "Materials",
          field: "materials",
          editor: "list",
          headerHozAlign: "center",
          hozAlign: "center",
          editable: function (cell) {
            const data = cell.getRow().getData();
            //Only enable materials for per item
            if (data.unit == "Per Item") {
              return true;
            }
          },
          formatter: function (cell, formatterParams, onRendered) {
            let materialDbId = cell.getValue();
            if (materialDbId != '') {
              let selectedMaterial = materials.find((eqp) => eqp.mat_id == materialDbId);
              if (selectedMaterial) {
                return selectedMaterial.mat_material_name;
              } else {
                return "";
              }
            }
          },
          editorParams: {
            values: materials.map((ewp) => {
              return {
                label: ewp.mat_material_name,
                value: ewp.mat_id
              }
            })
          }
        },
        {
          title: "Lo(in)",
          field: "lo",
          editor: "input",
          width: 100,
          hozAlign: "center",
          headerHozAlign: "center",
          visible: this.selectedTrade.st_id == 1 ? true : false,
          validator: "required",
          cellEditing: function (cell) {
            self.pricingTabulatorEditMode = true
          },
          cellEditCancelled: function (cell) {
            self.pricingTabulatorEditMode = false
          },
          cellEdited: function (cell) {
            self.pricingTabulatorEditMode = false
          },
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Hi(in)",
          field: "hi",
          editor: "input",
          width: 100,
          hozAlign: "center",
          headerHozAlign: "center",
          visible: this.selectedTrade.st_id == 1 ? true : false,
          validator: "required",
          cellEditing: function (cell) {
            self.pricingTabulatorEditMode = true
          },
          cellEditCancelled: function (cell) {
            self.pricingTabulatorEditMode = false
          },
          cellEdited: function (cell) {
            self.pricingTabulatorEditMode = false
          },
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Seasonal Lo",
          field: "seasonal_lo",
          visible: false,
          width: 150,
          editor: "input",
          hozAlign: "center",
          headerHozAlign: "center",
          mutatorData: (value) => value ?? 0,
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Seasonal Hi",
          field: "seasonal_hi",
          visible: false,
          width: 150,
          editor: "input",
          hozAlign: "center",
          headerHozAlign: "center",
          mutatorData: (value) => value ?? 999,
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Actions",
          field: "actions",
          frozen: true,
          print: false,
          download: false,
          width: 120,
          hozAlign: "center",
          headerHozAlign: "center",
          formatter: (cell, formatterParams, onRendered) => {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();

            let container = document.createElement("div");

            const addNewRow = this.$refs.tabulatorPricing.createButton('mdi-plus', 'Add Row', () => {
              this.addNewRow()
            });

            const duplicateButton = this.$refs.tabulatorPricing.createButton('mdi-content-copy', 'Add Row', () => {
              let newRowData = Object.assign({}, rowData);
              newRowData.dbRowId = 0;
              table.addRow(newRowData);
            });

            const deleteButton = this.$refs.tabulatorPricing.createButton('mdi-delete', 'Add Row', () => {
              this.rowToDeleteForPricingContract = row;
              this.showDeleteDialog = true;
            });

            container.appendChild(addNewRow);
            container.appendChild(duplicateButton);
            container.appendChild(deleteButton);
            return container;
          },
        }
      ];

      if (this.pricingsmount.length > 0) {
        let pricingContractForThisSiteFromDb = this.pricingsmount;
         //SelectedServiceLevel
        let selectedServiceLevelFromDb = parent.spc_sesl_id;

        if (selectedServiceLevelFromDb == 0 || selectedServiceLevelFromDb == undefined || selectedServiceLevelFromDb == null) {
          //Default to
          this.selectedServiceLevelDbVersion = this.serviceLevelsDbVersion.length > 0 && this.serviceLevelsDbVersion[0];
        } else {
          this.selectedServiceLevelDbVersion = this.serviceLevelsDbVersion.find(sldbv => sldbv.sesl_id == selectedServiceLevelFromDb)
        }
        //Trigger depth
        this.triggerDepth = parent.spc_trigger_depth;
        //if contractors is set to -1, it means all of them are selected

        if (parent.spc_contractors == -1) {
          this.gridSelectContractor.selected = this.contractors;
        } else {
          //value
          let contractorIds = parent.spc_contractors;
          this.gridSelectContractor.selected = this.contractors.filter(c => contractorIds.includes(c.value));
        }

        let pricingContractConvertedForTabulator = [];

        const hasAtLeastOneSeasonal = pricingContractForThisSiteFromDb.some(row => row.use_seasonal_values === 1);

        if (hasAtLeastOneSeasonal) {
          const seasonalHiCol = pricingColumns.find(pc => pc.field === "seasonal_hi");
          if (seasonalHiCol) seasonalHiCol.visible = true;
          const seasonalLoCol = pricingColumns.find(pc => pc.field === "seasonal_lo");
          if (seasonalLoCol) seasonalLoCol.visible = true;
          const seasonalCheckbox = pricingColumns.find(pc => pc.field === "use_seasonal_values");
          if (seasonalCheckbox) seasonalCheckbox.visible = true;
          this.toggle_seasonal_pricing_tier = 1;
        }

        for (const pricingContractRow of pricingContractForThisSiteFromDb) {
          let unit = this.pricingTypeMatching.find(ptm => ptm.key == pricingContractRow.pricingBaseOrUnit);
          if (unit == undefined) {
            unit = this.pricingTypeMatching.find(ptm => ptm.value == pricingContractRow.contractDropDownType);
          }
          let obj = {
            dbRowId: pricingContractRow.id,
            service: pricingContractRow.serviceTypeId,
            price: pricingContractRow.price,
            minimum_minutes: pricingContractRow.minimum_minutes,
            unit: unit == undefined ? "" : unit.value,
            equipment: pricingContractRow.equipmentServiceId,
            materials: `${pricingContractRow.materialId}`,
            lo: pricingContractRow.snowThresholdStart,
            hi: pricingContractRow.snowThresholdEnd,
            seasonal_lo: pricingContractRow.seasonal_lo,
            seasonal_hi: pricingContractRow.seasonal_hi,
            use_seasonal_values: pricingContractRow.use_seasonal_values
          };
          pricingContractConvertedForTabulator.push(obj);
        }
        this.pricingData = pricingContractConvertedForTabulator;
      } else {
         this.pricingData = [{
          dbRowId: 0,
          id: 1,
          service: 100,
          price: 0,
          minimum_minutes: 0,
          unit: "Per Push",
          equipment: "",
          materials: "",
          lo: 0,
          hi: 1
        }];
        this.selectedServiceLevelDbVersion = this.serviceLevelsDbVersion.length > 0 && this.serviceLevelsDbVersion[0];
      }


      return pricingColumns;
    },
    dismissDialog() {
      EventBus.$emit('onCancel')
    },
    startImportFromEstimator() {
      this.pageNumber = 2;
      this.userServicesMatchWithImported = [];
    },
    async estimatesTableRowOnClick(item) {
      let see_id = item.see_id;
      // let see_data = item.see_data;
      let estimate = await fetch(myBaseURL + '/node/estimator/parse-estimate/' + see_id);
      estimate = await estimate.json();
      estimate = estimate.groupedData;
      this.selectedEstimate = estimate;

      let request = await fetch(myBaseURL + `/node/estimator/estimate/${see_id}`)
      let res = await request.json();
      let dataObj = JSON.parse(res.see_data);
      // //Lets get all presets
      this.globalImportedDataObj = dataObj
      let dataPresets = dataObj.presets;
      this.presets = dataPresets;
      this.selectedForecast = dataObj.selectedForecast;
      //
      this.importedPresets = dataPresets;
      this.selectedEventRange = dataObj.selectedEventRange;

      // this.pageNumber = 3;
      if (dataObj.selectedEventRange == undefined) {
        this.$refs.tabulatorPricing.showAlert("Estimate has no data for import.");
      }
      else {
        this.pageNumber = 3;
      }
    },
    //Snow bidder helpers
    adjustSeasonalUsingFloorCaps(seasonalWithFloorCapPrice) {
      let lowerValue = parseInt(this.historicalSeasons[0]);
      let upperValue = parseInt(this.historicalSeasons[1]);
      let calculatedPrice = seasonalWithFloorCapPrice * (upperValue - lowerValue)
      return calculatedPrice;
    },
    getMaterialSlaTotalSeasonalHybrid() {
      let total = 0;
      for (let j = 0; j < this.presets.length; j++) {
        total += this.getMaterialTotals(this.presets[j]);
      }
      return total;
    },
    formatDollarAmount(v) {
      try {
        if (!isFinite(v)) {
          return "-";
        }
        return v.toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
        });
      }
      catch (e) {
        console.log(v, e)
      }
    },
    getPerPushTotal(mypreset, start = null, end = null) {
      let total = 0;
      if (typeof (mypreset) === "string") {
        mypreset = this.presets.find(o => o.name === mypreset)
      }

      let effectiveProductionRate = start === null ? 1 : this.getEffectiveProductionRate(start, end);
      return mypreset.totalserviceprice / effectiveProductionRate;
    },
    getEffectiveProductionRate(start, end) {
      const prd_data = this.productionRateDecline.map(x => [x.sesdpr_snow_trigger_end + .01, x.sesdpr_rate_decline]).sort((a, b) => a[0] - b[0])
      function getProductionRateDecline(inches) {
        let i = 0;
        for (i = 0; i < prd_data.length; i++) {
          if (prd_data[i][0] > inches)
            break;
        }
        return prd_data[i][1] / 100.;
      }

      return this.getBlendedValue(start, end, getProductionRateDecline);
    },
    getBlendedValue(start, end, f) {
      if (isNaN(start) || isNaN(end)) return NaN;

      if (start === end) {
        return f(start);
      }
      end += .01;
      const startInt = Math.ceil(start);
      const endInt = Math.floor(end);

      let total = 0, div = 0;
      for (var i = startInt; i < endInt; i++) {
        total += Number(f(i));
        div++;
      }

      const partialStartWeight = startInt - start;
      const partialEndWeight = end - endInt;
      // Changed Math.ceil to Math.floor to fix issue with rounding, may need to revist.
      if (partialEndWeight) total += f(Math.floor(end)) * partialEndWeight;
      if (partialStartWeight) total += f(Math.floor(start)) * partialStartWeight;

      // Required to handle the case when start and end don't have an integer boundary
      div += partialStartWeight + partialEndWeight;
      return total / div;
    },
    getServiceSlaTotal(preset, item) {
      return this.getPerEventTotal(preset, item.sesp_snow_trigger_start, item.sesp_snow_trigger_end);
    },
    getPerEventTotal(preset, start, end) {
      const mysla = this.getServiceLevelForSnowrange(preset, start, end);
      const deicing = mysla.ses_num_deicing;
      const deicing_modifier = deicing > 0 ? 1 : 0;
      const perPushStart = Math.max(0, start - deicing_modifier) / Math.max(1, mysla.ses_num_pushes);
      const perPushEnd = Math.max(0, end - deicing_modifier) / Math.max(1, mysla.ses_num_pushes);
      let mypreset = null;
      if (typeof (preset) === "string") {
        mypreset = this.presets.find(o => o.name === preset)
      } else {
        mypreset = preset
      }

      let effectiveProductionRate = perPushStart === null ? 1 : this.getEffectiveProductionRate(perPushStart, perPushEnd);
      const perPushTotal = mypreset.eventserviceprice / effectiveProductionRate;
      return perPushTotal * mysla.ses_num_pushes
    },
    getServiceLevelForSnowrange(preset, start, end) {
      const serviceLevelData = this.serviceLevels[this.selectedSla];

      function valForSnowstart(lst, inches) {
        if (lst == null) return undefined;
        if (inches < 0) return lst[0];
        if (inches >= lst.length) return lst[lst.length - 1];
        return lst[inches];
      }

      let deices = serviceLevelData?.lot_deices;
      let pushes = serviceLevelData?.lot_pushes;

      if (this.isSidewalkType(preset)) {
        deices = serviceLevelData?.walk_deices;
        pushes = serviceLevelData?.walk_pushes;
      }

      return {
        ses_num_deicing: this.getBlendedValue(start, end, x => valForSnowstart(deices, x)),
        ses_num_pushes: this.getBlendedValue(start, end, x => valForSnowstart(pushes, x))
      };
    },
    isSidewalkType(preset) {
      if (typeof (preset) === "string") {
        preset = this.presets.find(o => o.name === preset)
      }

      for (const layer of preset.layers) {
        if (layer.equipment.length === 0)
          continue;

        const equipmentId = layer.equipment[0]?.sesed_id;
        const equipment = this.activeServicePricing.find(o => o.sesed_id === equipmentId);
        if (equipment === null)
          continue;

        return equipment.sesed_service_name === "Walk";
      }

      return false;
    },
    getSeasonalMaterialTotal() {
      let total = 0;
      this.presets.forEach(preset => {
        if (this.snowData) {
          this.snowData.forEach(mysnow => {
            const perEventTotal = this.getPerEventMaterialTotal(preset, mysnow.sesp_snow_trigger_start, mysnow.sesp_snow_trigger_end);
            if (isFinite(perEventTotal)) {
              total += perEventTotal * mysnow.events;
            }
          })
        }
      })

      return total;
    },
    getSeasonalServiceTotal() {
      let total = 0;
      this.presets.forEach(preset => {
        if (this.snowData) {
          this.snowData.forEach(mysnow => {
            const perEventTotal = this.getPerEventTotal(preset, mysnow.sesp_snow_trigger_start, mysnow.sesp_snow_trigger_end);
            if (isFinite(perEventTotal)) {
              total += perEventTotal * mysnow.events;
            }
          })
        }
      })
      return total;
    },
    getPerEventMaterialTotal(preset, start, end) {
      const mysla = this.getServiceLevelForSnowrange(preset, start, end);
      return this.getMaterialTotals(preset) * mysla.ses_num_deicing;
    },
    getMaterialSlaTotal(item) {
      let total = 0;
      for (let j = 0; j < this.presets.length; j++) {
        total += this.getPerEventMaterialTotal(this.presets[j], item.sesp_snow_trigger_start, item.sesp_snow_trigger_end);
      }
      return total;
    },
    getMaterialTotals(mypreset) {
      let total = 0;
      if (typeof (mypreset) === "string") {
        mypreset = this.presets.find(o => o.name === mypreset)
      }
      for (let i = 0; i < mypreset.layers.length; i++) {
        if (isFinite(mypreset.layers[i]?.materialprice)) {
          total = total + mypreset.layers[i].materialprice;
        }
      }
      return total;
    },
    getTotalSnowInches() {
      let total = 0
      if (this.snowData) {
        this.snowData.forEach(data => {
          total += data.sesp_snow_trigger_start * data.events
        })
      }
      return total;
    },
  },
  computed: {
    seasonal_switch: {
      get() {
        return this.toggle_seasonal_pricing_tier === 1;
      },
      set(val) {
        this.toggle_seasonal_pricing_tier = val ? 1 : 0;
      }
    },
    materials: {
      get() {
        const mats = _.filter(this.$store.getters.getMaterials, {
          'material_own': "1"
        });
        const matsSorted = _.orderBy(mats, ['mat_material_name'], ['asc']);
        return matsSorted;
      },
    },
    uniqueEquipment() {
      return _.uniqBy(this.servicePricing, 'sesed_equipment_label')
    },
    uniqueServices() {
      return ['Lot', 'Walk']
    },
    exportSingleFileName: {
      get() {
        return 'Contract Export-' + moment().format("MM-DD-YY")
      }
    },
    isButtonDisabled() {
      const data = this.pricingData; // Replace this with the actual data array you want to check
      const start = this.dateRangePicker.range.start; // Replace this with the actual start date value
      const end = this.dateRangePicker.range.end; // Replace this with the actual end date value

      if (data.length === 0) {
        return true;
      } else if (!isNaN(start) || !isNaN(end)) {
        return true;
      } else {
        return false;
      }
    },
    contractors: {
      get() {
        return this.$store.getters.getContractors;
      },
    },
    parseTitle: {
      get() {
        let itemsForTitle = this.gridSelectContractor.selected;
        let title = "";
        if (itemsForTitle.length > 0) {
          if (itemsForTitle.length == 1) {
            title = `${itemsForTitle[0].text}`;
            return title.length > 20 ? title.substring(0, 19) + "..." : title;
          }
          else {
            title = `${itemsForTitle[0].text}`;
            title = `${title.length > 10 ? title.substring(0, 9) + "..." : title} (+${itemsForTitle.length - 1} others)`;
            return title;
          }
        }
        else {
          return "Assign to Contractor"
        }
      }
    },
    humanizedRange() {
      let DateTime = luxon.DateTime;
      if (!this.dateRangePicker.range.start && !this.dateRangePicker.range.end) return null;

      let startLuxon = DateTime.fromJSDate(this.dateRangePicker.range.start);
      let endLuxon = DateTime.fromJSDate(this.dateRangePicker.range.end);
      if (startLuxon.invalid) {
        startLuxon = DateTime.fromISO(this.dateRangePicker.range.start);
        endLuxon = DateTime.fromISO(this.dateRangePicker.range.end);
      }
      return startLuxon.toFormat('MMM d, yyyy') + " - " + endLuxon.toFormat('MMM d, yyyy');
    },
    headers() {
      let headers = [];
      for (let i = 0; i < this.presets.length; i++) {
        if (this.presets[i].totalserviceprice) {
          headers.push(this.presets[i].name)
        }
      }
      return headers;
    },
    selectedBuilding: {
      get() {
        if (this.selectedBuildingId) {
          return this.allBuildings.find(b => b.mb_id == this.selectedBuildingId);
        }
        return null;
      },
      set(newBuilding) {
        // Update the selectedBuildingId when the computed property is set
        if (newBuilding) {
          this.selectedBuildingId = newBuilding.mb_id;
        } else {
          this.selectedBuildingId = null;
        }
      },
    },
    allBuildings: {
      get() {
        const builds = this.$store.getters.getBuildings;
        if (!builds)
          return [];
        const contacts = this.$store.getters.getContacts;
        const buildsParsed = builds.reduce((a, o) => {
          if (o.mb_status == '1') {
            const client = contacts.find(a => a.sf_contact_id == o.mb_client);
            a.push({ ...o, clientName: typeof client != 'undefined' ? client.sf_contact_company_name : "" })
          }
          return a;
        }, []);
        return buildsParsed;
      }
    },
    computedImportedPresets() {
      return this.importedPresets.filter((ip) => !this.importedPresetsMatching.includes(ip.name)).filter((ip) => ip.totalserviceprice > 0);
    },
    pricingOption: VuexPathify.sync('estimator/pricingOption'),
    snowData() {
      if (this.selectedForecast) {
        return this.selectedForecast.data;
      }
      return [];
    },
    availableServiceLevels() {
      return Object.keys(this.serviceLevels)
    },
    currentPRDData() {
      let dt = DefaultSnowLevels.mapToRanges(this.selectedRange?.range).map((interval, idx) => ({
        sesp_id: idx,
        sesp_snow_trigger_start: interval[0],
        sesp_snow_trigger_end: interval[1] - .01,
        sesp_rate_decline: 100,
        price_per_row: 0
      }));
      return dt;
    },
    activeServicePricing() {
      if (Object.keys(this.calculatedPricingFromDb).length > 0) {
        //get from db
        this.servicePricing = this.calculatedPricingFromDb.tandmadjusted.activeServicePrice;
        this.activeServicePricingModel = this.servicePricing;
        this.activeServicePricingModel = this.activeServicePricingModel.map(sp => ({ ...sp, sesed_price_hour_with_icon: sp.sesed_price_hour.toFixed(2) }))
        return this.activeServicePricingModel;
      }
      else {
        this.servicePricing = this.servicePricing.map(sp => ({ ...sp, sesed_adjusted_price_hour: sp.sesed_price_hour.toFixed(2), sesed_price_hour_with_icon: sp.sesed_price_hour.toFixed(2) }));
        this.activeServicePricingModel = this.servicePricing.filter(a => a.sesed_active == 'Active');
        return this.activeServicePricingModel
      }
    },
    activeMaterialPricing() {
      if (Object.keys(this.calculatedPricingFromDb).length > 0) {
        //get from db
        this.materialPricing = this.calculatedPricingFromDb.tandmadjusted.activeMaterialPrice;
        this.activeMaterialPricingModel = this.materialPricing;
        this.activeMaterialPricingModel = this.activeMaterialPricingModel.map(sp => ({ ...sp, semd_price_hour_with_icon: sp.semd_price_unit.toFixed(2) }))
        return this.activeMaterialPricingModel;
      }
      else {
        this.materialPricing = this.materialPricing.map(sp => ({ ...sp, semd_adjusted_price_unit: sp.semd_price_unit.toFixed(2), semd_price_hour_with_icon: sp.semd_price_unit.toFixed(2) }));
        this.activeMaterialPricingModel = this.materialPricing.filter(a => a.semd_active == 'Active');
        return this.activeMaterialPricingModel
      }
    },
    getTAndMMaterials() {
      return this.tAndMMaterials;
    },
    getTAndMEquip() {
      return this.tAndMEquip;
    }
  },
}
