import companyProfile from "./company-profile.js";

export default {
    template: /*html*/`
    <div>
    <v-text-field hide-details outlined v-if="!!employeeCompany" :disabled="true" dense label="Company" :value="employeeCompany"></v-text-field>
    <v-autocomplete hide-details outlined v-else dense ref="companySelection" :items="companies" item-text="sf_company_name" item-value="sf_company_name" :rules="[rules.required]" label="Company" v-model="companyName" append-outer-icon="mdi-plus" @click:append-outer="dialogManageCompany = true"></v-autocomplete>
    
<!--    <v-text-field hide-details outlined  dense ref="companySelection" list="existing" :rules="[rules.required]" label="Company" v-model="companyName" append-outer-icon="mdi-pencil" @click:append-outer="dialogManageCompany = true"></v-text-field>-->
<!--    <datalist id="existing">-->
<!--        <template v-for="(item,key) in companies">-->
<!--            <option>{{item.sf_company_name}}</option>-->
<!--        </template>-->
<!--    </datalist>-->
    <v-dialog scrollable absolute v-model="dialogManageCompany" :fullscreen="$vuetify.breakpoint.smAndDown" :hide-overlay="$vuetify.breakpoint.smAndDown" width="70vw" transition="dialog-bottom-transition">
        <v-card height="400pt" class="overflow-hidden">
        <v-card-text class="pa-0">
        <tabulator-datatable title="Companies"
        :tabulator-configuration="tabulatorConfigCompanies"
        refresh-type="local"
        :show-clear-all-filters-button="true"
        ref="companiesTable"
        :show-primary-button="pagePermissionAddCompany"
        @rowClick="editCompanyClicked"
        @primaryButtonCallback="createNewCompany"
        :primary-button-config='{
            "icon": "mdi-plus",
            "title": "Add Company"
        }'> 
        <template v-slot:back-button>
            <v-btn icon @click="dialogManageCompany = false">
                <v-icon>mdi-close</v-icon>
            </v-btn>
        </template>
        </tabulator-datatable>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAddCompany" max-width="500px">
        <company-profile :key="companyID" :companyID="companyID" :states="states" @dismiss-self="companyID = undefined; dialogAddCompany = false" @company-saved="companySaved"></company-profile>
    </v-dialog>
    </div>
    `,
    props: ['editedItem', 'states', 'employeeCompany'],
    inject: ['getCompanies', 'deleteCompany', 'addUpdateCompany', 'showSnakeAlert', 'permissionAdd', 'permissionEdit', 'permissionDelete'],
    components: { companyProfile },

    data: function () {
        return {
            companyID: undefined,
            companyName: undefined,
            dialogManageCompany: false,
            dialogAddCompany: false,
            rules: {
                required: value => !!value || 'Required.',
            },
            tabulatorConfigCompanies: {
                placeholder: "No Data Available",
                placeholderHeaderFilter: "No Matching Data",
                data: [],
                reactiveData: false,
                printStyled: true, //copy table styling to exported table
                height: "450px",
                maxHeight: "450px",
                minHeight: "450px",
                movableColumns: true,
                resizableRows: false, // this option takes a boolean value (default = false)
                downloadRowRange: "selected", //change default selector to selected
                movableRows: false,
                layout: "fitColumns",
                pagination: true,
                paginationMode: "local",
                paginationSize: 100,
                paginationSizeSelector: [100, 250, 500, 1000],
                paginationCounter: "rows",
                filterMode: "local",
                sortMode: "local",
                columns: [
                    {
                        title: "Logo",
                        field: "sf_company_profile_pic",
                        width: 120,
                        headerSort: false,
                        formatter: 'image',
                        formatterParams: {
                            height: "25px",
                        },
                    },
                    {
                        title: "Company Name",
                        field: "sf_company_name",
                        headerFilter: "input",
                        headerHozAlign: "center",
                        minWidth: 200,
                        headerFilterParams: { clearable: true },
                    },
                    {
                        title: "Phone",
                        field: "sf_company_phone",
                        headerFilter: "input",
                        headerHozAlign: "center",
                        minWidth: 200,
                        headerFilterParams: { clearable: true },
                    },
                    {
                        title: "Email",
                        field: "sf_company_email",
                        headerFilter: "input",
                        headerHozAlign: "center",
                        minWidth: 150,
                        headerFilterParams: { clearable: true },
                    },
                    {
                        title: "Address",
                        field: "sf_company_address1",
                        headerFilter: "input",
                        headerHozAlign: "center",
                        minWidth: 200,
                        headerFilterParams: { clearable: true },
                    },
                    {
                        title: "City",
                        field: "sf_company_city",
                        headerFilter: "list",
                        headerHozAlign: "center",
                        hozAlign: "center",
                        minWidth: 100,
                        headerFilterParams: { valuesLookup: true, multiselect: true, clearable: true },
                    },
                    {
                        title: "State",
                        field: "sf_company_state",
                        headerFilter: "list",
                        headerHozAlign: "center",
                        hozAlign: "center",
                        minWidth: 100,
                        headerFilterParams: { valuesLookup: true, multiselect: true, clearable: true },
                    },
                    {
                        title: "Country",
                        field: "sf_company_country",
                        headerFilter: "list",
                        headerHozAlign: "center",
                        hozAlign: "center",
                        minWidth: 120,
                        headerFilterParams: { valuesLookup: true, multiselect: true, clearable: true },
                    },
                    {
                        title: "Actions",
                        field: "actions",
                        headerHozAlign: "center",
                        headerSort: false,
                        frozen: true,
                        width: 90,
                        visible: this.pagePermissionDeleteCompany,
                        hozAlign: "center",
                        formatter: (cell, formatterParams, onRendered) => {
                            let row = cell.getRow();
                            let rowData = row.getData();
                            let container = document.createElement("div");
                            let btnDelete = this.$refs.companiesTable.createButton("mdi-delete", "Delete", async (e) => {
                                e.stopPropagation();
                                await this.deleteCompanyTabulator(rowData);
                            });
                            container.appendChild(btnDelete);
                            return container;
                        }
                    }
                ]
            },
        }
    },
    watch: {
        companies(newCompanies) {
            this.updateTableData(newCompanies);
        }
    },

    methods: {
        async saveCompanyDetails() {
            if (!this.companyName) return
            //Find company name in existing companies list caseinsensitive and lowercase
            let company = this.getCompanies().find(item => item.sf_company_name.toLowerCase() == this.companyName.toLowerCase());
            if (!company) {
                //Quick create company.
                company = await this.createCompany(this.companyName);
                this.addUpdateCompany(company);
            }
            this.editedItem.sf_contact_company_id = company.sf_company_id;
        },
        async createCompany(companyName) {
            const data = {
                sf_company_name: companyName,
            }
            const response = await fetch(`/node/contact/company`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data)
            });
            this.loading = false;
            if (response.ok) {
                return await response.json();
            } else if (response.status == 400) {
                const responseJSON = await response.json();
                this.showSnakeAlert(responseJSON.error);
            } else {
                this.showSnakeAlert('Error saving company');
            }
        },
        updateTableData(companies) {
            this.tabulatorConfigCompanies.data = companies;
            if (this.$refs.companiesTable && this.$refs.companiesTable.tabulator) {
                this.$refs.companiesTable.tabulator.replaceData(companies);
            }
        },
        createNewCompany() {
            this.$refs.companySelection.blur();
            this.companyID = undefined;
            this.dialogAddCompany = true;
        },
        async companySaved(companyInfo) {
            this.companyID = companyInfo.sf_company_id;
            this.dialogAddCompany = false;
        },
        editCompanyClicked(e, row) {
            if (!this.pagePermissionEditCompany) return
            const rowData = row.getData();
            this.companyID = rowData.sf_company_id;
            this.dialogAddCompany = true;
        },
        async deleteCompanyTabulator(rowData) {
            //Ask for confirmation
            if (!confirm('Are you sure you want to delete this company?')) {
                return;
            }
            const response = await fetch(`/node/contact/company`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ companyID: rowData.sf_company_id })
            });
            if (response.status == 200) {
                this.showSnakeAlert('Company deleted successfully.');
                this.deleteCompany(rowData.sf_company_id);
            } else if (response.status == 400) {
                const responseJSON = await response.json();
                this.showSnakeAlert(responseJSON.error);
            } else {
                this.showSnakeAlert('Unknow error occured while deleting company.');
            }
        },
    },
    beforeMount() {
        this.companyName = this.editedItem.sf_contact_company_id ? this.getCompanies().find(item => item.sf_company_id == this.editedItem.sf_contact_company_id)?.sf_company_name : undefined;
    },

    computed: {
        companies() {
            return this.getCompanies().toSorted((item, item2) => item.sf_company_name > item2.sf_company_name).map(item => {
                item.sf_company_id = item.sf_company_id ? item.sf_company_id.toString() : '';
                item.sf_company_profile_pic = item.sf_company_profile_pic ? item.sf_company_profile_pic : window.origin + '/images/company-placeholder.png';
                return item;
            });
        },
        pagePermissionAddCompany() {
            let userPermissions = window.userpermissions[59] || [];
            const permission = userPermissions.find(a => a["Permission"] == 'Add');
            return permission ? permission['Value'] : true;
          },
          pagePermissionEditCompany() {
            let userPermissions = window.userpermissions[59] || [];
            const permission = userPermissions.find(a => a["Permission"] == 'Edit');
            return permission ? permission['Value'] : true;
          },
          pagePermissionDeleteCompany() {
            let userPermissions = window.userpermissions[59] || [];
            const permission = userPermissions.find(a => a["Permission"] == 'Delete');
            return permission ? permission['Value'] : true;
          },

    },

    mounted() {
        this.updateTableData(this.companies);
    }
}
