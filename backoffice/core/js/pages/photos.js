import vdaterange from '../components/vdaterange.js'
import DropdownMultipleDatatableHeader from "../components/dropdownmultipledatatableheader.js";
import TabulatorDatatable from "../components/tabulator/tabulator-datatable.js";
import DropdownSimpleDatatableHeader from "../components/dropdown-simple-datatable-header.js";
import {
    EventBus
} from '../eventbus.js'
const photoDateRangePicker = {
    name: 'photoDateRangePicker',
    template: `
    <v-date-picker is-range mode="dateTime" class="inline-block" v-model="range" :model-config="modelConfig" :popover="{ positionFixed: true }" @popoverDidHide="change">
    <template v-slot="{ inputValue, inputEvents, togglePopover }">
      <div class="flex items-center">
      
        
        <v-text-field @click="togglePopover()" 
        outlined
        hide-details
        :value="humanizedRange" clearable @click:clear="clickClear" 
          class="hidden-sm-and-down" style="max-width: 120px; width: 120px; margin-left:6px;" 
          readonly>  <template v-slot:prepend-inner>
        <v-tooltip
          bottom
        >
          <template v-slot:activator="{ on }">
            <v-icon v-on="on" @click="togglePopover()" >
            mdi-calendar
            </v-icon>
          </template>
          Select a date range
        </v-tooltip>
      </template>
      </v-text-field>
      </div>
    </template>
  </v-date-picker>
    `,
    data: function () {
        return {
            modelConfig: {
                start: {
                    type: 'string',
                    mask: 'iso',
                },
                end: {
                    type: 'string',
                    mask: 'iso',
                },
            },

            range: {
                start: null,
                end: null,
            },
        };
    },
    methods: {
        clickClear() {
            this.range.start = null;
            this.range.end = null;
            this.$emit('input', {});
        },
        change(e) {
            if (this.range.start && this.range.end)
                this.$emit('input', { start: this.range.start, end: this.range.end });
        }
    },
    computed: {

        humanizedRange() {
            let DateTime = luxon.DateTime;
            if (!this.range.start && !this.range.end) return null;

            let startLuxon = DateTime.fromISO(this.range.start);
            let endLuxon = DateTime.fromISO(this.range.end);
            if (startLuxon.hasSame(endLuxon, 'month')) {
                return startLuxon.toFormat('d') + "-" + endLuxon.toFormat('d MMM');
            } else {
                return startLuxon.toFormat('d MMM') + " - " + endLuxon.toFormat('d MMM');
            }

        }
    }
};
const html = /*HTML*/`<div style="width:100%">
    <v-app-bar app fixed elevate-on-scroll clipped-left class="mainback"
               v-if="$route.path!='/livemap' && $route.path!='/mapbuilder' && selectedPhotos.length==0"
               style="border-bottom:1px solid #e5e5e5;">
        <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
        <div class="">
            <img src="/images/sitefotos_logo_icon.svg" style="width:44px; height:44px;padding-right:10px;">
        </div>
        <span class="page-titles">Photos</span>
        <v-spacer></v-spacer>

        <v-autocomplete outlined dense class="hidden-sm-and-down " id="searchsite" deletable-chips clearable outlined hide-details
                v-model="gridSelect.selected" return-object item-text="mb_nickname" content-class="selectmenufix1"
                item-value="mb_id" :items="autocompleteData" label="Sites" multiple small-chips
                style="max-width: 230px; width: 230px; height: 44px;max-height: 44px; margin-top: 1px">
    <template v-slot:selection="{ item, index }">
        <v-chip small v-if="index === 0"><span>{{ item.mb_nickname.length > 10 ? item.mb_nickname.substring(0, 10) + "..." : item.mb_nickname }}</span></v-chip>
        <span v-if="index === 1" class="grey--text caption">(+{{ gridSelect.selected.length - 1 }} others)</span>
    </template>
    <template slot="item" slot-scope="data">
        <v-list-item-content>
            <!-- Check if item is the "Untagged" item -->
            <v-list-item-title v-if="data.item.mb_id === 0">
                {{ data.item.mb_nickname }}
                <div style="width: 100%; height: 3px; margin-top:10px" aria-hidden="true" >
                <v-divider ></v-divider> <!-- Divider for the "Untagged" item -->
               </div>
            </v-list-item-title>
            <v-list-item-title v-else-if="data.item.mb_external_src=='BossLM'" v-html="'* '+data.item.mb_nickname"></v-list-item-title>
            <v-list-item-title v-else v-html="data.item.mb_nickname"></v-list-item-title>
        </v-list-item-content>
    </template>


</v-autocomplete>


        <v-btn v-if="false" class="hidden-sm-and-down" text @click="gridSelect.dialog=true" style="max-width: 230px;border-bottom-color: #00000099; color:#00000099; border-bottom-style: solid;border-bottom-right-radius: 0;border-bottom-left-radius: 0;border-bottom-width: 0.5px;margin-bottom: 14px;text-transform: none;  font-size: 1rem; min-width: 200px; font-weight: normal; letter-spacing: normal; word-spacing: normal;">
            {{ gridSelect.selected.length > 0 ? parseNick() : 'Sites' }}
             <v-spacer></v-spacer>
          <v-icon dark right>arrow_drop_down</v-icon>
        </v-btn>

        <v-btn icon @click="gridSelect.dialog=true" class="mr-1" style='width: 30px !important; height: 30px !important;'>
          <v-icon dark>mdi-magnify</v-icon>
        </v-btn>
        
        <v-dialog absolute scrollable v-if="gridSelect.dialog" v-model="gridSelect.dialog" :fullscreen="$vuetify.breakpoint.smAndDown" width="70vw" :hide-overlay="$vuetify.breakpoint.smAndDown" transition="dialog-bottom-transition">
  <v-card height="62vh" class="elevation-0 overflow-hidden">
    <v-progress-linear v-if="gridSelect.loading" color="blue" indeterminate></v-progress-linear>
    <v-card-text height="50vh" style="padding-left:0px;padding-right:0px;" class="overflow-hidden">
      <tabulator-datatable
        :tabulator-configuration="gridSelect.tabulatorConfig"
        title="Sites"
        refresh-type="local"
        @localRefresh="refreshSites"
        id="siteSelectionTabulator"
        ref="siteSelectionTabulator"
        :show-clear-all-filters-button="true"
        :show-refresh-button="true"
        @tabulatorInitialized="refreshSites"
      >
        <template v-slot:back-button>
          <v-btn icon @click="gridSelect.dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </template>
      </tabulator-datatable>
    </v-card-text>
    <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn color="primary" :loading="loading" @click="selectSites">Select</v-btn>
    </v-card-actions>
  </v-card>
</v-dialog>
        <photo-date-range-picker class='photo-date-range mt-1' v-model="range" @input="onDateRangeChange"></photo-date-range-picker>
       
        <v-combobox outlined hide-details class="hidden-sm-and-down " deletable-chips content-class="selectmenufix1"
                    v-model="selectedKeywords" dense clearable :items="getkeywords"
                    :search-input.sync="selectedKeywordsSearch" hide-selected label="Keywords"
                    style="max-width: 230px; width: 230px;height: 44px;max-height: 44px;  margin-left:6px;" multiple
                    small-chips>
            <template v-slot:selection="{ item, index }">
                <v-chip small v-if="index === 0">
                    <span>{{ item.length > 8 ? item.substring(0, 8) + "..." : item }}</span>
                </v-chip>
                <span v-if="index === 1" class="grey--text caption">(+{{ selectedKeywords.length - 1 }}
            others)</span>
            </template>
            <template slot="no-data">
                <v-list-item>
                    <v-list-item-content>
                        <v-list-item-title>No results matching "<strong>{{ selectedKeywordsSearch }}</strong>". Press
                            <kbd>enter</kbd>
                            to create a new one
                        </v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
            </template>

        </v-combobox>
        <v-autocomplete v-if="isClientViewEnabled" outlined hide-details class="hidden-sm-and-down" deletable-chips
        v-model="selectedSubcontractors" dense clearable :items="subcontractorsData"
         item-text="name" item-value="id" :loading="initialDataLoaded == false"
        label="Contractors" content-class="selectmenufix1" return-object
        style="max-width: 230px; width: 230px;height: 44px;max-height: 44px; margin-left:6px;" multiple
        small-chips>
<template v-slot:selection="{ item, index }">
<v-chip small v-if="index === 0">
    <span>{{ item.name && item.name.length > 8 ? item.name.substring(0, 8) + "..." : item.name }}</span>
</v-chip>
<span v-if="index === 1" class="grey--text caption">(+{{ selectedSubcontractors.length - 1 }} others)</span>
</template>
</v-autocomplete>
        <v-menu full-width nudge-bottom=50 :close-on-content-click="false" v-model="searchmenu" bottom
                transition="scale-transition" v-if="false">
            <v-card>
                <v-card-text>
                    <v-layout wrap>
                        <v-flex xs12 sm12 md12>
                            <v-combobox deletable-chips prepend-icon="account_balance" clearable
                                        v-model="gridSelect.selected" return-object item-text="mb_nickname"
                                        item-value="mb_id" :items="activebuildingsdata" label="Filter Photos By Sites"
                                        multiple small-chips></v-combobox>
                        </v-flex>
                        <v-flex xs12 sm12 md12>
                            <vdaterange no-title v-model="range" :input-props="dprops" :menu-props="mprops"
                                        displayFormat='MM/DD/YYYY' :presets="presets" :options="dateRangeOptions"
                                        @input="onDateRangeChange"></vdaterange>
                        </v-flex>
                        <v-flex xs12 sm12 md12>
                            <v-combobox deletable-chips prepend-icon="title" v-model="selectedKeywords" clearable
                                        :items="getkeywords" :search-input.sync="selectedKeywordsSearch" hide-selected
                                        label="Filter by Keywords" multiple small-chips>
                                <template slot="no-data">
                                    <v-list-item>
                                        <v-list-item-content>
                                            <v-list-item-title>No results matching "<strong>{{ selectedKeywordsSearch
                                                }}</strong>". Press <kbd>enter</kbd>
                                                to create a new one
                                            </v-list-item-title>
                                        </v-list-item-content>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                        </v-flex>
                    </v-layout>
                </v-card-text>
            </v-card>
        </v-menu>
        <v-btn icon class="hidden-sm-and-down" @click="togglephotodisplay">
            <v-icon color="rgba(0,0,0,.54)">{{photodisplay.icon}}</v-icon>
        </v-btn>
        <v-btn icon v-if="!isClientViewEnabled && !isHybrid"
        class="hidden-sm-and-down" @click="welcomedialog=true" title="Download Apps">
            <v-icon color="rgba(0,0,0,.54)">mdi-cellphone</v-icon>
        </v-btn>
        <div class="nav-block">
            <v-img :src="cLogo" contain max-height="32" max-width="78" :alt="cName"
                   style="display: inline-block"></v-img>

            <v-menu offset-y bottom style="max-width: 200px">
                <template v-slot:activator="{ on, attrs }">
                    <v-avatar color="purple" size="36" class="ml-2" v-bind="attrs" v-on="on">
                        <span class="white--text" style="font-size:18px;">{{initials}}</span>
                    </v-avatar>
                </template>
                <v-list>
                    <v-list-item to="/account">
                        <v-list-item-title>Settings</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="logout()">
                        <v-list-item-title>Log Off</v-list-item-title>
                    </v-list-item>
                </v-list>
            </v-menu>
        </div>
        <v-menu offset-y :close-on-content-click='false'>
            <template v-slot:activator="{ on }">
                <v-app-bar-nav-icon v-on="on" class="hidden-md-and-up">
                    <v-icon>more_vert</v-icon>
                </v-app-bar-nav-icon>
            </template>
            <v-card>
                <v-list dense>
                    <v-list-item class="pb-2">
                        <v-list-item-content>
                            <v-combobox class="dense-inputs" dense deletable-chips prepend-icon="account_balance" clearable
                                        v-model="gridSelect.selected" return-object item-text="mb_nickname"
                                        item-value="mb_id" hide-details :items="activebuildingsdata"
                                        label="Filter Photos By Sites" multiple small-chips></v-combobox>
                        </v-list-item-content>
                    </v-list-item>
                    <v-list-item class="pb-2">
                        <v-list-item-content>
                            <vdaterange class="dense-inputs" no-title v-model="range" :input-props="dpropsmobile"
                                        displayFormat='MM/DD/YYYY' :presets="presets" :options="dateRangeOptions"
                                        @input="onDateRangeChange"></vdaterange>
                        </v-list-item-content>
                    </v-list-item>
                    <v-list-item class="pb-2">
                        <v-list-item-content>
                            <v-combobox dense class="dense-inputs" hide-details deletable-chips prepend-icon="title"
                                        v-model="selectedKeywords" clearable :items="getkeywords"
                                        :search-input.sync="selectedKeywordsSearch" hide-selected
                                        label="Filter by Keywords" multiple small-chips>
                                <template slot="no-data">
                                    <v-list-item>
                                        <v-list-item-content>
                                            <v-list-item-title>No results matching "<strong>{{ selectedKeywordsSearch
                                                }}</strong>". Press <kbd>enter</kbd>
                                                to create a new one
                                            </v-list-item-title>
                                        </v-list-item-content>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                        </v-list-item-content>
<v-list-item v-if="isClientViewEnabled" class="pb-2">
                        <v-list-item-content>
                            <v-autocomplete dense class="dense-inputs" hide-details deletable-chips prepend-icon="mdi-account-group"
                                            v-model="selectedSubcontractors" clearable :items="subcontractorsData"
                                             item-text="name" item-value="id" return-object :loading="initialDataLoaded == false"
                                            label="Filter by Contractors" multiple small-chips>
                            </v-autocomplete>
                        </v-list-item-content>
                    </v-list-item>
                    </v-list-item>
                    <v-list-item @click="togglephotodisplay">
                        <v-list-item-action class="reduce-min-width">
                            <v-icon small color="rgba(0,0,0,.54)">{{photodisplay.icon}}</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                            <v-list-item-title>Toggle Layout</v-list-item-title>
                        </v-list-item-content>
                    </v-list-item>
                    <v-list-item @click="welcomedialog=true">
                        <v-list-item-action class="reduce-min-width">
                            <v-icon small color="rgba(0,0,0,.54)">mdi-cellphone</v-icon>
                        </v-list-item-action>
                        <v-list-item-content>
                            <v-list-item-title>Download Apps</v-list-item-title>
                        </v-list-item-content>
                    </v-list-item>
                </v-list>
            </v-card>
        </v-menu>
    </v-app-bar>
    <v-app-bar app fixed elevate-on-scroll clipped-left class="mainback" v-if="selectedPhotos.length>0"
               style="border-bottom:1px solid #e5e5e5;">
        <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
        {{selectedPhotos.length}}&nbsp; Item(s) are selected.
        <v-spacer></v-spacer>
        <v-toolbar-items class="hidden-md-and-down">
            <v-menu></v-menu>
            <v-btn text @click="openShareDialog" class="stopcaps navtext" style="color: rgba(0, 0, 0, 0.54);">
                <!--v-icon left dark style="margin-right:3px">reply_all</v-icon-->Share
            </v-btn>
            <v-menu bottom fixed nudge-bottom=64>
                <template v-slot:activator="{ on }">
                    <v-btn text v-on="on" class="stopcaps navtext" style="color: rgba(0, 0, 0, 0.54);">
                        <!--v-icon left dark style="margin-right:3px">print</v-icon-->Create Report
                        <v-icon right dark style="margin-left:3px">expand_more</v-icon>
                    </v-btn>
                </template>
                <v-list>
                    <v-list-item @click="print(2)">
                        <v-list-item-title>4 Per Page</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="print(4)">
                        <v-list-item-title>1 Per Page</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="printLocalTime()">
                                    <v-list-item-title>4 Per Page(Local Time)</v-list-item-title>
                                </v-list-item>
                </v-list>
            </v-menu>
            <v-btn text v-if="shouldShowAddMap" @click.native="openMap()" class="stopcaps navtext"
                   style="color: rgba(0, 0, 0, 0.54);">
                <!--v-icon left dark style="margin-right:3px">add_location</v-icon-->Add To Map
            </v-btn>
            <v-btn text @click.native="openTagDialog" class="stopcaps navtext" style="color: rgba(0, 0, 0, 0.54);">
                <!--v-icon left dark style="margin-right:3px">style</v-icon-->Add Tags
            </v-btn>
            <v-btn text @click.native="movedialog=true" class="stopcaps navtext" style="color: rgba(0, 0, 0, 0.54);">
                <!--v-icon left dark style="margin-right:3px">reply_all</v-icon-->Change Site
            </v-btn>
            <v-btn text @click.native="selectedPhotos = []" class="stopcaps navtext"
                   style="color: rgba(0, 0, 0, 0.54);">
                <!--v-icon left dark style="margin-right:3px">reply_all</v-icon-->Deselect All
            </v-btn>
        </v-toolbar-items>
        <v-menu offset-y :close-on-content-click='false'>
            <template v-slot:activator="{ on }">
                <v-app-bar-nav-icon v-on="on" class="hidden-lg-and-up">
                    <v-icon>more_vert</v-icon>
                </v-app-bar-nav-icon>
            </template>
            <v-card>
                <v-list dense>
                    <v-list-item>
                        <v-btn text @click="openShareDialog" class="stopcaps navtext">
                            <!--v-icon left dark style="margin-right:3px">reply_all</v-icon-->Share
                        </v-btn>
                    </v-list-item>
                    <v-list-item>
                        <v-menu bottom fixed nudge-bottom=64>
                            <template v-slot:activator="{ on }">
                                <v-btn text v-on="on" class="stopcaps navtext">Create Report
                                    <v-icon right dark style="margin-left:3px">expand_more</v-icon>
                                </v-btn>
                            </template>
                            <v-list>
                                <v-list-item @click="print(2)">
                                    <v-list-item-title>4 Per Page</v-list-item-title>
                                </v-list-item>
                                <v-list-item @click="print(4)">
                                    <v-list-item-title>1 Per Page</v-list-item-title>
                                </v-list-item>
                                <v-list-item @click="printLocalTime()">
                                    <v-list-item-title>4 Per Page(Local Time)</v-list-item-title>
                                </v-list-item>
                            </v-list>
                        </v-menu>
                    </v-list-item>
                    <v-list-item v-if="shouldShowAddMap">
                        <v-btn text @click.native="openMap()" class="stopcaps navtext">Add To Map</v-btn>
                    </v-list-item>
                    <v-list-item>
                        <v-btn text @click.native="openTagDialog" class="stopcaps navtext">Add Tags</v-btn>
                    </v-list-item>
                    <v-list-item>
                        <v-btn text @click.native="movedialog=true" class="stopcaps navtext">Change Site</v-btn>
                    </v-list-item>
                    <v-list-item>
                        <v-btn text @click.native="selectedPhotos = []" class="stopcaps navtext">Deselect All</v-btn>
                    </v-list-item>
                </v-list>
            </v-card>
        </v-menu>
    </v-app-bar>
    <!--v-card flat style="padding:25px; margin:0px 25px 0px 25px;"-->
    <v-card flat style="padding:25px; margin:0px; ">
        <v-container fluid v-bind:style="{padding: '0px', position:'relative'}">
            <transition name="fade-transition">
                <v-btn fab small fixed color="#497cb1" style="color:white" bottom right v-if="offsetTop>20"
                       @click.native="scrollUp">
                    <v-icon>keyboard_arrow_up</v-icon>
                </v-btn>
            </transition>
            <v-layout wrap>
                <v-flex xs12 sm3 md3></v-flex>
                <v-flex xs12 sm9 md9></v-flex>
            </v-layout>
            <v-container grid-list-md text-xs-center fluid style="padding:4px 0px;">
                <v-layout v-if="initialpicsloaded==true && (rawphotos.length>0 || filteredphotos.length>0)" row wrap>
                    <v-flex v-for="(item,index) in photospaged" v-if="photospaged.length>0" xs6 sm4 md3 lg3 xl2
                            :key="index" :class="'page' + Math.floor(index/48)">
                        <v-hover v-if="typeof item!= 'undefined'">
                            <v-card class="elevation-3 eachphoto" slot-scope="{ hover }" style="border-radius: 24px;"
                                    ref="poo">
                                <v-img v-longtap="()=>selectPhoto(index, item.mvp_id,...arguments)" class="galleryimage"
                                       :src="getDisplayImageUrl(item)" :height="photodisplay.height"
                                       :aspect-ratio="photodisplay.aspect" @click="openDialog(index)">
                                    <v-checkbox class="photocheckbox" on-icon="" off-icon=""
                                                :input-value="isPhotoSelected(index, item.mvp_id)" dark
                                                v-if="hover || isPhotoSelected(index, item.mvp_id)"
                                                v-on:click.stop.prevent="selectPhoto(index, item.mvp_id,...arguments)"
                                                color="white"></v-checkbox>
                                    <template v-if="item.mvp_vendor_id!=getVtem">
                                        <div class="body-1 white--text"
                                             style="position:absolute;top:0px;left:0px;margin-left:10px">
                                            {{getCompanyName(item.mvp_id)}}
                                        </div>
                                    </template>
                                    <div class="caption white--text"
                                         style="position:absolute;bottom:0px;right:0px;margin-right:10px">
                                        {{getFormatedDate(item.mvp_dt)}}
                                    </div>
                                </v-img>
                                <v-card-text v-if="initialpicsloaded==true && photospaged.length>0">
                                    <div class="subheading font-weight-bold text-truncate text-center"
                                         style="color:#A92733; cursor: pointer;"
                                         @click="buildingclick(item.mvp_building_id)">{{isClientViewEnabled ? getClientBuildingName(item) : item.mb_nickname}}
                                    </div>
                                    <div v-if="item.mb_nickname=='' || item.mb_nickname==null"
                                         class="subheading font-weight-bold text-truncate text-center deep-orange--text"
                                         style="">&nbsp;
                                    </div>
                                    <div class="caption text-truncate text-sm-left" style="line-height:2!important;">
                                        &nbsp;{{item.mvp_disc}}
                                    </div>
                                    <template v-if="item.mvp_disc!= null">
                                        <div v-if="item.mvp_disc.trim()==''"
                                             class="caption text-truncate text-sm-left"></div>
                                    </template>
                                </v-card-text>
                            </v-card>
                        </v-hover>
                    </v-flex>
                    <mugen-scroll :handler="getStations" :should-handle="!loading"></mugen-scroll>
                </v-layout>
                <v-layout v-else-if="initialpicsloaded==true">
                    <v-flex xs12 sm12 md12 lg12 xl12>
                        <v-card>
                            <v-toolbar dense dark color="primary">
                                <v-toolbar-title>Send a link. Get the app.</v-toolbar-title>
                                <v-spacer></v-spacer>
                            </v-toolbar>
                            <v-card-text>
                                <v-container grid-list-md>
                                    <v-layout wrap>
                                        <v-flex xs12 sm12 md7>
                                            <v-img max-height=400 contain
                                                   src="/images/screenshotofappon2phones.png"></v-img>
                                        </v-flex>
                                        <v-flex xs12 sm12 md5>
                                            <v-text-field outlined dense hide-details v-model="email" label="Your Email Address"></v-text-field>
                                            <v-btn depressed color="primary" @click="emaillink">Email app</v-btn>
                                            <div class="headline" style="margin-top:10px;margin-bottom:5px">OR</div>
                                            <v-text-field outlined dense hide-details v-model="mobile" label="Your Mobile Number"></v-text-field>
                                            <v-btn depressed color="primary" @click="textApp">Text app</v-btn>
                                        </v-flex>
                                    </v-layout>
                                </v-container>
                            </v-card-text>
                        </v-card>
                    </v-flex>
                </v-layout>
                <v-dialog v-model="welcomedialog" v-if="welcomedialog" :fullscreen="$vuetify.breakpoint.smAndDown"
                          :hide-overlay="$vuetify.breakpoint.smAndDown" scrollable width="60vw"
                          transition="dialog-bottom-transition">
                    <v-card>
                        <v-toolbar class="elevation-0 white">
                            <v-toolbar-title>
                                <h3 class="headline mb-0">Send a link. Get the app.</h3>
                            </v-toolbar-title>
                            <v-spacer></v-spacer>
                            <v-toolbar-items></v-toolbar-items>
                            <v-btn icon @click="welcomedialog=false">
                                <v-icon>close</v-icon>
                            </v-btn>
                        </v-toolbar>
                        <v-card-text>
                            <v-container grid-list-md>
                                <v-layout wrap>
                                    <v-flex xs12 sm12 md7>
                                        <v-img max-height=400 contain
                                               src="/images/screenshotofappon2phones.png"></v-img>
                                    </v-flex>
                                    <v-flex xs12 sm12 md5>
                                        <v-text-field outlined class='mb-2' hide-details dense v-model="email" label="Your Email Address"></v-text-field>
                                        <v-btn depressed color="primary" @click="emaillink">Email app</v-btn>
                                        <div class="headline" style="margin-top:10px;margin-bottom:5px">OR</div>
                                        <v-text-field outlined class='mb-2' hide-details dense v-model="mobile" label="Your Mobile Number"></v-text-field>
                                        <v-btn depressed color="primary" @click="textApp">Text app</v-btn>
                                    </v-flex>
                                </v-layout>
                            </v-container>
                        </v-card-text>
                    </v-card>
                </v-dialog>
                <v-dialog v-model="dialog" v-if="dialog && initialpicsloaded==true && photospaged.length>0"
                          :fullscreen="$vuetify.breakpoint.smAndDown" :hide-overlay="$vuetify.breakpoint.smAndDown"
                          scrollable width="60vw" transition="dialog-bottom-transition">
                    <v-card>
                        <!--v-toolbar dark color="primary" v-if="$vuetify.breakpoint.smAndDown"-->
                        
                        <v-card-title>
                            <v-btn color="primary" icon @click="close"><v-icon>close</v-icon></v-btn>
                            Photo
                        </v-card-title>
                        <v-card-text>
                            <v-container grid-list-md fluid style="padding-top:0px;">
                                <v-layout wrap>
                                    <v-flex xs12 sm12 md7>
                                        <v-carousel style="" :cycle="false" v-model="carouselIndex" hide-delimiters
                                                    v-if="initialpicsloaded==true && photospaged.length>0">
                                            <v-carousel-item v-for="(item,i) in photospaged" :key="i"
                                                      :src="getDisplayImageUrl(item)"
                                                             style="background-position: top center;">
                                                <template v-if="item.mvp_hrimage!= null">
                                                    <template v-if="item.mvp_hrimage.trim()!==''">
                                                      <v-btn color="indigo"
                                                               @click="openfullresolution(item.mvp_hrimage)">View
                                                            Full-Resolution
                                                      </v-btn>
                                                    </template>
                                                </template>
                                                <div class="photo-rotation-controls"
                                                        v-if="item && item.mvp_vendor_id == getVtem && item.mvp_ruler == 0"
                                                        style="position: absolute; bottom: 0px; width: 100%; justify-content: center; z-index: 10; display: flex;  padding: 8px; border-radius: 8px;">
                                                      <v-btn fab small color="primary" class="mr-2" @click="rotatePhoto(carouselIndex, -90)" title="Rotate Left">
                                                        <v-icon>rotate_left</v-icon>
                                                      </v-btn>
                                                      <v-btn fab small color="primary" @click="rotatePhoto(carouselIndex,90)" title="Rotate Right">
                                                        <v-icon>rotate_right</v-icon>
                                                      </v-btn>
                                                    </div>
                                                  </v-carousel-item>
                                        </v-carousel>
                                    </v-flex>
                                    <v-flex xs12 sm12 md5 v-if="initialpicsloaded==true && photospaged.length>0 && photospaged[carouselIndex]">
                                        <v-row>
                                            <v-col md="12" cols="12" class='pa-0 pb-2'>
                                                <v-autocomplete
                                                :key="carouselIndex" class='mt-2'
                                                outlined dense hide-details
                                                        :menu-props="{ bottom: true, maxHeight: 200, maxWidth: 300 }"
                                                        return-object v-model="selectedbuilding" :loading="selectedBuildingLoading"
                                                        :items="courselselectbuilditems" label="Sites"
                                                        item-text="buildingname"
                                                        item-value="buildingid"></v-autocomplete>
                                            </v-col>
                                            <v-col md="12" cols="12" class='pa-0 pb-2'>
                                                <template v-if="photospaged[carouselIndex].mvp_vendor_id==getVtem">
                                                    <v-textarea outlined dense class='white-input mt-2' hide-details filled ref="photodesc" rows=3 name="input-7-1"
                                                                label="Enter Photo Description"
                                                                v-model="photodesc"></v-textarea>
                                                </template>
                                                <template v-else>
                                                    <v-textarea outlined hide-details class='white-input' filled ref="photodesc" rows=3 disabled name="input-7-1"
                                                                label="Photo Description"
                                                                v-model="photodesc"></v-textarea>
                                                </template>
                                           </v-col>
                                             <v-col md="12" cols="12" class='pa-0 pb-2 mt-1'><span class="primary-font-size2">Taken On: &nbsp</span><span
                                                    class="primary-font-size1">{{getFormatedDate(photospaged[carouselIndex].mvp_dt)}}</span>
                                           </v-col>
                                            <v-col md="12" cols="12" class='pa-0 pb-3' v-if="!isClientViewEnabled && !isHybrid"><span class="primary-font-size2" >Taken By: &nbsp</span><span
                                                    class="primary-font-size1"
                                                    style="-webkit-user-select: text; -moz-user-select: text;     -ms-user-select: text; user-select: text;">{{photospaged[carouselIndex].mvp_uploader_email}}</span>
                                            </v-col>
                                           <v-col md="12" cols="12" class='pa-0 pb-2'>
                                                <template v-if="photospaged[carouselIndex].mvp_vendor_id==getVtem">
                                                    <v-textarea class='mb-2 white-input' outlined dense hide-details filled ref="phototags" rows=3 name="input-8-1"
                                                                label="Enter Photo Tags"
                                                                v-model="phototags"></v-textarea>
                                                </template>
                                                <template v-else>
                                                    <v-textarea outlined dense hide-details filled ref="phototags" rows=3 name="input-8-1" disabled
                                                                label="Photo Tags" v-model="phototags"></v-textarea>
                                                </template>
                                                <button type="button" class="v-btn v-btn--icon theme--light"
                                                        @click="openPinterest">
                                                    <div class="v-btn__content"><i class="fa fa-pinterest fa-3x"
                                                                                   style="color:#E60023;"></i>
                                                    </div>
                                                </button>
                                                <button type="button" class="ml-2 v-btn v-btn--icon theme--light"
                                                        @click="openFacebookWindow">
                                                    <div class="v-btn__content"><i class="fa fa-facebook fa-3x"
                                                                                   style="color:#3b5998;"></i>
                                                    </div>
                                                </button>
                                                <button type="button" class="ml-2 v-btn v-btn--icon theme--light"
                                                        @click="openLinkedinWindow">
                                                    <div class="v-btn__content"><i class="fa fa-linkedin fa-3x"
                                                                                   style="color:#0077B5;"></i>
                                                    </div>
                                                </button>
                                                <button type="button" class="ml-2 v-btn v-btn--icon theme--light" @click="downloadPhoto" title="Download Image">
                        <div class="v-btn__content"><i class="fa fa-download fa-3x" style="color:#0077B5;"></i></div>
                      </button>
                                            </v-col>
                                           <v-col md="12" cols="12" class='pa-0 pb-2'>
                                                <v-spacer></v-spacer>
                                            </v-col>
                                        </v-row>
                                    </v-flex>
                                </v-layout>
                            </v-container>
                        </v-card-text>
                        <v-card-actions v-if="initialpicsloaded==true && photospaged.length>0">
                            <v-spacer></v-spacer>
                            <!--v-btn color="blue darken-1" flat @click.native="close">Close</v-btn-->
                            <template v-if="photospaged[carouselIndex].mvp_vendor_id==getVtem">
                                <v-btn text outlined color="error" @click.native="deletePhoto" v-if="pagePermissionDelete">Delete</v-btn>
                                <v-btn color="primary" depressed @click.native="savephoto" v-if="pagePermissionEdit" :loading="isSaving" :disabled="isSaving">Save</v-btn>
                            </template>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <v-dialog v-model="sharedialog" v-if="sharedialog" max-width="500px">
                    <v-card>
                        <v-card-title>
                            <h3 class="headline mb-0">Share</h3>
                        </v-card-title>
                        <v-card-text>
                            <v-container grid-list-md>
                                <v-layout wrap>
                                    <v-flex xs12 sm12 md12>
                                        <v-text-field outlined dense hide-details disabled v-model="sharedialogdata.alreadyshared"
                                                      label="Who has access"></v-text-field>
                                        <v-combobox outlined dense hide-details class='pt-2' :menu-props="{ bottom: true, maxHeight: 200 }"
                                                    v-model="sharedialogdata.model" :items="sharedialogdata.items"
                                                    :search-input.sync="sharedialogdata.search" hide-selected
                                                    label="Share with others" multiple small-chips>
                                            <template slot="no-data">
                                                <v-list-item>
                                                    <v-list-item-content>
                                                        <v-list-item-title>No results matching "<strong>{{
                                                            sharedialogdata.search }}</strong>". Press <kbd>enter</kbd>
                                                            to create a new one
                                                        </v-list-item-title>
                                                    </v-list-item-content>
                                                </v-list-item>
                                            </template>
                                        </v-combobox>
                                    </v-flex>
                                </v-layout>
                            </v-container>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn color="blue darken-1" text @click.native="sharedialog=false">Cancel</v-btn>
                            <v-btn color="primary" @click.native="saveshare">Save</v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <v-dialog v-if="movedialog" v-model="movedialog" max-width="500px">
                    <v-card>
                        <v-card-title>
                            <h3 class="headline mb-0">Choose the site for the selected photos</h3>
                        </v-card-title>
                        <v-card-text>
                            <v-container grid-list-md>
                                <v-layout wrap>
                                    <v-flex xs12 sm12 md12>
                                        <v-autocomplete outlined dense hide-details :menu-props="{ bottom: true, maxHeight: 200 }"
                                                  v-model="selectbuilding" :items="activebuildingsdata"
                                                  item-text="mb_nickname" item-value="mb_id"
                                                  label="Choose a site"></v-autocomplete>
                                    </v-flex>
                                </v-layout>
                            </v-container>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn color="blue darken-1" text @click.native="movedialog=false">Cancel</v-btn>
                            <v-btn color="primary" @click.native="pMove">Submit</v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <v-dialog v-if="mapselectDialog" v-model="mapselectDialog" max-width="500px">
                <v-card>
                    <v-card-title>
                        <h3 class="headline mb-0">Choose the map for the selected photos</h3>
                    </v-card-title>
                    <v-card-text>
                        <v-container grid-list-md>
                            <v-layout wrap>
                                <v-flex xs12 sm12 md12>
                                    <v-autocomplete outlined dense hide-details :menu-props="{ bottom: true, maxHeight: 200 }"
                                              v-model="mapSelection" :items="mapSelections"
                                              item-text="sml_layer_name" item-value="sml_id"
                                              label="Choose a Map"></v-autocomplete>
                                </v-flex>
                            </v-layout>
                        </v-container>
                    </v-card-text>
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="blue darken-1" text @click.native="mapselectDialog=false">Cancel</v-btn>
                        <v-btn color="primary" text @click.native="startMap">Submit</v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>
                <v-dialog v-if="tagdialog" v-model="tagdialog" max-width="500px">
                    <v-card>
                        <v-card-title>
                            <h3 class="headline mb-0">Add Tags for Sorting and Grouping</h3>
                        </v-card-title>
                        <v-card-text>
                            <v-container grid-list-md>
                                <v-layout wrap>
                                    <v-flex xs12 sm12 md12>
                                        <v-combobox outlined dense hide-details :menu-props="{ bottom: true, maxHeight: 200 }"
                                                    v-model="tagdialogdata.model" :items="tagdialogdata.items"
                                                    :search-input.sync="tagdialogdata.search" hide-selected
                                                    label="Type tags here or select from dropdown list" multiple
                                                    small-chips>
                                            <template slot="no-data">
                                                <v-list-item>
                                                    <v-list-item-content>
                                                        <v-list-item-title>No results matching "<strong>{{
                                                            tagdialogdata.search }}</strong>". Press <kbd>enter</kbd>
                                                            to create a new one
                                                        </v-list-item-title>
                                                    </v-list-item-content>
                                                </v-list-item>
                                            </template>
                                        </v-combobox>
                                    </v-flex>
                                </v-layout>
                            </v-container>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn color="blue darken-1" text @click.native="tagdialog=false">Cancel</v-btn>
                            <v-btn color="primary" @click.native="savetags">Save</v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <v-dialog v-model="loadingdialog" persistent transition=false fullscreen
                          content-class="loading-dialog">
                    <v-container fill-height>
                        <v-layout row justify-center align-center>
                            <v-progress-circular indeterminate :size="70" :width="7"
                                                 color="purple"></v-progress-circular>
                        </v-layout>
                    </v-container>
                </v-dialog>
                <v-dialog v-model="searchdialog" persistent fullscreen content-class="loading-dialog">
                    <v-container fill-height>
                        <v-layout row justify-center align-center>
                            <v-progress-circular indeterminate :size="70" :width="7"
                                                 color="purple"></v-progress-circular>
                        </v-layout>
                    </v-container>
                </v-dialog>
                <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
                            :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">{{
                    snackbar.text }}
                    <v-btn color="pink" text @click="snackbar.snackbar = false">Close</v-btn>
                </v-snackbar>
            </v-container>
        </v-container>
    </v-card>
</div>`


export default {
    template: html,

    components: {
        photoDateRangePicker,
        vdaterange,
        TabulatorDatatable,
        DropdownMultipleDatatableHeader,
        DropdownSimpleDatatableHeader
    },
    async mounted() {
        const headers = {
            'Content-Type': 'application/json'
        };

        const payload = {
            accessCode: this.$store.getters.getAccessCode,
            initial: 1,
            limit: 200
        };

        try {
            let response = await fetch(globalThis.myBaseURL + '/node/photos', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(payload)
            });
            let data = await response.json();
            Vue.set(this, 'rawphotos', data);

            let limit = 2000;
            let current = Math.round(new Date().getTime() / 1000);
            let past = current - 604800; // 7 Days

            const payload2 = {
                accessCode: this.$store.getters.getAccessCode,
                limit: limit,
                ...(myrecentcount < 1000 ? {} : { enddate: current, startdate: past })
            };

            response = await fetch(globalThis.myBaseURL + '/node/photos', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(payload2)
            });
            let data2 = await response.json();
            Vue.set(this, 'rawphotos', data2);
            this.rawphotos.sort((a, b) => parseInt(b.mvp_dt) - parseInt(a.mvp_dt));

        } catch (e) {
            EventBus.$emit('AjaxError', e);
        }



    },

    updated() {

        window.refs = {}
        window.refs.topblock = this.$refs.topblock
        window.refs.bottomblock = this.$refs.bottomblock
    },
    directives: {
        longtap: {
            isLiteral: true,

            bind: (el, binding, vNode) => {
                // Make sure expression provided is a function

                if (typeof binding.value !== 'function') {
                    // Fetch name of component
                    const compName = vNode.context.name
                    // pass warning to console
                    let warn = `[longpress:] provided expression '${binding.expression}' is not a function, but has to be`
                    if (compName) {
                        warn += `Found in component '${compName}' `
                    }

                    console.warn(warn)
                }

                // Define variable
                let pressTimer = null

                
                const handler = (e) => {
                    binding.value(e)
                }

                // Define funtion handlers
                // Create timeout ( run function after 1s )
                let start = (e) => {

                    if (e.type === 'click' && e.button !== 0) {
                        return;
                    }

                    if (pressTimer === null) {
                        pressTimer = setTimeout(() => {
                            // Run function
                            handler()
                        }, 1000)
                    }
                }

                // Cancel Timeout
                let cancel = (e) => {
                    // Check if timer has a value or not

                    if (pressTimer !== null) {
                        clearTimeout(pressTimer)
                        pressTimer = null
                    }
                }


                // Add Event listeners

                el.addEventListener("touchstart", start, {
                    passive: true
                });
                // Cancel timeouts if this events happen

                el.addEventListener("touchend", cancel, {
                    passive: true
                });
                el.addEventListener("touchcancel", cancel, {
                    passive: true
                });
            }
        }
    },

    data: function () {
        return {
            pageId:1,
            mapSelections: [],
            mapImgs: [],
            mapSelection: null,
            mapselectDialog: false,
            sitesDtSearch: '',
            selectedBuildingLoading: false,
            gridSelect: {
                dialog: false,
                loading: false,
                selected: [],
                tabulatorConfig: {
                    selectableRows: true,
                    selectableRowsRollingSelection: true,
                    placeholder: "No Data Available",
                    placeholderHeaderFilter: "No Matching Data",
                    reactiveData: false,
                    height: "50vh",
                    layout: "fitColumns",
                    pagination: true,
                    paginationMode: "local",
                    paginationSize: 100,
                    paginationSizeSelector: [100, 250, 500, 1000],
                    paginationCounter: "rows",
                    filterMode: "local",
                    index: "mb_id",
                    sortMode: "local",
                    columns: [
                        {
                            formatter: "rowSelection",
                            titleFormatter: "rowSelection",
                            hozAlign: "center",
                            headerSort: false,
                            width: 50,
                        },
                        {
                            title: "Site Name",
                            field: "mb_nickname",
                            headerFilter: "input",
                            headerHozAlign: "center",
                            minWidth: 200,
                            headerFilterParams: { clearable: true },
                        },
                        {
                            title: "Address",
                            field: "mb_address1",
                            headerFilter: "input",
                            headerHozAlign: "center",
                            minWidth: 200,
                            headerFilterParams: { clearable: true },
                        },
                        {
                            title: "City",
                            field: "cityname",
                            headerFilter: "list",
                            headerHozAlign: "center",
                            minWidth: 150,
                            headerFilterParams: {
                                valuesLookup: true,
                                multiselect: true,
                                clearable: true
                            },
                        },
                        {
                            title: "State",
                            field: "statename",
                            headerFilter: "list",
                            headerHozAlign: "center",
                            minWidth: 100,
                            headerFilterParams: {
                                valuesLookup: true,
                                multiselect: true,
                                clearable: true
                            },
                        },
                        {
                            title: "Zip",
                            field: "mb_zip_code",
                            headerFilter: "input",
                            headerHozAlign: "center",
                            minWidth: 100,
                            headerFilterParams: { clearable: true },
                        },
                    ],
                },
            },
            page: 1,
            dialog: false,
            sharedialog: false,
            tagdialog: false,
            busy: false,
            phototags: "",
            movedialog: false,
            rawphotos: [],
            filteredphotos: [],
            filters: false,
            photodesc: "",
            snackbar: {
                snackbar: false,
                y: 'bottom',
                x: 'left',
                mode: '',
                timeout: 2000,
                text: ''
            },
            welcomedialog: false,
            photodisplay: {
                height: 200,
                aspect: 1.778,
                icon: "mdi-view-grid",
                divstyle: "height:274px"
            },
            courselselectbuilditems: [],
            selectedbuilding: {},
            selectedKeywords: [],
            selectedKeywordsSearch: null,
            selectedSubcontractors: [],
            loadingdialog: true,
            loading: false,
            selectbuilding: null,
            searchdialog: false,
            email: "",
            mobile: "",
            sharedialogdata: {
                items: [],
                model: [],
                search: null,
                alreadyshared: ""
            },
            recompute: false,
            sharedialogdataempty: {
                items: [],
                model: [],
                search: null,
                alreadyshared: ""
            },
            dprops: {
                clearable: true
            },
            dpropsmobile: {

                prependIcon: 'event',
                clearable: true,
                hideDetails: true,
            },
            mprops: {
                fullWidth: true
            },
            tagdialogdata: {
                items: [],
                model: [],
                search: null,
            },
            tagdialogdataempty: {
                items: [],
                model: [],
                search: null,
            },
            tempphotos: [],
            offsetTop: 0,
            carouselIndex: 0,
            selectedPhotos: [],

            allpicsloaded: false,
            range: {},
            menu: null,
            searchmenu: null,
            presets: [{
                label: 'Today',
                range: [
                    dateFns.format(new Date(), 'YYYY-MM-DD'),
                    dateFns.format(new Date(), 'YYYY-MM-DD'),
                ],
            },
            {
                label: 'Yesterday',
                range: [
                    dateFns.format(dateFns.subDays(new Date(), 1), 'YYYY-MM-DD'),
                    dateFns.format(dateFns.subDays(new Date(), 1), 'YYYY-MM-DD'),
                ],
            },
            {
                label: 'Last Week',
                range: [
                    dateFns.format(dateFns.subDays(new Date(), 7), 'YYYY-MM-DD'),
                    dateFns.format(new Date(), 'YYYY-MM-DD'),
                ],
            },
            {
                label: 'Last 30 Days',
                range: [
                    dateFns.format(dateFns.subDays(new Date(), 30), 'YYYY-MM-DD'),
                    dateFns.format(new Date(), 'YYYY-MM-DD'),
                ],
            },
            ],
            dateRangeOptions: {
                start: 'Start Date',
                end: 'End Date',
                startDate: dateFns.format(new Date(), 'YYYY-MM-DD'),
                endDate: dateFns.format(new Date(), 'YYYY-MM-DD'),

                format: 'MM/DD/YYYY'

            },
            currentRotation: 0,
            isSaving: false,
            currentPhotoMvpId: null, 
           
        };
    },

    methods: {
       
getDisplayImageUrl(item) {
            if (!item || !item.mvp_image) {
                return ''; 
            }

          
            if (item.mvp_ruler == 1) {
                return this.getRulerImage(item.mvp_image);
            }

           
            if (item.mvp_image_rotated) {
                return item.mvp_image_rotated;
            }

            let imageUrl = item.mvp_image;
            
            if (item.mvp_rotated && imageUrl && !imageUrl.includes('?v=') && !imageUrl.includes('?cb=') && !imageUrl.includes('?date=')) {
                imageUrl += `?cb=${Date.now()}`;
            }
            return imageUrl;
        },
        refreshSites() {

            this.$nextTick(() => {
                if (this.$refs.siteSelectionTabulator && this.$refs.siteSelectionTabulator.tabulator) {


                    this.$refs.siteSelectionTabulator.tabulator.setData(this.activebuildingsdata);
                    if (this.gridSelect.selected && this.gridSelect.selected.length > 0) {

                        let ids = this.gridSelect.selected.map(a => a.mb_id);
                        this.$refs.siteSelectionTabulator.tabulator.selectRow(ids);
                    }


                }
            });
        },

        selectSites() {
            if (this.$refs.siteSelectionTabulator) {
                const selectedRows = this.$refs.siteSelectionTabulator.tabulator.getSelectedData();
                this.gridSelect.selected = selectedRows;
                this.gridSelect.dialog = false;

            }
        },

        openSiteSelectionDialog() {
            this.gridSelect.dialog = true;
            this.$nextTick(() => {
                this.refreshSites();
            });
        },
        getClientBuildingName(item) {
            return this.buildingsdata.find(a => a.sscm_grouped_sites.split(",").includes(item.mvp_building_id.toString()))?.mb_nickname;
        },
        async downloadPhoto(event) {
            var imgid = this.photospaged[this.carouselIndex].mvp_id;

            if (!this.filters) {
                var photodetail = _.find(this.rawphotos, {
                    'mvp_id': imgid
                })
            } else {
                var photodetail = _.find(this.filteredphotos, {
                    'mvp_id': imgid
                })
            }
            var imageurl = globalThis.myBaseURL + '/vpics/corsprofileimage?url=' + photodetail.mvp_image;
            const a = document.createElement("a");
            fetch(imageurl).then((response) => {
                return response.blob();

            }).then(blob => {
                a.href = URL.createObjectURL(blob);

                a.download = photodetail.mvp_image.substring(photodetail.mvp_image.lastIndexOf('/') + 1);
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            });




        },
        getRulerImage(url) {
            let l = url.substring(url.lastIndexOf('/') + 1);
            return "https://customer.ewr1.vultrobjects.com/" + l.substring(0, l.lastIndexOf(".")) + ".png";

        },
        parseNick() {
            if (this.gridSelect.selected.length === 1) {
                let item = this.gridSelect.selected[0];
                return item.mb_nickname.length > 10 ? item.mb_nickname.substring(0, 10) + "..." : item.mb_nickname;
            }
            else if (this.gridSelect.selected.length > 1) {
                let item = this.gridSelect.selected[0];
                return `${item.mb_nickname.length > 10 ? item.mb_nickname.substring(0, 10) + "..." : item.mb_nickname} (+${this.gridSelect.selected.length - 1} others)`;
            }
        },
        photosDone() {
            this.gridSelect.selected = this.gridSelect.dtSelected;
            this.gridSelect.dialog = false;
        },
        filterSites() {
            let k = this.gridSelect.showSelected ? this.gridSelect.selected : this.activebuildingsdata;
            if (this.gridSelect.filter.site)
                if (this.gridSelect.filter.site.length > 0) {
                    k = k.filter(item => item.mb_nickname.toLowerCase().includes(this.gridSelect.filter.site.toLowerCase()));
                }
            if (this.gridSelect.filter.address)
                if (this.gridSelect.filter.address.length > 0) {
                    k = k.filter(item => item.mb_address1.toLowerCase().includes(this.gridSelect.filter.address.toLowerCase()));
                }
            if (this.gridSelect.filter.city)
                if (this.gridSelect.filter.city.length > 0) {
                    k = k.filter(item => (item.cityname !== null) && item.cityname.toLowerCase().includes(this.gridSelect.filter.city.toLowerCase()));
                }
            if (this.gridSelect.filter.state)
                if (this.gridSelect.filter.state.length > 0) {
                    k = k.filter(item => (item.statename !== null) && item.statename.toLowerCase().includes(this.gridSelect.filter.state.toLowerCase()));
                }
            if (this.gridSelect.filter.zone)
                if (this.gridSelect.filter.zone.length > 0) {
                    k = k.filter(item => (item.mb_zone !== null) && item.mb_zone.toLowerCase().includes(this.gridSelect.filter.zone.toLowerCase()));
                }
            if (this.gridSelect.filter.customername)
                if (this.gridSelect.filter.customername.length > 0) {
                    k = k.filter(item => item.customername != undefined && item.customername.toLowerCase().includes(this.gridSelect.filter.customername.toLowerCase()));
                }
            return k;

        },
        logout() {
            window.location = globalThis.myBaseURL + '/index/logout'
        },
        async emaillink() {
            var email_address = this.email;
            try {
                const formBody = new URLSearchParams();
                formBody.append("email_address", email_address);
                formBody.append("vid", myvid);
                formBody.append("companyname", mycompanyname);

                let response = await fetch(globalThis.myBaseURL + "/vpics/emailapplink", {
                    method: 'POST',
                    body: formBody,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                if (response.ok) {
                    this.appdialog = false;
                    this.email = "";
                    this.snackbar.text = 'Email has been sent to ' + email_address
                    this.snackbar.snackbar = true;
                } else {

                    this.snackbar.text = "Error. Are you connected to internet?";
                    this.snackbar.snackbar = true;
                }

            } catch (err) {
                this.snackbar.text = "Error. Are you connected to internet?";
                this.snackbar.snackbar = true;
            }


        },
        async textApp() {
            var cell = this.mobile;
            try {
                const formBody = new URLSearchParams();
                formBody.append("cell", cell);
                formBody.append("vid", myvid);


                let response = await fetch(globalThis.myBaseURL + "/vpics/sendtextmsg", {
                    method: 'POST',
                    body: formBody,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                if (response.ok) {
                    this.appdialog = false;
                    this.mobile = "";
                    this.snackbar.text = 'Text has been sent to ' + cell
                    this.snackbar.snackbar = true;
                } else {
                    this.snackbar.text = "Error. Are you connected to internet?";
                    this.snackbar.snackbar = true;
                }

            } catch (err) {
                this.snackbar.text = "Error. Are you connected to internet?";
                this.snackbar.snackbar = true;
            }

        },
        pMove: async function () {
            var imgs = new Array();
            for (var i = 0; i < this.selectedPhotos.length; i++) {
                var imgid = this.selectedPhotos[i];
                imgs.push(imgid);
            }
            var self = this;
            var picIDs = imgs.join();
            var buildingdetail = _.find(this.buildingsdata, {
                'mb_id': this.selectbuilding
            })

            try {
                const formBody = new URLSearchParams();
                formBody.append("picIDs", picIDs);
                formBody.append("bid", this.selectbuilding);


                let response = await fetch(globalThis.myBaseURL + "/vpics/updatephotobuilding", {
                    method: 'POST',
                    body: formBody,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                if (response.ok) {
                    self.movedialog = false;

                    var payload = {
                        picIDs: imgs,
                        bid: this.selectbuilding
                    }
                    //this.$store.commit('photoMove', payload);
                    if (!self.filters) {
                        for (var i = 0; i < payload.picIDs.length; i++) {
                            var k = self.rawphotos.findIndex(x => x.mvp_id == payload.picIDs[i]);
                            Vue.set(self.rawphotos[k], "mvp_building_id", payload.bid);
                            Vue.set(self.rawphotos[k], "mb_nickname", buildingdetail.mb_nickname);

                        }
                    } else {
                        for (var i = 0; i < payload.picIDs.length; i++) {
                            var k = self.filteredphotos.findIndex(x => x.mvp_id == payload.picIDs[i]);
                            Vue.set(self.filteredphotos[k], "mvp_building_id", payload.bid);
                            Vue.set(self.filteredphotos[k], "mb_nickname", buildingdetail.mb_nickname);
                        }
                    }
                    self.snackbar.text = "Photos have been moved."
                    self.snackbar.snackbar = true;
                    self.selectbuilding = null;
                } else {
                    this.snackbar.text = "Error. Are you connected to internet?";
                    this.snackbar.snackbar = true;
                }

            } catch (err) {

                this.snackbar.text = "Error. Are you connected to internet?";
                this.snackbar.snackbar = true;
            }


        },
        isOnScreen: function ($allWhiteSections) {
            // if the element doesn't exist, abort
            var $whiteSpaceMatchingExpression = $allWhiteSections.filter(function () {
                var $this = $(this);
                var top_of_element = $this.offset().top;
                var bottom_of_element = $this.offset().top + $this.outerHeight();
                var bottom_of_screen = $(window).scrollTop() + $(window).height();
                var top_of_screen = $(window).scrollTop();

                return ((bottom_of_screen > top_of_element) && (top_of_screen < bottom_of_element));
            });

            if ($whiteSpaceMatchingExpression.length) {
                return true;
            } else {
                return false;
            }
        },
        getStations() {
            this.loading = true
            this.offsetTop = window.pageYOffset || document.documentElement.scrollTop
            this.page++;

            this.loading = false
        },

        openPinterest: function (event) {

            var imgid = this.photospaged[this.carouselIndex].mvp_id;
            if (!this.filters) {
                var photodetail = _.find(this.rawphotos, {
                    'mvp_id': imgid
                })
            } else {
                var photodetail = _.find(this.filteredphotos, {
                    'mvp_id': imgid
                })
            }

            var sharemessage = photodetail.mvp_disc;
            var imageurl = photodetail.mvp_image;
            let url = "http://pinterest.com/pin/create/bookmarklet/?media=" + decodeURI(imageurl) +
                ((sharemessage) ? "&description=" + sharemessage : "") + "&is_video=false&url=" + decodeURI(globalThis.myBaseURL);
            window.open(url, '_blank', "toolbar=yes,scrollbars=yes,resizable=yes,top=200,left=200,width=600,height=400");


        },
        openFacebookWindow: function (event) {


            var imgid = this.photospaged[this.carouselIndex].mvp_id;
            if (!this.filters) {
                var photodetail = _.find(this.rawphotos, {
                    'mvp_id': imgid
                })
            } else {
                var photodetail = _.find(this.filteredphotos, {
                    'mvp_id': imgid
                })
            }

            var sharemessage = photodetail.mvp_disc;
            var imageurl = photodetail.mvp_image;
            let url = "https://www.facebook.com/sharer/sharer.php?s=100&p[title]=Sitefotos Photo Share" +
                ((sharemessage) ? "&p[title]=" + decodeURI(sharemessage) : "") +
                "&p[url]=" + decodeURI(imageurl)
            window.open(url, '_blank', "toolbar=yes,scrollbars=yes,resizable=yes,top=200,left=200,width=600,height=400");


        },
        openLinkedinWindow: function (event) {

            var imgid = this.photospaged[this.carouselIndex].mvp_id;

            if (!this.filters) {
                var photodetail = _.find(this.rawphotos, {
                    'mvp_id': imgid
                })
            } else {
                var photodetail = _.find(this.filteredphotos, {
                    'mvp_id': imgid
                })
            }
            var sharemessage = photodetail.mvp_disc;
            var imageurl = photodetail.mvp_image;

            let url = "https://www.linkedin.com/shareArticle?url=" + decodeURI(imageurl) +
                "&mini=true" +
                ((sharemessage) ? "&title=" + decodeURI(sharemessage) : "") + "&source=" + decodeURI(globalThis.myBaseURL);
            window.open(url, '_blank', "toolbar=yes,scrollbars=yes,resizable=yes,top=200,left=200,width=600,height=400");


        },
        togglephotodisplay() {
            if (this.photodisplay.height == 200) {
                this.photodisplay.height = 400
                this.photodisplay.divstyle = "height:474px"
                this.photodisplay.icon = "mdi-view-comfy"
            } else {
                this.photodisplay.height = 200
                this.photodisplay.divstyle = "height:274px"
                this.photodisplay.icon = "mdi-view-grid"

            }
        },
        getFormatedDate(picdate) {

            return dateFns.format(picdate * 1000, 'MM-DD-YYYY hh:mma')
        },
        removebuildfilter() {
            this.gridSelect.selected = [];
        },
        openfullresolution(url) {
            let win = window.open(globalThis.myBaseURL + '/viewimage/?i=' + url, '_blank');
            win.focus();
        },
        buildingclick(id) {
            const b = this.buildingsdata.find(building => building.mb_id == id);
            this.gridSelect.selected.push(b);
        },
        async savetags() {
            var picIDarray = [];
            for (var i = 0; i < this.selectedPhotos.length; i++) {
                var imgid = this.selectedPhotos[i];
                if (!this.filters) {
                    var photodetail = _.find(this.rawphotos, {
                        'mvp_id': imgid
                    })
                } else {
                    var photodetail = _.find(this.filteredphotos, {
                        'mvp_id': imgid
                    })
                }

                var getpicid = imgid;
                picIDarray.push(getpicid);

            }

            var pid = picIDarray[0];
            var picIDs = picIDarray.join();
            var keys = this.tagdialogdata.model.toString();
            try {
                const formBody = new URLSearchParams();
                formBody.append("picIDs", picIDs);
                formBody.append("keys", keys);


                let response = await fetch(globalThis.myBaseURL + "/vpics/updatetags/pid/" + pid, {
                    method: 'POST',
                    body: formBody,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                if (response.ok) {
                    this.tagdialog = false;
                } else {
                    this.snackbar.text = "Error. Are you connected to internet?";
                    this.snackbar.snackbar = true;
                }

            } catch (err) {

                this.snackbar.text = "Error. Are you connected to internet?";
                this.snackbar.snackbar = true;
            }



        },
        report() {
            var rnd = Math.floor(Math.random() * 10000000);
            var imgs = new Array();
            for (var i = 0; i < this.selectedPhotos.length; i++) {
                var imgid = this.selectedPhotos[i];

                if (!this.filters) {
                    var photodetail = _.find(this.rawphotos, {
                        'mvp_id': imgid
                    })
                } else {
                    var photodetail = _.find(this.filteredphotos, {
                        'mvp_id': imgid
                    })
                }
                var desc = photodetail.mvp_disc;
                var imgdate = this.getFormatedDate(photodetail.mvp_dt);
                var result = imgdate.replace(/^[\r\n]+|\.|[\r\n]+$/g, "");
                result = result.trim();

                imgs[i] = new Array(imgid, desc, result);
            }
            var options = {
                method: 'POST',
                headers: {
                    'content-type': 'application/x-www-form-urlencoded'
                },
                data: {
                    rnd: rnd,
                    imgs: imgs
                },
                transformRequest: [function (data, headers) {
                    return Qs.stringify(data)
                }],
                paramsSerializer: function (params) {
                    return Qs.stringify(params)
                },
                url: globalThis.myBaseURL + "/vpics/savechanges2",
            };
            axios(options)
                .then(response => {
                    if (typeof response.data !== 'undefined') {
                        window.open(response.data, '_blank');
                    }

                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })

        },
        printLocalTime() {
            let form = document.createElement('form');
            form.method = 'POST';
            form.action = globalThis.myBaseURL + "/node/reports/photo-report-local-time";
            form.target = '_blank';


            const data = { photoIds: this.selectedPhotos };
            for (let key in data) {
                let input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = JSON.stringify(data[key]);
                form.appendChild(input);
            }


            document.body.appendChild(form);
            form.submit();


            document.body.removeChild(form);


        },
        print(rnd) {
            rnd = rnd || 1;
            var imgs = [];

            for (var i = 0; i < this.selectedPhotos.length; i++) {
                var imgid = this.selectedPhotos[i];

                if (!this.filters) {
                    var photodetail = _.find(this.rawphotos, {
                        'mvp_id': imgid
                    })
                } else {
                    var photodetail = _.find(this.filteredphotos, {
                        'mvp_id': imgid
                    })
                }
                const buildingdetail = this.buildingsdata.find(building => building.mb_id == photodetail.mvp_building_id);

                if (!buildingdetail)
                    var bname = ""
                else
                    var bname = buildingdetail.mb_nickname;
                var desc = photodetail.mvp_disc;
                var desc = desc.replace(/^[\r\n]+|[\r\n]+$/g, "");
                var imgdate = this.getFormatedDate(photodetail.mvp_dt);
                var url = photodetail.mvp_image;
                var result = imgdate.replace(/^[\r\n]+|\.|[\r\n]+$/g, "");
                result = result.trim();
                imgs[i] = new Array(imgid, desc, result, url, bname);
            }
            var options = {
                method: 'POST',
                headers: {
                    'content-type': 'application/x-www-form-urlencoded'
                },
                data: {
                    rnd: rnd,
                    imgs: imgs
                },
                transformRequest: [function (data, headers) {
                    return Qs.stringify(data)
                }],
                paramsSerializer: function (params) {
                    return Qs.stringify(params)
                },
                url: globalThis.myBaseURL + "/vpics/print",
            };
            axios(options)
                .then(response => {
                    if (typeof response.data !== 'undefined') {
                        window.open(response.data, '_blank');
                    }

                })
                .catch(e => {
                    EventBus.$emit('AjaxError', e);
                })
        },
        scrollUp() {
            window.scrollTo(0, 0)
        },
        onscroll: async function (event) {
            const scrollY = window.scrollY
            const visible = document.documentElement.clientHeight
            const pageHeight = document.documentElement.scrollHeight
            const bottomOfPage = visible + scrollY >= pageHeight

            if (bottomOfPage || pageHeight < visible) {
                this.page++;
                // console.log('At the bottom');
            }
        },
        /* onScroll: _.debounce(function () {
             this.offsetTop = window.pageYOffset || document.documentElement.scrollTop

             let bottomOfWindow = document.documentElement.scrollTop + window.innerHeight > document.documentElement.offsetHeight - 200;
             if (bottomOfWindow) {
                 this.page++;
             }
         }, 300), */
        startMap() {

            //add mapSelection to each object of mapImgs
            this.mapImgs.forEach(img => {
                img[11] = this.mapSelection
                img[12] = this.mapSelections.find(map => map.sml_id == this.mapSelection).sml_layer_name
                img[13] = this.mapSelections.find(map => map.sml_id == this.mapSelection).sml_urlkey
            })
            localStorage.imgs = JSON.stringify(this.mapImgs);

            const routeData = this.$router.resolve({
                name: 'mapbuilder',
                query: {
                    img: true
                }
            });

            window.open(routeData.href, '_blank');
            this.mapselectDialog = false
        },
        async openMap() {
            const imgs = [];
            let test = false;

            const firstPhoto = this.selectedPhotos[0];
            const firstBuildingId = this.filters ? this.filteredphotos.find(photo => photo.mvp_id === firstPhoto).mvp_building_id : this.rawphotos.find(photo => photo.mvp_id === firstPhoto).mvp_building_id;

            const allSameBuilding = this.selectedPhotos.every(imgid => {
                const buildingId = this.filters ? this.filteredphotos.find(photo => photo.mvp_id === imgid).mvp_building_id : this.rawphotos.find(photo => photo.mvp_id === imgid).mvp_building_id;
                return buildingId === firstBuildingId;
            });

            if (!allSameBuilding) {
                alert('All selected photos must be from the same building.');
                return;
            }
            let maps = await fetch(globalThis.myBaseURL + '/node/maps/maps-of-site/' + firstBuildingId);
            maps = await maps.json();

            this.selectedPhotos.forEach((imgid, i) => {
                const photodetail = this.filters ? this.filteredphotos.find(photo => photo.mvp_id === imgid) : this.rawphotos.find(photo => photo.mvp_id === imgid);
                const buildingdetail = this.buildingsdata.find(building => building.mb_id == photodetail.mvp_building_id);

                if (!buildingdetail) {
                    test = true;
                    return;
                }

                const desc = photodetail.mvp_disc;
                const imgdate = this.getFormatedDate(photodetail.mvp_dt);
                const url = photodetail.mvp_image;
                const lat = photodetail.mvp_lat;
                const lng = photodetail.mvp_lng;
                const bname = buildingdetail.mb_nickname;
                const buildid = photodetail.mvp_building_id;
                let result = imgdate.replace(/^[\r\n]+|\.|[\r\n]+$/g, "").trim();
                const lat2 = photodetail.mvp_latTarget;
                const lng2 = photodetail.mvp_lngTarget;
                const tags = ""; // correct this after controller update
                imgs[i] = [imgid, desc, result, url, bname, lat, lng, lat2, lng2, tags, buildid, 0, "", ""];
            });
            if (maps.length > 0) {
                maps.unshift({
                    sml_id: 0,
                    sml_layer_name: "New Map",
                    sml_urlkey: "",
                });
                this.mapSelections = maps;
                this.mapImgs = imgs;
                this.mapSelection = maps[0].sml_id;
                this.mapselectDialog = true;
                return;

            }
            if (test && !confirm('Only photos assigned to a site can be added to a map, the rest will be ignored. Do you still want to continue?')) return;

            localStorage.imgs = JSON.stringify(imgs);

            const routeData = this.$router.resolve({
                name: 'mapbuilder',
                query: {
                    img: true
                }
            });

            window.open(routeData.href, '_blank');
        },
        
        updateDialogData(index) {
           
            if (index < 0 || !this.photospaged || index >= this.photospaged.length || !this.photospaged[index]) {
                console.error("UpdateDialogData: Invalid index or photo not found at index", index);
                if (this.dialog) {
                    this.close();
                }
                return;
            }

            const currentPhoto = this.photospaged[index];

       
            this.photodesc = currentPhoto.mvp_disc;
            const keywords = globalThis.mykeywords.filter(keyword => keyword.image_id == currentPhoto.mvp_id);
            const mykey = keywords.map(k => k.keyword);
            this.phototags = mykey.toString();

            
            if (this.initialDataLoaded) {
                const buildings = this.buildingsdata;
                const currentBuildingId = currentPhoto.mvp_building_id;
                const building = buildings.find(b => b.mb_id == currentBuildingId);
                this.selectedbuilding = building ?
                    { buildingname: building.mb_nickname, buildingid: building.mb_id } :
                    { buildingname: "Unassigned", buildingid: "0" };
            } else {
                this.selectedbuilding = { buildingname: "Loading...", buildingid: null };
            }
        },
        openDialog(index) { 
            this.carouselIndex = index;
            this.currentPhotoMvpId = this.photospaged[index] ? this.photospaged[index].mvp_id : null;

            this.updateDialogData(this.carouselIndex);


            this.courselselectbuilditems = [{ buildingname: "Unassigned", buildingid: "0" }];

            this.$nextTick(() => {
                this.dialog = true;
            });




            
             if (!this.initialDataLoaded) {
                 this.selectedBuildingLoading = true;
                 this.$watch('initialDataLoaded', (newValue) => {
                     if (newValue) {
                      
                         const buildings = this.buildingsdata;
                         this.courselselectbuilditems = [{ buildingname: "Unassigned", buildingid: "0" }];
                         this.courselselectbuilditems.push(...buildings
                             .filter(building => building.mb_status == 1)
                             .map(building => ({ buildingname: building.mb_nickname, buildingid: building.mb_id }))
                         );
                         this.updateDialogData(this.carouselIndex);
                         this.selectedBuildingLoading = false;
                     }
                 }, { once: true });
             } else {
                 
                 const buildings = this.buildingsdata;
                 this.courselselectbuilditems = [{ buildingname: "Unassigned", buildingid: "0" }];
                 this.courselselectbuilditems.push(...buildings
                     .filter(building => building.mb_status == 1)
                     .map(building => ({ buildingname: building.mb_nickname, buildingid: building.mb_id }))
                 );
             }
        },

        savephotoOld() {
            var pid = this.photospaged[this.carouselIndex].mvp_id;
            var readonly = 0;
            if (pid != "") {
                var bid = this.selectedbuilding.buildingid;
                var desc = this.photodesc;
                var keys = this.phototags;
                var sharephoto = 0;
                var self = this;
                var buildingdetail = _.find(this.buildingsdata, {
                    'mb_id': bid
                })
                var options = {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/x-www-form-urlencoded'
                    },
                    data: {
                        bid: bid,
                        desc: desc,
                        keys: keys,
                        readonly: readonly,
                        sharephoto: sharephoto
                    },
                    transformRequest: [function (data, headers) {
                        return Qs.stringify(data)
                    }],
                    paramsSerializer: function (params) {
                        return Qs.stringify(params)
                    },
                    url: globalThis.myBaseURL + "/vpics/updatephoto/pid/" + pid,
                };
                axios(options)
                    .then(response => {
                        this.dialog = false;
                        var payload = {
                            pid: pid,
                            bid: bid,
                            desc: desc,
                            keys: keys
                        }

                        if (!self.filters) {
                            var k = _.find(self.rawphotos, {
                                'mvp_id': payload.pid
                            });
                        } else {
                            var k = _.find(self.filteredphotos, {
                                'mvp_id': payload.pid
                            });
                        }

                        Vue.set(k, 'mvp_disc', payload.desc);
                        Vue.set(k, 'mvp_building_id', payload.bid);
                        Vue.set(k, "mb_nickname", buildingdetail.mb_nickname);

                        //update local keyworks
                        _.remove(globalThis.mykeywords, {
                            'image_id': pid
                        });
                        var res = keys.split(",");
                        for (var j = 0; j < res.length; j++) {
                            globalThis.mykeywords.push({
                                id: 1,
                                image_id: pid,
                                keyword: res[j]

                            })
                        }
                        self.recompute = !self.recompute;


                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                    })
            }
        },
        async savephoto() {
            this.isSaving = true;
            const targetMvpId = this.currentPhotoMvpId; 
            try {
                
                const photoObject = this.photospaged[this.carouselIndex];

                if (!photoObject || photoObject.mvp_id !== targetMvpId) { 
                    console.error("SavePhoto: Photo object at carouselIndex does not match stored mvp_id or not found.", this.carouselIndex, targetMvpId);
                    this.isSaving = false;
                    this.close(); 
                    return;
                }

                const pid = targetMvpId; 
                if (!pid) {
                    console.error("SavePhoto: targetMvpId is missing.");
                    this.isSaving = false;
                    this.close();
                    return;
                }
                
                const readonly = 0;
                const bid = this.selectedbuilding.buildingid;
                const desc = this.photodesc;
                const keys = this.phototags;
                const sharephoto = 0;
                const self = this;
                const buildingdetail = _.find(this.buildingsdata, { mb_id: bid });
                
                // Prepare the data object
                const data = {
                    bid: bid,
                    pid: pid, 
                    desc: desc,
                    keys: keys,
                    readonly: readonly,
                    sharephoto: sharephoto
                };

                // Update photo details first
                const updatePhotoUrl = `${globalThis.myBaseURL}/vpics/updatephoto/pid/${pid}`;
                
                
                const options = {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/x-www-form-urlencoded'
                    },
                    data: data,
                    transformRequest: [function (data, headers) {
                        return Qs.stringify(data)
                    }],
                    url: updatePhotoUrl,
                };

                
                if (this.currentRotation !== 0 && photoObject.mvp_image_rotated) {
                    
                    await axios(options);
                    
                   
                    const imageDataFull = photoObject.mvp_image_rotated;
                    const imageData = imageDataFull;
                    const updateRotatedUrl = `/node/photos/replacePhoto`;
                    
                   
                    const rotationResponse = await fetch(updateRotatedUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            imageData,
                            originalObjectName: photoObject.mvp_image,
                            mvp_id: pid 
                        })
                    });

                    if (!rotationResponse.ok) {
                         const errorData = await rotationResponse.text(); 
                         throw new Error(`Failed to update rotated image: ${rotationResponse.status} ${errorData}`);
                    }

                   
                    const rotationResult = await rotationResponse.json();
                    if (rotationResult.success) {
                        this.$set(photoObject, 'mvp_image', rotationResult.imageUrl); 
                        this.$set(photoObject, 'mvp_rotated', rotationResult.mvp_rotated); 
                        this.$delete(photoObject, 'mvp_image_rotated'); 
                        this.currentRotation = 0; 
                    } else {
                        throw new Error(`Server reported failure during image replacement: ${rotationResult.message}`);
                    }

                } else {
                    
                    const metadataResponse = await axios(options);
                    
                }

              
                const payload = {
                    pid: pid, 
                    bid: bid, 
                    desc: desc, 
                    keys: keys 
                };
                
                let k;
                
                if (!self.filters) {
                    k = self.rawphotos.find(p => p && p.mvp_id === targetMvpId);
                } else {
                    k = self.filteredphotos.find(p => p && p.mvp_id === targetMvpId);
                }

                if (k) { 
                    Vue.set(k, 'mvp_disc', payload.desc);
                    Vue.set(k, 'mvp_building_id', payload.bid);
                    if (buildingdetail) { 
                       Vue.set(k, 'mb_nickname', buildingdetail.mb_nickname);
                    } else {
                       Vue.set(k, 'mb_nickname', 'Unassigned'); 
                    }
                } else {
                     console.error("SavePhoto: Could not find photo object with mvp_id", targetMvpId, "in source array after save.");
                }

                
                _.remove(globalThis.mykeywords, { image_id: targetMvpId });
                const res = keys.split(",");
                for (let j = 0; j < res.length; j++) {
                    globalThis.mykeywords.push({
                        id: 1,
                        image_id: pid,
                        keyword: res[j]
                    });
                }
                self.recompute = !self.recompute;

               
                this.close();

            } catch (e) {
                 console.error("Error in savephoto:", e); 
                 EventBus.$emit('AjaxError', e); 
                 
                 this.snackbar.text = `Error saving photo: ${e.message}`;
                 this.snackbar.snackbar = true;
            } finally {
                this.isSaving = false;
            }
        },
        openShareDialog() {
            this.sharedialogdata = { ...this.sharedialogdataempty };
            globalThis.mysharedemails.forEach(email => {
                this.sharedialogdata.items.push(email.vendor_email);
            });

            if (this.selectedPhotos.length === 1) {
                const params = new URLSearchParams();
                params.append('accessCode', this.$store.getters.getAccessCode);
                params.append('picid', this.selectedPhotos[0]);

                fetch(globalThis.myBaseURL + "/vpics/bogetsharedemails", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: params.toString()
                })
                    .then(response => response.json())
                    .then(data => {
                        data.forEach(item => {
                            if (item.vendor_email !== undefined) {
                                this.sharedialogdata.alreadyshared += item.vendor_email + " ";
                            }
                        });
                    })
                    .catch(e => {
                        EventBus.$emit('AjaxError', e);
                    });
            }

            this.sharedialog = true;
        },
        openTagDialog() {
            this.tagdialogdata = { ...this.tagdialogdataempty };
            const keysunique = _.uniqBy(globalThis.mykeywords, 'keyword');
            keysunique.forEach(key => {
                this.tagdialogdata.items.push(key.keyword);
            });
            this.tagdialog = true;
        },
        getCompanyName(id) {
            var b = _.find(this.photospaged, {
                'mvp_id': id
            });
            if (typeof b != 'undefined') {
                return b.vendor_company_name;
            } else {
                return "";
            }
        },
        async deletePhoto() {
            const delid = this.photospaged[this.carouselIndex].mvp_id;
            if (delid !== "" && confirm('Are you sure?')) {
                const body = new URLSearchParams();
                body.append('delid', delid);

                try {
                    const response = await fetch(`${globalThis.myBaseURL}/vpics/deletephoto/delid/${delid}`, {
                        method: 'POST',
                        headers: {
                            'content-type': 'application/x-www-form-urlencoded'
                        },
                        body: body.toString()
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    this.dialog = false;
                    const payload = { delid: delid };
                    //this.$store.commit('photoDelete', payload);
                    if (!this.filters) {
                        this.rawphotos = this.rawphotos.filter(n => n.mvp_id !== delid);
                    } else {
                        this.filteredphotos = this.filteredphotos.filter(n => n.mvp_id !== delid);
                    }
                } catch (e) {
                    EventBus.$emit('AjaxError', e);
                }
            }
        },
        close() {
            const currentIndex = this.carouselIndex; 
            this.currentRotation = 0;
           
            if (this.photospaged && currentIndex >= 0 && currentIndex < this.photospaged.length && this.photospaged[currentIndex] && this.photospaged[currentIndex].mvp_image_rotated) {
                this.$delete(this.photospaged[currentIndex], 'mvp_image_rotated');
            }
            this.currentPhotoMvpId = null; 
            this.dialog = false;
        },
        async saveshare() {
            const picIDarray = [];
            const picURLsarray = [];
            for (let i = 0; i < this.selectedPhotos.length; i++) {
                const imgid = this.selectedPhotos[i];
                const photodetail = !this.filters
                    ? this.rawphotos.find(photo => photo.mvp_id == imgid)
                    : this.filteredphotos.find(photo => photo.mvp_id == imgid);

                picIDarray.push(imgid);
                picURLsarray.push(photodetail.mvp_image);
            }

            const pid = picIDarray[0];
            const picIDs = picIDarray.join();
            const picURLs = picURLsarray.join();
            const keys = this.sharedialogdata.model.toString();

            const body = new URLSearchParams({
                picIDs: picIDs,
                keys: keys,
                picURLs: picURLs
            });

            try {
                const response = await fetch(`${globalThis.myBaseURL}/vpics/sharephotos/pid/${pid}`, {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/x-www-form-urlencoded'
                    },
                    body: body.toString()
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                this.sharedialog = false;
            } catch (e) {
                EventBus.$emit('AjaxError', e);
            }
        },
        save() {

            this.close()
        },


        onDateRangeChange(range) {

            this.range = range;
        },
        selectPhoto(index, mvpid, args) {
            if (typeof mvpid == 'undefined')
                return;
            var d = -1;


            if (args.shiftKey && this.selectedPhotos.length != 0) {

                var b = this.selectedPhotos;
                for (var i = 0; i < this.selectedPhotos.length; i++) {
                    var k = _.findIndex(this.photospaged, function (o) {
                        return o.mvp_id == b[i];
                    });
                    if (k == -1)
                        continue;
                    if (k < index && k > d) {
                        d = k
                    }
                }
            }
            var myindex = this.selectedPhotos.indexOf(mvpid);

            if (myindex == -1)
                this.selectedPhotos.push(mvpid);
            else
                this.selectedPhotos.splice(myindex, 1);
            if (d != -1) {
                for (var i = d + 1; i < index; i++) {
                    this.selectedPhotos.push(this.photospaged[i].mvp_id);
                }
            }
        },
        isPhotoSelected(index, mvpid) {
            var found = this.selectedPhotos.find(function (element) {
                return element == mvpid;
            });


            if (found == undefined)
                return false;
            else
                return true;
        },
       // Helper function to send the image data to the server and get a rotated image
async  rotateImageOnServer(source, rotationDegrees) {
    try {
      const response = await fetch("/node/photos/rotateImage", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          imageData: source, // This can be a remote URL (if not a data URL)
          rotation: rotationDegrees,
        }),
      });
      const result = await response.json();
      return result.rotatedImage; // Should be a Base64 data URL returned by the server.
    } catch (error) {
      console.error("Error rotating image on server:", error);
      throw error;
    }
  },
  rotatePhoto(index,degrees) {
    const targetMvpId = this.currentPhotoMvpId; 

    this.currentRotation = (this.currentRotation + degrees) % 360;
    if (this.currentRotation < 0) {
      this.currentRotation += 360;
    }

    
    const photoObject = this.photospaged[index];


    if (!targetMvpId) {
        console.error("RotatePhoto: Missing targetMvpId.");
        this.loadingdialog = false;
        this.close(); 
        return;
    }
    if (!photoObject || photoObject.mvp_id !== targetMvpId) { 
        console.error("RotatePhoto: Photo object at index", index, "does not match targetMvpId", targetMvpId, "or not found.");
        this.loadingdialog = false;
        return;
    }
  
    
    this.loadingdialog = true;
    
    
    const imageSource = photoObject.mvp_image_rotated ? photoObject.mvp_image_rotated : photoObject.mvp_image;
    
   
    this.rotateImageOnServer(imageSource, degrees)
      .then((rotatedImageUrl) => {
        if (rotatedImageUrl) {
          this.$set(photoObject, 'mvp_image_rotated', rotatedImageUrl);
          console.log("Image rotated on server; overall rotation:", this.currentRotation);

          
          this.$nextTick(() => {
            if (this.dialog) { 
                const currentIndex = this.photospaged.findIndex(p => p && p.mvp_id === targetMvpId);

                if (currentIndex === -1) {
                    
                    console.error("Photo no longer in list after rotation");
                    this.close();
                } else {
                    
                    if (this.carouselIndex !== currentIndex) {
                        this.carouselIndex = currentIndex; 
                    } else {
                         
                         this.updateDialogData(this.carouselIndex);
                    }
                }
            }
          });
        } else {
          console.error("Rotation failed on server.");
        }
      })
      .catch((error) => {
        console.error("Error in rotation request:", error);
      })
      .finally(() => {
       
        this.$nextTick(() => {
            this.loadingdialog = false;
        });
      });
  },
  
            
          
        
        carouselIndex(val) {
            // Reset rotation when changing photos
            this.currentRotation = 0;
            
            
            
            
            this.photodesc = this.photospaged[val].mvp_disc;
            const keywords = globalThis.mykeywords.filter(keyword => keyword.image_id == this.photospaged[val].mvp_id);


            const mykey = keywords.map(k => k.keyword);
            this.phototags = mykey.toString();

            const currentbuilding = this.photospaged[val].mvp_building_id;


            const building = this.buildingsdata.find(building => building.mb_id == currentbuilding);

            if (!building)
                this.selectedbuilding = { buildingname: "Unassigned", buildingid: "0" };
            else
                this.selectedbuilding = { buildingname: building.mb_nickname, buildingid: building.mb_id };
        },



    },
    watch: {
        activebuildingsdata: function (val) {
            this.refreshSites();
        },
        'gridSelect.selected': function (val) {
            this.gridSelect.dtSelected = this.gridSelect.selected;
        },
        buildingsdata(val) {
            if (val.length != 0) {
                this.$nextTick(() => {

                    if (typeof this.$route.params.id !== 'undefined') {

                        if (isNaN(this.$route.params.id)) {
                            this.selectedKeywords.push(this.$route.params.id);

                        } else {
                            let b = val.find(building => building.mb_id == this.$route.params.id);
                            this.gridSelect.selected.push(b);
                        }
                    }

                })
            }
        },
        range(val) {

        },
        page(val) {
            //  window.scrollTo(0, 0)
        },
        photospaged: {
            handler(newVal, oldVal) {
                if (this.dialog) { 
                    if (!newVal || newVal.length === 0) {
                        console.error("Photo list is empty");
                        this.close();
                    } else {
                      
                        const currentIndex = newVal.findIndex(p => p && p.mvp_id === this.currentPhotoMvpId);
                        if (currentIndex === -1) {
                            console.error("Photo no longer in list after pagination");
                           
                            this.close();
                        } else if (this.carouselIndex !== currentIndex) {
                          
                             this.carouselIndex = currentIndex;
                        } else {
                           
                             this.updateDialogData(this.carouselIndex);
                        }
                    }
                }
            },
            immediate: true 
        },
        carouselIndex(val, oldVal) {
            this.currentRotation = 0;
            
            if (oldVal !== undefined && oldVal >= 0 && this.photospaged && oldVal < this.photospaged.length && this.photospaged[oldVal] && this.photospaged[oldVal].mvp_image_rotated) {
                 this.$delete(this.photospaged[oldVal], 'mvp_image_rotated');
            }

            this.currentPhotoMvpId = (val >= 0 && this.photospaged && val < this.photospaged.length && this.photospaged[val]) ? this.photospaged[val].mvp_id : null;
            
            if (val >= 0 && this.photospaged && val < this.photospaged.length && this.photospaged[val]) {
                this.updateDialogData(val);
            } else if (this.dialog) {
                this.close();
            }
        },



    },
    asyncComputed: {
        photos: {
            async get() {
                const photosPerPage = 48; 
                let newPageToSet = 1; 
                let currentSourceArray; 

                if (
                    this.gridSelect.selected.length === 0 &&
                    typeof this.range.start === 'undefined' &&
                    this.selectedKeywords.length === 0 &&
                    this.selectedSubcontractors.length === 0
                ) {
                    this.filters = false;
                    currentSourceArray = this.rawphotos;
                    if (this.dialog && this.currentPhotoMvpId && Array.isArray(currentSourceArray)) {
                        const indexOfTrackedPhoto = currentSourceArray.findIndex(p => p && p.mvp_id === this.currentPhotoMvpId);
                        if (indexOfTrackedPhoto !== -1) {
                            newPageToSet = Math.floor(indexOfTrackedPhoto / photosPerPage) + 1;
                        }
                        
                    }
                    this.page = newPageToSet;
                    return this.rawphotos;

                } else { 
                    this.loadingdialog = true;
                    this.filters = true;
                    
                    const bFilter = this.gridSelect.selected.map(item => item.mb_id);
                    const kFilter = this.selectedKeywords.map(keyword => keyword.toLowerCase().trim());
                    let start, end;
                    if (typeof this.range.start !== 'undefined' && typeof this.range.end !== 'undefined') {
                        start = Math.floor(new Date(this.range.start).getTime() / 1000);
                        end = Math.floor(new Date(this.range.end).getTime() / 1000);
                    }
                    const headers = { 'Content-Type': 'application/json' };
                    const body = { accessCode: this.$store.getters.getAccessCode, limit: 2000 };
                    if (bFilter.length > 0) body.sitesearch = bFilter.join();
                    if (kFilter.length > 0) body.searchstring = kFilter.join();
if (this.selectedSubcontractors.length > 0) body.subcontractorVendorIds = this.selectedSubcontractors.map(s => s.id);
                    if (start) { body.startdate = start; body.enddate = end; }

                    try {
                        const response = await fetch(`${globalThis.myBaseURL}/node/photos`, {
                            method: 'POST', headers, body: JSON.stringify(body)
                        });
                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                        const data = await response.json();
                        this.loadingdialog = false;
                        
                        currentSourceArray = data; 
                        if (this.dialog && this.currentPhotoMvpId && Array.isArray(currentSourceArray)) {
                            const newIndexOfTrackedPhoto = currentSourceArray.findIndex(p => p && p.mvp_id === this.currentPhotoMvpId);
                            if (newIndexOfTrackedPhoto !== -1) {
                                newPageToSet = Math.floor(newIndexOfTrackedPhoto / photosPerPage) + 1;
                            }
                           
                        }
                        this.page = newPageToSet;
                        this.$set(this, 'filteredphotos', data);
                        return this.filteredphotos;
                    } catch (e) {
                        this.loadingdialog = false; 
                        EventBus.$emit('AjaxError', e);
                        this.page = 1; 
                        return this.filteredphotos || []; 
                    }
                }
            },
            default() {
                return [];
            }
        }
    },


    computed: {
        initialDataLoaded() {
            return this.$store.getters.getDataLoaded;
        },
        isHybrid() {
            return globalThis.hybridUser ?? false;
        },
        isClientViewEnabled() {
            return globalThis.clientViewEnabled ?? false;
        },
        initialpicsloaded() {
            if (this.rawphotos.length > 0 || this.filteredphotos.length > 0 || mypiccount == 0 || this.isClientViewEnabled) {
                this.loadingdialog = false;
                return true;
            }
            else {
                return false;
            }
        },
        shouldShowAddMap() {
            var test = true;
            for (var i = 0; i < this.selectedPhotos.length; i++) {
                var mytest = false
                var imgid = this.selectedPhotos[i];
                if (!this.filters) {
                    var photodetail = _.find(this.rawphotos, {
                        'mvp_id': imgid
                    })
                } else {
                    var photodetail = _.find(this.filteredphotos, {
                        'mvp_id': imgid
                    })
                }
                if (typeof photodetail == 'undefined') {
                    mytest = false;
                } else {
                    if (photodetail.mvp_building_id == null || photodetail.mvp_building_id == 0) {
                        mytest = false;
                    } else {
                        mytest = true;
                    }
                }
                test = test && mytest;
            }
            return test && (globalThis.mychstatus == "member");
        },
        photospaged() {
            const sourceArray = !this.filters ? this.rawphotos : this.filteredphotos;
            // Ensure sourceArray is an array before trying to filter/slice
            if (!Array.isArray(sourceArray)) {
                return [];
            }
            // Filter out undefined/null items
            const validPhotos = sourceArray.filter(photo => photo != null); // Or simply 'photo => photo' if objects are never falsey
            return validPhotos.slice(0, this.page * 48);
        },
        getkeywords() {
            let keywords = [];

            if (this.recompute == true || this.recompute == false) {


                for (var i = 0; i < globalThis.mykeywords.length; i++) {
                    keywords.push(globalThis.mykeywords[i].keyword);
                }
            }
            return keywords;
        },

        photoscount() {
            if (this.gridSelect.selected.length == 0 && typeof this.range.start == 'undefined' && this.selectedKeywords.length == 0)
                return this.rawphotos.length;
            else
                return this.filteredphotos.length;


        },
        activebuildingsdata() {
            return this.buildingsdata.filter(building => building.mb_status == "1");
        },
        autocompleteData() {
            return [{ mb_id: 0, mb_nickname: "Untagged" }, ...this.activebuildingsdata];
        },
        subcontractorsData() {
            let buildings = this.buildingsdata;
            let data = [];
            for(let building of buildings) {
                let contractors = building.clientview_contractors ? building.clientview_contractors.split(/[,]+/) : [];
                let contractorIds = building.clientview_contractor_ids ? building.clientview_contractor_ids.split(/[,]+/) : [];
                data.push(...contractors.map((contractor, index) => ({
                    name: contractor,
                    id: contractorIds[index]
                })));    
            }
            return data;
        },
        buildingsdata: {
            get() {
                var k = this.$store.getters.getBuildings;
                if (k == null)
                    return []
                else
                    return k;
                // return this.$store.getters.getBuildings;
            },
        },
        getVtem() {
            return parseInt(globalThis.myvtem);
        },
        marginTop() {
            if (this.$vuetify.breakpoint.smAndDown)
                return '56px';
            else
                return '64px';
        },
        mini: {
            get() {
                return this.$store.getters.getMini;
            },
            set(value) {
                this.$store.commit('setMini', value)
            }
        },
        cLogo() {
            return globalThis.mycompanylogo;
        },
        cName() {
            return globalThis.mycompanyname;
        },
        initials() {
            return globalThis.myfname.charAt(0) + globalThis.mylname.charAt(0);
        },
        pagePermissions: {
            get() {
                return globalThis.userpermissions[this.pageId] || [];
            }
        },
        pagePermissionEdit() {
            const permission = this.pagePermissions.find(a => a["Permission"] == 'Edit');
            return permission ? permission['Value'] : true;
        },
        pagePermissionDelete() {
            const permission = this.pagePermissions.find(a => a["Permission"] == 'Delete');
            return permission ? permission['Value'] : true;
        }
    }
};
