import tabulatorDatatable from "../../components/tabulator/tabulator-datatable.js";
import FormSubmit from "../form-submit.js";
import pricingEditor from "../../components/pricing-editor.js";
import postdateddashboard from "../../components/postdated/postdateddashboard.js";
import {EventBus} from "../../eventbus.js";
import rangeDatePicker from "../../components/sitefotos/range-date-picker.js";

let formIdToColorMap = {};
let colors = ["#1867c0", "#dabc12"];
let currentColor = colors[0];

//define column header menu as column visibility toggle
export default {
  template: /*html*/`
    <div id="post-dated-services-manager">
    <v-app-bar
          app
          fixed
          elevate-on-scroll
          clipped-left
          class="mainback"
          style="border-bottom:1px solid #e5e5e5;">
        <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
        <div class="">
          <img src="/images/sitefotos_logo_icon.svg" style="width:44px; height:44px;padding-right:10px;">
        </div>
        <span class="page-titles">{{$route.meta.ptitle}}</span>
        <v-spacer></v-spacer>

      <range-date-picker label="Start End Date" style="width: 300px" v-model="range" :default-range="range" @onHide="fetchData"></range-date-picker>
      
      <v-spacer></v-spacer>

        <div class="nav-block">
          <v-img :src="cLogo" max-height="36" contain max-width="72" :alt="cName"
                 style="display: inline-block"></v-img>
          <v-menu offset-y bottom style="max-width: 200px">
            <template v-slot:activator="{ on, attrs }">
              <v-avatar color="purple" size="36" class="ml-2" v-bind="attrs" v-on="on">
                <span class="white--text headline">{{initials}}</span>
              </v-avatar>
            </template>
            <v-list>
              <v-list-item to="/account">
                <v-list-item-title>Settings</v-list-item-title>
              </v-list-item>
              <v-list-item @click="logout()">
                <v-list-item-title>Log Off</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
        <v-menu offset-y :close-on-content-click='false'>
          <template v-slot:activator="{ on }">
            <v-app-bar-nav-icon v-on="on" class="hidden-md-and-up">
              <v-icon>more_vert</v-icon>
            </v-app-bar-nav-icon>
          </template>
          <v-card>
            <v-list dense>
            </v-list>
          </v-card>
        </v-menu>
      </v-app-bar>

    <v-progress-linear indeterminate color="primary" v-if="getDataLoader"></v-progress-linear>
    <tabulator-datatable
      v-if="!loading && tabulatorConfiguration != undefined"
      ref="pricingDashboard"
      :tabulator-configuration="tabulatorConfiguration"
      title="Service History"
      :primary-button-config='{
        "icon": "mdi-plus",
        "title": "New Services"
      }'
      :show-primary-button="pagePermissionAdmin||pagePermissionSuperAdmin"
      :show-clear-all-filters-button="true"
      :show-download-excel-button="true"
      :show-refresh-button="true"
      :name-of-file-export="exportFileName"
      refresh-type="local"
      @primaryButtonCallback="primaryButtonCallback"
      @rowSelectionChanged="processSelectionCount"
      @localRefresh="fetchData(false)"
      :clear-selections-on-downloads="true"
    >
    <template v-slot:custom-header>
          <v-spacer></v-spacer>
      <v-btn :disabled="originalData.length <= 0" color="black" icon @click="collapseTable">
        <v-icon v-if="!tableCollapsed">mdi-arrow-collapse</v-icon>
        <v-icon v-else>mdi-arrow-expand</v-icon>
      </v-btn>
          <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="sendEmailDialog=true;" color="black" :disabled="emailButtonDisabled" title="Send email to contractor">
          <v-icon>mdi-email</v-icon>
        </v-btn>
        <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin"  icon @click="snowApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Apply snow total">
        <v-icon>mdi-snowflake</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="peopleApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Change People">
        <v-icon>mdi-account-multiple</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="hoursApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Change Hours">
        <v-icon>mdi-clock-time-nine-outline</v-icon>
      </v-btn>
      <v-btn v-show="false"  icon @click="" color="black" :disabled="emailButtonDisabled" title="Update Status">
        <v-icon>mdi-grease-pencil</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="eventApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Assign to event">
      <v-icon>mdi-weather-snowy</v-icon>
    </v-btn>

      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" :loading="rejectServicesLoading" icon @click="rejectServices" color="black" :disabled="emailButtonDisabled" title="Reject Services">
        <v-icon>mdi-cancel</v-icon>
      </v-btn>
      
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" :loading="approveServicesLoading" icon @click="approveServices('APPROVED')" color="black" :disabled="emailButtonDisabled" title="Approve Services">
        <v-icon>mdi-check-all</v-icon>
      </v-btn>

      
      <v-btn v-if="pagePermissionSuperAdmin" :loading="approveServicesLoading" icon @click="approveServices('LOCKED')" color="black" :disabled="emailButtonDisabled" title="Approve/Lock Services">
        <v-icon>mdi-lock</v-icon>
      </v-btn>

      <v-btn v-if="pagePermissionAccounting" icon color="black" :disabled="invoiceButtonDisabled" :loading="invoiceLoading" title="Create Invoice" @click="createInvoice">
        <v-icon color="rgba(0,0,54,.54)">sitefotos-quickbooks</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAccounting" icon color="black"  :loading="billLoading"  :disabled="emailButtonDisabled" title="Create Bill" @click="createBill">
        <v-icon color="rgba(0,54,0,.54)">sitefotos-quickbooks</v-icon>
      </v-btn>
      <v-btn v-if=" vendorAccessCode == 'f182c4c2e7b4bd91debd2d0d636becac' && pagePermissionSuperAdmin " color="black" icon title="Postdated Emails" @click="openPostdatedDashboard">
        <v-icon>mdi-email-check</v-icon>
      </v-btn>
      <v-btn v-if="useCurrentTimeZone" color="black" icon title="Change Timezone to Sites" @click="toggleTimezone">
        <v-icon>mdi-earth</v-icon>
      </v-btn>
      <v-btn v-if="!useCurrentTimeZone" color="primary" icon title="Use browser Timezone" @click="toggleTimezone">
        <v-icon>mdi-earth</v-icon>
      </v-btn>
    </template>
    </tabulator-datatable>
    <v-dialog persistent v-model="snowApplyDialog" max-width="600px">
    <v-card>
      <v-toolbar class="elevation-0 white">
        <v-toolbar-title>
          <h3 class="headline mb-0">Apply Snow Total</h3>
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-toolbar-items></v-toolbar-items>
        <v-btn icon @click="snowApplyDialog=false">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-text-field type="number" label="Snow Total" v-model="snowTotal" ></v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="snowApplyDialog=false">Cancel</v-btn>
        <v-btn color="primary" :loading="snowTotalApplyLoading" @click="applySnowTotalBulk()">Apply</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog persistent v-model="eventApplyDialog" max-width="600px">
    <v-card>
      <v-toolbar class="elevation-0 white">
        <v-toolbar-title>
          <h3 class="headline mb-0">Apply Event</h3>
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-toolbar-items></v-toolbar-items>
        <v-btn icon @click="eventApplyDialog=false">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-text-field outlined hide-details dense type="text" label="Event Name" v-model="eventName" ></v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="eventApplyDialog=false">Cancel</v-btn>
        <v-btn color="primary" :loading="eventApplyLoading" @click="applyEventBulk()">Apply</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

    <v-dialog v-model="peopleApplyDialog" persistent max-width="600px">
      <v-card>
        <v-toolbar class="elevation-0 white">
          <v-toolbar-title>
            <h3 class="headline mb-0">Apply People</h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items></v-toolbar-items>
          <v-btn icon @click="peopleApplyDialog=false">
            <v-icon>close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-text-field type="number" outlined hide-details dense label="People" v-model="peopleTotal" ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="peopleApplyDialog=false">Cancel</v-btn>
          <v-btn color="primary" :loading="peopleTotalApplyLoading" @click="applyPeopleToBulk()">Apply</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="hoursApplyDialog" persistent max-width="600px">
      <v-card>
        <v-toolbar class="elevation-0 white">
          <v-toolbar-title>
            <h3 class="headline mb-0">Apply Hours</h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items></v-toolbar-items>
          <v-btn icon @click="hoursApplyDialog=false">
            <v-icon>close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-text-field type="number" outlined hide-details dense label="Hours" v-model="hoursTotal" ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="hoursApplyDialog=false">Cancel</v-btn>
          <v-btn color="primary" :loading="hoursTotalApplyLoading" @click="applyHoursToBulk()">Apply</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog persistent v-model="sendEmailDialog" fullscreen>
      <div style="display: flex; align-items: center; justify-content: center; background-color: #00000050; height: 100%">
        <div style="height: 600px; width: 600px;">
          <v-card>
            <v-toolbar class="elevation-0 white">
              <v-toolbar-title>
                <h3 class="headline mb-0">Services Approval Email</h3>
              </v-toolbar-title>
              <v-spacer></v-spacer>
              <v-toolbar-items></v-toolbar-items>
              <v-btn icon @click="sendEmailDialog=false; resetDate();">
                <v-icon>close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text>
              <v-col class="mt-5 pa-0">
                <span style="color: black;">Hi [CONTRACTOR],</span>
                <v-textarea
                    style="position: relative"
                    class="mt-5"
                    rows="4"
                    v-model="emailBody"
                    label="Email Text"
                    outlined hide-details dense
                >
                </v-textarea>
                <div class="mt-3" style="display: flex; flex-direction: row; align-items: center; height: 20px;justify-content: end;">
                  <span>
                      <v-checkbox v-model="chBoxSaveEmailBodyAsTemplate" hide-details>
                        <template v-slot:label>
                          <div class='mb-4'>
                            Save Template
                          </div>
                        </template>
                      </v-checkbox>
                    </span>
                </div>
                <v-date-picker
                    is-range
                    mode="dateTime"
                    v-model="dateRangePicker.range"
                    :model-config="dateRangePicker.modelConfig">
                  <template v-slot="{ inputValue, inputEvents, togglePopover }">
                    <div class="flex items-center" style="font-size: 12px;">
                      <v-btn style="text-align: left; height: 36px; width: 100%;" text class="pa-0 v-label input-date theme--light mt-5" @click="togglePopover()">
                        <span style="margin-left: 6px; text-transform: initial; font-size: 0.8rem; font-weight: 500; letter-spacing: normal; border-bottom: 1px solid rgb(0 0 0 / 42%); padding-bottom: 10px; display: flex; justify-content: space-between; width: 100%;">
                          <span style="margin-top: 11px; " v-if="dateRangePicker.range == null">Checkin Checkout Range</span>
                          <span style="margin-top: 11px; color: rgba(0, 0, 0, 0.87); font-weight: 500" v-else>{{humanizedRange}}</span>
                          <v-icon class='mt-2'>arrow_drop_down</v-icon>
                        </span>
                      </v-btn>
                    </div>
                  </template>
                </v-date-picker>
                <br><span class="mt-3" style="color: black;">Any changes or additions are subject to admin approval by {{cName}}</span>
                <br><span style="text-decoration: underline; color: blue; cursor: pointer;">View Service History</span>
              </v-col>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-checkbox v-model="chBoxIncludePricingColumn"  class="mr-5">
                <template v-slot:label>
                  <div class='mb-4'>
                    Include Pricing Column
                  </div>
                </template>
              </v-checkbox>
              <v-btn color="primary" :loading="emailButtonLoading" @click="sendReportInEmail" depressed tile>Send</v-btn>
            </v-card-actions>
          </v-card>
        </div>
      </div>
    </v-dialog>

    <v-dialog :key="webformkey" scrollable v-model="formSubmitDialog" persistent max-width="500px">
      <form-submit :selectedSiteID="siteIDForFormSubmit" source="ADMIN" device-model="SERVICEHISTORY" @closeFormSubmit="closeFormSubmit($event)"></form-submit>
    </v-dialog>
    
    <v-dialog :key="pricingProps.buildingId" v-model="pricingProps.show" max-width="95vw" :width=" !overallFetch ? '10vw':'95vw' " persistent scrollable>
      <pricing-editor
          v-if="pricingProps.loaded"
          v-bind:pricing-type="pricingProps.pricingType"
          :contract-type=pricingProps.contractType
          :copy-mode="pricingProps.copyMode"
          :parent-id="pricingProps.parentId"
          :trade-id="pricingProps.tradeId"
          :pricingType="pricingProps.pricingType"
          :buildingId="pricingProps.buildingId"
          :slaData="[]"
          :calculatedPricingFromDb="pricingProps.pricingJson"
          :selectedEventRange="[]"
          :customForecasts="[]"
          :serviceLevels="[]"
          :eventRanges="[]"
          :materialPricing="[]"
          :servicePricing="[]"
          :disable-role-selector="true"
          @onCancel="onCancel"
          @onComponentFetchDone="onComponentFetchDone">
      </pricing-editor>
      <v-row v-else style="background-color: white;" justify="center">
        <v-col align="center">
          <v-progress-circular indeterminate></v-progress-circular>
        </v-col>
      </v-row>
    </v-dialog>
    <template>
  <v-dialog v-model="billDialog" max-width="500px">
    <v-card>
      <v-card-title>
        Creating Bills
      </v-card-title>
      <v-card-text style="overflow:auto">
        <v-progress-linear :value="billProgress" buffer-value="0" stream></v-progress-linear>
        <v-list height="300px">
          <v-list-item-group v-if="billResults.length > 0">
            <v-list-item v-for="(result, index) in billResults" :key="index">
              <v-list-item-content>
                <v-list-item-title>{{ result.contractorName }}</v-list-item-title>
                <v-list-item-subtitle style="white-space:normal; overflow: visible" :class="{ 'error--text': result.status === 'error', 'success--text': result.status === 'success' }">
                  {{ result.message }}
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list-item-group>
          <v-alert v-else type="info">
            Processing...
          </v-alert>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
  <v-dialog v-model="invoiceDialog" max-width="500px">
    <v-card>
      <v-card-title>
        Creating Invoices
      </v-card-title>
      <v-card-text style="overflow:auto">
        <v-progress-linear :value="invoiceProgress" buffer-value="0" stream></v-progress-linear>
        <v-list height="300px">
          <v-list-item-group v-if="invoiceResults.length > 0">
            <v-list-item v-for="(result, index) in invoiceResults" :key="index">
              <v-list-item-content>
                <v-list-item-title>{{ result.clientName }}</v-list-item-title>
                <v-list-item-subtitle style="white-space:normal;overflow:visible" :class="{ 'error--text': result.status === 'error', 'success--text': result.status === 'success' }">
                  {{ result.message }}
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list-item-group>
          <v-alert v-else type="info">
            Processing...
          </v-alert>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
      
      <v-dialog v-model="info.vDialog" width="500">
        <v-card>
          <v-card-title>{{info.actionPerformed}}</v-card-title>
          <v-card-text>
            <p>{{info.successRows}}</p>
            <p>{{info.failedRows}}</p>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" tile @click="info.vDialog=false">Done</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <v-dialog persistent v-model="dialogPostdatedDashboard" fullscreen :hide-overlay="$vuetify.breakpoint.smAndDown" width="85vw" transition="dialog-bottom-transition">
        <postdateddashboard @dismissPostdated="dialogPostdatedDashboard=false"/>
      </v-dialog>
</template>

    <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
    :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
    {{ snackbar.text }}
    <v-btn color="pink" text @click="snackbar.snackbar = false">
      Close
    </v-btn>
  </v-snackbar>
    </div>
  `,
  components: {
    tabulatorDatatable,
    FormSubmit,
    pricingEditor,
    postdateddashboard,
    rangeDatePicker
  },

  created: function () {

  },

  beforeDestroy() {
    // Clean up event listeners
    EventBus.$off('onCancel', this.onCancel);
    EventBus.$off('onComponentFetchDone', this.onComponentFetchDone);
  },

  data: function () {
    return {
      useCurrentTimeZone: true,
      snowStorms: null,
      webformkey: 0,
      range: {
        start: null,
        end: null,
      },
      dialogPostdatedDashboard: false,
      dateRangePicker: {
        modelConfig: {
          start: {
            type: 'string',
            mask: 'iso',
          },
          end: {
            type: 'string',
            mask: 'iso',
          },
        },
        range: null,
      },
      info: {
        vDialog: false,
        actionPerformed: null,
        successRows: null,
        failedRows: null
      },
      pageID: 24,
      billDialog: false,
      billResults: [],
      billProgress: 0,
      billLoading: false,
      pricingProps: {
        tradeId: 0,
        parentId: 0,
        buildingId: 0,
        show: false,
        loaded: true,
        pricingType: '',
        contractType: 'Snow Removal',
        pricingJson: {},
        componentFetchDone: false,
        copyMode: false
      },
      invoiceDialog: false,
      invoiceResults: [],
      invoiceProgress: 0,
      invoiceLoading: false,

      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'right',
        mode: '',
        timeout: 6000,
        text: ''
      },
      chBoxIncludePricingColumn: true,
      chBoxSaveEmailBodyAsTemplate: true,
      emailBody: 'Please click the link below to view and edit and/or approve all services performed by you and your crews on the sites. Any changes or additions are subject to admin approval.',
      sendEmailDialog: false,
      tableCollapsed: false,
      //FORMSUBMIT VARIABLES
      formSubmitDialog: false,
      siteIDForFormSubmit: false,
      clients: [],

      peopleTotalApplyLoading: false,
      peopleApplyDialog: false,
      peopleTotal: null,

      hoursTotalApplyLoading: false,
      hoursApplyDialog: false,
      hoursTotal: null,

      snowTotalApplyLoading: false,
      snowApplyDialog: false,
      snowTotal: null,

      eventApplyLoading: false,
      eventApplyDialog: false,
      eventName: null,


      rejectServicesLoading: false,
      getDataLoader: false,
      sDate: null,
      eDate: null,
      emailButtonDisabled: true,
      invoiceButtonDisabled: true,
      approveServicesButtonDisabled: true,
      emailButtonLoading: false,
      approveServicesLoading: false,
      loading: false,
      tabulatorConfiguration: {},
      tableData: [

      ],
      originalData: []
    }
  },
  watch: {
    snowApplyDialog(val) {

      this.snowTotal = '';

    }
  },

  methods: {
    toggleTimezone(){
      this.useCurrentTimeZone = !this.useCurrentTimeZone;
      const table = this.$refs.pricingDashboard.tabulator;
      table.blockRedraw();                 // suspend all redraws
      table.getRows("active").forEach(r => r.reformat()); // only visible rows
      table.restoreRedraw();
    },
    openPostdatedDashboard(){
      this.dialogPostdatedDashboard = true;
    },
    resetDate() {
      this.dateRangePicker.range = null;
    },
    showInfoDialog(actionPerformed,actionSuccess, actionFailed){
      this.info.actionPerformed = actionPerformed;
      this.info.successRows = actionSuccess;
      this.info.failedRows = actionFailed;
      this.info.vDialog = true;
    },
    openPricingContractForContractor(e, row) {
      let rowData = row.getData();
      if (rowData.TradeIdContractor !== -1){
        this.pricingProps.buildingId = rowData.site_id;
        this.pricingProps.pricingType = rowData.PricingTypeContractor;
        this.pricingProps.parentId = rowData.ParentIdContractor;
        this.pricingProps.tradeId = rowData.TradeIdContractor;
        this.pricingProps.copyMode = false;
        this.pricingProps.show = true;
      }
    },
    openPricingContract(e, row) {
      let rowData = row.getData();
      this.pricingProps.buildingId = rowData.site_id;
      this.pricingProps.pricingType = rowData.PricingType;
      this.pricingProps.parentId = rowData.ParentId;
      this.pricingProps.tradeId = rowData.TradeId;
      this.pricingProps.copyMode = false;
      this.pricingProps.show = true;
    },
    onComponentFetchDone() {
      this.pricingProps.componentFetchDone = true;
    },
    onCancel: async function () {
      this.pricingProps = {
        tradeId: 0,
        parentId: 0,
        buildingId: 0,
        show: false,
        loaded: true,
        pricingType: '',
        contractType: 'Snow Removal',
        pricingJson: {},
        componentFetchDone: false,
        copyMode: false
      }
    },
    parseCollapsedRows(selectedRows) {

      if (this.tableCollapsed) {
        //If view is collapsed we need to get data from original data
        let originalData = this.originalData;
        let selectedRowsIds = selectedRows.map(od => od.FormId);
        let filteredData = originalData.filter(od => selectedRowsIds.includes(od.FormId));
        // selectedRows = filteredData.filter((sr) => sr.ProviderId != myvtem);
        return filteredData;
      } else {
        // selectedRows = selectedRows.filter((sr) => sr.ProviderId != myvtem);
        return selectedRows;
      }

    },
    getServiceChannelURL(id) {
      return `https://www.servicechannel.com/sc/wo/Workorders/index?id=${id}`
    },
    async collapseTable() {
      let data = this.originalData;
      this.tableCollapsed = !this.tableCollapsed;

      if (this.tableCollapsed) {
        //Table is collapsed
        let dataTreeStructure = this.convertDataToRooferStructure(data);
        this.$refs.pricingDashboard.tabulator.replaceData(dataTreeStructure);
        this.$refs.pricingDashboard.showAlert("Table is now in collapsed view", "success");
      } else {
        this.$refs.pricingDashboard.tabulator.replaceData(data);
        this.$refs.pricingDashboard.showAlert("Table is now in expanded view", "success");
      }

    },
    convertDataToRooferStructure(data) {
      const formIds = Array.from(new Set(data.map(dt => dt.FormId)));
      const finalArray = formIds.map(formId => {
        const perFormArray = data.filter(dt => dt.FormId === formId);

        const combinedObject = perFormArray.reduce((acc, curr) => {
          for (const key in curr) {
            if (acc.hasOwnProperty(key)) {
              if (key === 'Service' || key === 'Notes') {
                acc[key] += `${acc[key] ? ', ' : ''}${curr[key]}`;
              } else if (key === 'ContractorPricing' || key === 'ClientPricing' || key === 'snow_inch' || key === 'People' || key === 'Hours') {
                acc[key] += curr[key]
              }
            } else {
              acc[key] = curr[key];
            }
          }
          return acc;
        }, {});

        return combinedObject;
      });
      return finalArray;
    },
    isStringEmptyOrWhitespace(str) {
      return str.trim().length === 0;
    },
    //Update add service related notes
    async addUpdateServiceNotes(rowData, notes) {
      let serviceId = rowData.swsd_id;
      await fetch(`/node/services/service-notes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          serviceId: serviceId,
          notes: notes
        })
      });
      this.$refs.pricingDashboard.showAlert("Notes have been updated", "success");
    },
    //FORM SUBMIT RELATED METHODS START
    _formSubmit: function (event) {
      this.webformkey += 1;
      this.formSubmitDialog = true;
      this.siteIDForFormSubmit = 0;
    },
    closeFormSubmit: async function (shouldRefreshData) {
      this.formSubmitDialog = false;
      await this.fetchData();
    },
    //FORM SUBMIT RELATED METHODS END

    async applyEventBulk(singleUpdate = false, singleRow = []) {
      if (!this.eventName || this.eventName.length < 4) {
        alert("Event Name should contain at least 4 characters!");
        return;
      }
      try {
        let ids = [];
        let validRows = [];
        let failedRows = [];
        let rows = [];
        let selectedRows = [];

        if (singleUpdate && singleRow.length && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          validRows = singleRow; // If it's a single update, assume it's valid
        } else {
          this.eventApplyLoading = true;
          rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              eventName: this.eventName
            })
          });
          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          //Let's not fetch data from server, instead manipulate front-end
          this.newData = validRows.map(item => ({...item, swsd_event: this.eventName}))
          await this.updateData(this.newData);
          //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, swsd_event: this.eventName})) );

          // Show the result dialog
          if (validRows.length > 0 || failedRows.length > 0) {
            let message = 'Event Assignment';
            let successMessage = '';
            let failureMessage = '';

            if (validRows.length > 0) {
              successMessage += `\n${validRows.length} service(s) updated successfully.`;
            }
            if (failedRows.length > 0) {
              failureMessage += `\n${failedRows.length} service(s) is/are locked and cannot be updated.`;
            }

            this.showInfoDialog(message, successMessage, failureMessage);
          }

        } else {
          // Show a message if no valid services are selected
          this.showInfoDialog('Service Update Summary', '', 'No services were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.eventApplyLoading = false;
        this.eventApplyDialog = false;
        this.eventName = null;
      }
    },
    async applySnowTotalBulk(singleUpdate = false, singleRow = [], snowTPassed = 0) {

      if (isNaN(this.snowTotal) && singleUpdate == false) {
        alert("Snow Total should be a number");
        return;
      }
      try {
        this.snowTotalApplyLoading = true;
        let ids = [];
        let snowTotal = this.snowTotal;
        let validRows = [];
        let failedRows = [];

        if (singleUpdate && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          snowTotal = snowTPassed;
          validRows = [singleRow[0]]; // Assuming it's valid for single update
          if (isNaN(snowTotal)) {
            alert("Snow Total should be a number");
            return;
          }
        } else {
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              snowTotal: snowTotal
            })
          });
          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          let newData = validRows.map(item => ({...item, snow_inch: snowTotal}))
          //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, snow_inch: snowTotal})) );
          await this.updateData(newData);
          // Show the result dialog
          let message = 'Snow Total Update Summary';
          let successMessage = '';
          let failureMessage = '';

          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) snow total(s) updated successfully.`;
          }
          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) snow total(s) is/are locked and cannot be updated.`;
          }

          this.showInfoDialog(message, successMessage, failureMessage);

        } else {
          // Show a message if no valid snow totals are selected
          this.showInfoDialog('Snow Total Update Summary', '', 'No snow totals were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.snowTotalApplyLoading = false;
        this.snowApplyDialog = false;
        this.snowTotal = null;
      }
    },
    async updateData(data) {

      const lookup = {};
      for (const item of this.originalData) {
        lookup[item.swsd_id] = item;
      }


      for (const update of data) {
        if (lookup[update.swsd_id]) {
          Object.assign(lookup[update.swsd_id], update);
        }
      }
      this.$nextTick(() => {
        this.$refs.pricingDashboard.tabulator.replaceData(this.originalData);
      })
    },
    async applyPeopleToBulk(singleUpdate = false, singleRow = [], peoplePassed = 0) {
      if (isNaN(this.peopleTotal)) {
        alert("People should be a number");
        return;
      }
      try {
        this.peopleTotalApplyLoading = true;
        let ids = [];
        let people = this.peopleTotal;
        let validRows = [];
        let failedRows = [];

        if (singleUpdate && singleRow.length == 1 && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          people = peoplePassed;
          validRows = [singleRow[0]]; // Assuming single update is valid
        } else {
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              people: people
            })
          });

          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          let newData = validRows.map(item => ({...item, People: people}))
          await this.updateData(newData);
          // Show the result dialog
          let message = 'People Update Summary';
          let successMessage = '';
          let failureMessage = '';

          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) people total(s) updated successfully.`;
          }
          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) people total(s) is/are locked and cannot be updated.`;
          }

          this.showInfoDialog(message, successMessage, failureMessage);

        } else {
          // Show a message if no valid people totals are selected
          this.showInfoDialog('People Update Summary', '', 'No people totals were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.peopleTotalApplyLoading = false;
        this.peopleApplyDialog = false;
        this.peopleTotal = null;
      }
    },

    async applyHoursToBulk(singleUpdate = false, singleRow = [], hoursPassed = 0) {
      if (isNaN(this.hoursTotal)) {
        alert("Hours should be a number");
        return;
      }
      try {
        this.hoursTotalApplyLoading = true;
        let ids = [];
        let hours = this.hoursTotal;
        let validRows = [];
        let failedRows = [];

        if (singleUpdate && singleRow.length == 1 && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          hours = hoursPassed;
          validRows = [singleRow[0]]; // Assuming single update is valid
        } else {
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              hours: hours
            })
          });
          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          this.newData = validRows.map(item => ({...item, Hours: hours}))
          //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, Hours: hours})) );
          await this.updateData(this.newData);
          // Show the result dialog
          let message = 'Hours Update Summary';
          let successMessage = '';
          let failureMessage = '';

          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) hour(s) updated successfully.`;
          }
          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) hour(s) is/are locked and cannot be updated.`;
          }

          this.showInfoDialog(message, successMessage, failureMessage);

        } else {
          // Show a message if no valid hours are selected
          this.showInfoDialog('Hours Update Summary', '', 'No hours were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.hoursTotalApplyLoading = false;
        this.hoursApplyDialog = false;
        this.hoursTotal = null;
      }
    },

    processSelectionCount() {
      const selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      const filteredSelectedRows = selectedRows.filter(row => row.VendorId == myvtem); // Filter rows and remove subcontracted rows.

      // Enable invoice button if at least one row is selected, disable otherwise.
      this.invoiceButtonDisabled = !(selectedRows.length >= 1);

      // Enable email button if there are selected rows after filtering, disable otherwise.
      this.emailButtonDisabled = !(filteredSelectedRows.length >= 1);
    },
    async approveServices(status) {
      this.approveServicesLoading = true;
      let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      let validRows = [];
      let failedRows = [];

      selectedRows.forEach(sR => {
        if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
          validRows.push(sR);  // Rows that pass the conditions
        } else {
          failedRows.push(sR);  // Rows that fail the conditions
        }
      });
      await this.acceptUserChangesNew(validRows, failedRows, status);
    },
    async acceptUserChangesNew(validRows, failedRows, status) {
      let resp = await fetch('/node/services/service-status', {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "rows": validRows,
          "status": status
        })
      });
      this.approveServicesLoading = false;
      this.newData = validRows.map(item => ({...item, Status: status}))
      await this.updateData(this.newData);
      //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, Status: status})) );

      if (status == 'APPROVED') {
        let message = 'Service Approval Summary';
        let successMessage = '';
        let failureMessage = '';
        if (validRows.length > 0) {
          successMessage += `\n${validRows.length} service(s) approved successfully.`;
        }
        if (failedRows.length > 0) {
          failureMessage += `\n${failedRows.length} service(s) is/are locked and cannot be approved, as action is restricted for locked services.`;
        }
        this.showInfoDialog('Service Approval Summary', successMessage, failureMessage);
      }
      else if (status == 'LOCKED') {
        this.showInfoDialog('Service Lock Summary', `${validRows.length} services have been locked successfully.`, '');
      }
    },

    async createBill() {
      try {
        let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;

        if (selectedRows.length < 1) {
          this.$set(this.snackbar, 'text', "Please select some rows");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        //check if the row has invoice already if yes do not allow to reinvoice
        let billedRows = selectedRows.filter((sr) => sr.swsd_qb_bill_id != null && sr.swsd_qb_bill_id !== '' && sr.Status != 'LOCKED');

        if (billedRows.length > 0) {
          this.$set(this.snackbar, 'text', "Please select rows that are not already billed");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        let priceData = selectedRows.map(({ ContractorPricing }) => ContractorPricing);
        let isNumeric = priceData.every((price) => price === "" || price == 0 ? false : !isNaN(price));

        if (!isNumeric) {
          this.$set(this.snackbar, 'text', "Please select rows with contractor pricing");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        let contractorDump = {};


        selectedRows.forEach(({ swsd_id, swsd_service_id, ContractorPricingUi, Service, UnixTime, ProviderContactId, ContractorData, mb_quickbooks_customer_id, swsd_vendor_sequence_id }) => {
          const customer = this.clients.find((client) => client.sqc_id == mb_quickbooks_customer_id)?.sqc_external_id;

          if (!contractorDump[ProviderContactId]) {
            contractorDump[ProviderContactId] = [];
          }
          contractorDump[ProviderContactId].push({
            swsd_id: swsd_id,
            contractor_name: ContractorData?.company,
            customer_id: customer,
            s_id: swsd_service_id,
            total: ContractorPricingUi,
            service_name: Service,
            service_index: swsd_vendor_sequence_id,
            date: moment.unix(UnixTime).format("MM/DD/YYYY HH:mm")
          });
        });

        let uniqueContractorIds = Object.keys(contractorDump);
        if (uniqueContractorIds.length === 0) {
          this.$set(this.snackbar, 'text', "Please select rows for contractors associated with QuickBooks");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        this.billLoading = true;
        this.billProgress = 0;
        this.billDialog = true;
        this.billResults = [];

        for (let i = 0; i < uniqueContractorIds.length; i++) {
          let contractorId = uniqueContractorIds[i];
          let data = contractorDump[contractorId];

          let contractorName = data[0].contractor_name;
          try {
            let response = await fetch(`/node/quickbooks/bill/${contractorId}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data),
            });

            if (response.ok) {
              const res = await response.json();
              if (res.status === 'success')
                this.billResults.push({ contractorId, contractorName, message: res.message, status: 'success' });
              else
                this.billResults.push({ contractorId, contractorName, message: res.message, status: 'error' });
            } else {
              this.billResults.push({ contractorId, contractorName, message: "Error creating bill in QuickBooks", status: 'error' });
            }
          } catch (error) {
            console.error("Error in createBill for contractorId", contractorId, error);
            this.billResults.push({ contractorId, contractorName, message: "An unexpected error occurred", status: 'error' });
          }

          this.billProgress = ((i + 1) / uniqueContractorIds.length) * 100;
        }
        await this.fetchData();
        this.billLoading = false;
      } catch (error) {
        console.error("Error in createBill", error);
        this.$set(this.snackbar, 'text', "An unexpected error occurred");
        this.$set(this.snackbar, 'snackbar', true);
        this.billLoading = false;
        this.billDialog = false;
      }
    },

    async createInvoice() {
      try {
        let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;

        if (selectedRows.length < 1) {
          this.$set(this.snackbar, 'text', "Please select some rows");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        //check if the row has invoice already if yes do not allow to reinvoice, ALSO CHECK SERVICE IS NOT LOCKED
        let invoicedRows = selectedRows.filter((sr) => sr.swsd_qb_invoice_id != null && sr.swsd_qb_invoice_id !== '' && sr.Status != 'LOCKED');

        if (invoicedRows.length > 0) {
          this.$set(this.snackbar, 'text', "Please select rows that are not invoiced");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        //Following logic will fail for per event becuase there
        let priceData = selectedRows.map(({ ClientPricing }) => ClientPricing);
        let isNumeric = priceData.every((price) => price === "" || price == 0 ? false : !isNaN(price));
        if (!isNumeric) {
          this.$set(this.snackbar, 'text', "Please select rows with client pricing");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        let clientData = {};
        selectedRows.forEach(({ swsd_id, swsd_service_id, ClientPricingUi, Service, UnixTime, mb_quickbooks_customer_id, People, Hours, swsd_vendor_sequence_id }) => {
          if (!mb_quickbooks_customer_id || mb_quickbooks_customer_id === "" || mb_quickbooks_customer_id === "null") {
            return;
          }

          if (!clientData[mb_quickbooks_customer_id]) {
            clientData[mb_quickbooks_customer_id] = [];
          }
          const client_name = this.clients.find((client) => client.sqc_id == mb_quickbooks_customer_id);
          const qty = People && Hours ? People * Hours : 1;
          clientData[mb_quickbooks_customer_id].push({
            client_name: client_name?.sqc_display_name,
            s_id: swsd_service_id,
            total: ClientPricingUi,
            swsd_id: swsd_id,
            service_name: Service,
            service_index: swsd_vendor_sequence_id,
            date: moment.unix(UnixTime).format("MM/DD/YYYY HH:mm"),
            qty: qty
          });
        });

        let uniqueClientIds = Object.keys(clientData);
        if (uniqueClientIds.length === 0) {
          this.$set(this.snackbar, 'text', "There are no customers associated with the rows");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        this.invoiceLoading = true;
        this.invoiceProgress = 0;
        this.invoiceDialog = true;
        this.invoiceResults = [];

        for (let i = 0; i < uniqueClientIds.length; i++) {
          let clientId = uniqueClientIds[i];
          let data = clientData[clientId];
          let clientName = data[0]?.client_name;

          try {
            let response = await fetch(`/node/quickbooks/invoice-site-customer/${clientId}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data),
            });

            if (response.ok) {
              const invoiceNumber = await response.json();
              if (invoiceNumber.status === 'success')
                this.invoiceResults.push({ clientId, clientName, message: invoiceNumber.message, status: 'success' });
              if (invoiceNumber.status === 'error')
                this.invoiceResults.push({ clientId, clientName, message: invoiceNumber.message, status: 'error' });
            } else {
              this.invoiceResults.push({ clientId, clientName, message: invoiceNumber.message, status: 'error' });
            }
          } catch (error) {
            console.error("Error in createInvoice for clientId", clientId, error);
            this.invoiceResults.push({ clientId, clientName, message: "An unexpected error occurred", status: 'error' });
          }

          this.invoiceProgress = ((i + 1) / uniqueClientIds.length) * 100;
        }
        await this.fetchData();
        this.invoiceLoading = false;
      } catch (error) {
        console.error("Error in createInvoice", error);
        this.$set(this.snackbar, 'text', "An unexpected error occurred");
        this.$set(this.snackbar, 'snackbar', true);
        this.invoiceLoading = false;
        this.invoiceDialog = false;
      }
    },

    async rejectServices() {
      let message = 'Service Processing Summary';
      let successMessage = '';
      let failureMessage = '';

      // Filtering valid and failed rows
      let validRows = [];
      let failedRows = [];

      let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      selectedRows.forEach(sR => {
        const isMyVendor = sR.VendorId == myvtem;
        const isLocked = ['INVOICED', 'BILLED', 'INVOICED_BILLED', 'LOCKED'].includes(sR.Status);
        const isApproved = sR.Status === 'APPROVED';

        const canReject = isMyVendor && (
          !isLocked && (
            !isApproved || (isApproved && this.pagePermissionSuperAdmin)
          )
        );

        if (canReject) {
          validRows.push(sR);
        } else {
          failedRows.push(sR);
        }
      });

      // Generating success and failure messages
      if (validRows.length > 0) {
        successMessage += `\n${validRows.length} service(s) rejected successfully.`;
      }

      if (failedRows.length > 0) {
        failureMessage += `\n${failedRows.length} service(s) could not be rejected due to one of the following statuses: Approved, Invoiced, Billed, or Locked.`;
      }

      if (validRows.length > 0) {
        this.rejectServicesLoading = true;
        await fetch('/node/services/service-status', {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            "rows": validRows,
            "status": "REJECTED"
          })
        });
        this.rejectServicesLoading = false;
        this.newData = validRows.map(item => ({...item, Status: "REJECTED"}))
        await this.updateData(this.newData);
        //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, Status: "REJECTED"})) );

        this.showInfoDialog('Service Processing Summary', successMessage, failureMessage);
      }
      else {
        this.$refs.pricingDashboard.showAlert("Services cannot be rejected.", "success");
      }
    },
    async sendBreadcrumb(type, date, accessCode, siteId, profileID, uuid, contractorEmail) {
      let params = new URLSearchParams({
        'type': type,
        'accessCode': accessCode,
        'email': contractorEmail,
        'dt': date,
        'deviceType': 'WEB',
        'deviceModel': 'ADMINDASHBOARD',
        'lat': 0,
        'lon': 0,
        'uuid': uuid,
        'siteid': siteId,
        'profileID': profileID //i guess this is a form id
      });
      let request = await fetch("http://localhost/vpics/uploadbreadcrumb", {
        "headers": {
          "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
        },
        "body": params,
        "method": "POST",
      });
      let data = await request.json();
    },
    isNullOrEmpty(value) {
      return value === null ||
        value === undefined ||
        (typeof value === 'string' && value.trim() === '');
    },
    async sendReportInEmail() {
      this.emailButtonLoading = true;
      let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;
      selectedRows = selectedRows.filter(sr => sr.ProviderId != myvtem && sr.VendorId == myvtem && sr.Status != 'LOCKED');
      if (selectedRows.length > 0) {
        const saveTemplate = this.chBoxSaveEmailBodyAsTemplate;
        const includePricingColumn = this.chBoxIncludePricingColumn;

        if (this.tableCollapsed) {
          //If view is collapsed we need to get data from original data
          let originalData = this.originalData;
          let selectedRowsIds = selectedRows.map(od => od.FormId);
          selectedRows = originalData.filter(od => selectedRowsIds.includes(od.FormId));
        }

        await fetch(`/node/services/send-services-postdated`, {
          method: "post",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            selectedRows: selectedRows,
            startDate: moment(this.range.start).format('YYYY/MM/DD HH:mm:ss'),
            endDate: moment(this.range.end).format('YYYY/MM/DD HH:mm:ss'),
            checkIn: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.start).unix(),
            checkOut: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.end).unix(),
            saveTemplate: saveTemplate,
            includePricingColumn: includePricingColumn,
            emailBody: this.emailBody
          })
        });
        this.$refs.pricingDashboard.showAlert("Emails have been sent.", "success");
        this.sendEmailDialog = false;
        this.emailButtonLoading = false;
        this.newData = selectedRows.map(item => ({...item, Status: "SENT"}))
        await this.updateData(this.newData);
        //this.$refs.pricingDashboard.tabulator.updateData( selectedRows.map(item => ({...item, Status: "SENT"})) );
      }
      else {
        this.$refs.pricingDashboard.showAlert("Emails not sent. All services selected are read-only.", "success");
        this.sendEmailDialog = false;
        this.emailButtonLoading = false;
      }
    },
    openForm(formId) {
      window.open(`${myBaseURL}/vpics/printform?fid=${formId}`, '_blank')
    },
    navigateToRouteNewTab(name, id) {
      const routeURL = this.$router.resolve({ name: name, params: { id } }).href;
      window.open(routeURL, '_blank');
    },
    navigateToRoute(name, id) {
      this.$router.push({ name: name, params: { id } });
    },
    parseServiceDetails(value){
      if (value != null) {
        var co = phpUnserialize(value);
        if (Array.isArray(co)) {
          return co.join(", ")
        } else {
          //return co.substring(1, co.length - 1)
          return co;
        }
      }
    },
    async fetchData(callSnowTotal = true) { //This flag is just for testing and needs to be removed
      this.getDataLoader = true
      const start = dateFns.parse(this.range.start).getTime() / 1000;
      const end = dateFns.parse(this.range.end).getTime() / 1000;
      let timeZone = this.currentTimezone;

      const stormResponseAccuWeather = await fetch(`https://weather.sitefotos.com/snow-events?start=${start}&end=${end}`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + mytoken
        }
      });
      const stormResultsAccuWeather = await stormResponseAccuWeather.json();

      const resp = await fetch(`/node/services/service-history-dashboard?start=${start}&end=${end}&tz=${timeZone}`, {
        method: 'GET'
      });

      let rows = await resp.json();
      let pricingStructure = rows.pricingDetails;
      let lineItems = rows.lineItems;
      if (callSnowTotal) {
        this.snowStorms = rows.snowStorms;
      }
      const workorderListWhereWorkorderPricingAlreadyLoaded = [];
      const workorderListWhereWorkorderPricingAlreadyLoadedClient = [];
      let pricingResults = lineItems.map(svc => {
        let connected;
        let mainPrice;
        const contractTypes = ['CONTRACTOR', 'CLIENT'];
        let contractorPrice, clientPrice = 0;
        svc.ServiceLoggedByEmployee = true;
        svc.id = svc.swsd_id;

        if (svc.VendorId == myvtem) {
          //User Account
          //Let's find connected service and its connected contract details
          connected = pricingStructure.filter(e => (
            (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
              (e.sps_service_type === svc.connected_service &&
                e.sps_site_id === svc.site_id &&
                svc.vendor_service_trade_id == e.spc_st_trade_id) :
              (e.sps_service_type_id === svc.vendor_services_service_type_id &&
                e.sps_site_id === svc.site_id &&
                svc.vendor_service_trade_id == e.spc_st_trade_id)
          ));

          contractTypes.forEach(contractType => {
            let price = pricingStructure.filter(e => (
              (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
                (e.sps_service_type === svc.connected_service &&
                  e.sps_site_id === svc.site_id &&
                  svc.vendor_service_trade_id == e.spc_st_trade_id &&
                  e.sps_contract_for === contractType) :
                (e.sps_service_type_id === svc.vendor_services_service_type_id &&
                  e.sps_site_id === svc.site_id &&
                  svc.vendor_service_trade_id == e.spc_st_trade_id &&
                  e.sps_contract_for === contractType)
            ));

            if (contractType === 'CONTRACTOR') {
              if (svc.ProviderId == myvtem) {
                contractorPrice = [];
                //check if InternalContact is empty string if yes use email
                //Services were logged by our employee
                svc.ProviderText = svc.InternalContact != null ? (svc.InternalContact.trim() === "" ? svc.swsd_email : svc.InternalContact) : "";
                svc.ServiceLoggedByEmployee = true;
              }
              else {
                svc.ProviderText = svc.ContractorData.company;
                svc.ServiceLoggedByEmployee = false;

                if (svc.ProviderContactId && price.some(p => p.spc_contractors.includes(svc.ProviderContactId.toString()))) {
                  //Getting contractor specific contract
                  contractorPrice = price.find(p => p.spc_contractors.includes(svc.ProviderContactId.toString()));
                }

                else if ( price.find(p => p.spc_contractors.length === 0) ) {
                  //Getting contracts assigned to all contractors
                  contractorPrice = price.find(p => p.spc_contractors.length === 0);
                }

                else if (!svc.ProviderContactId) {
                  //Getting not assigned contracts?
                  contractorPrice = price.find(p => !svc.ProviderContactId);
                }

                if (contractorPrice == undefined) {
                  contractorPrice = []
                } else {
                  contractorPrice = price.filter(p => p.spc_id == contractorPrice.spc_id);
                }
                mainPrice = price;
              }
            }


            else if (contractType === 'CLIENT') {
              clientPrice = price;
            }
          });
        }
        else {
          //Sub Account
          //There will be no contractor price
          //Contractor price of the main account will be client price for sub
          svc.Notes = ""; //Do not pass down notes to sub accounts.
          connected = pricingStructure.filter(e => (
            (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
              (e.sps_service_type === svc.connected_service &&
                e.sps_site_id === svc.site_id &&
                svc.vendor_service_trade_id == e.spc_st_trade_id) :
              (e.sps_service_type_id === svc.vendor_services_service_type_id &&
                e.sps_site_id === svc.site_id &&
                svc.vendor_service_trade_id == e.spc_st_trade_id)
          ));
          contractorPrice = [];
          contractTypes.forEach(contractType => {
            let price = pricingStructure.filter(e => (
              (svc.vendor_services_service_type_id == null || svc.vendor_services_service_type_id == undefined) ?
                (e.sps_service_type === svc.connected_service &&
                  e.sps_site_id === svc.site_id &&
                  svc.vendor_service_trade_id == e.spc_st_trade_id &&
                  e.sps_contract_for === contractType) :
                (e.sps_service_type_id === svc.vendor_services_service_type_id &&
                  e.sps_site_id === svc.site_id &&
                  svc.vendor_service_trade_id == e.spc_st_trade_id &&
                  e.sps_contract_for === contractType)
            ));

            if (contractType === 'CONTRACTOR') {
              if (svc.ProviderContactId && price.some(p => p.spc_contractors.includes(svc.ProviderContactId.toString()))) {
                clientPrice = price.find(p => p.spc_contractors.includes(svc.ProviderContactId.toString()));
              } else if (price.some(p => p.spc_contractors.length === 0)) {
                clientPrice = price.find(p => p.spc_contractors.length === 0);
              } else if (!svc.ProviderContactId) {
                clientPrice = price.find(p => !svc.ProviderContactId);
              }

              if (clientPrice == undefined) {
                clientPrice = []
              } else {
                clientPrice = price.filter(p => p.spc_id == clientPrice.spc_id);
              }
              mainPrice = price;
            }
          });
        }



        if (typeof (connected) != "undefined") {
          //sps_contract_type is old pricing structure, we have old rows as well so we will be using this to avoid any crashes
          //sps_service_option_trigger is new pricing type


          svc.snow_seasonal_total = this.snowStorms?.[svc.site_id] ?? 0;

          // Filter seasonal client price items
          const seasonalItems = clientPrice.filter(cp => cp?.sps_use_seasonal_values);
          const seasonalItemsContractor = contractorPrice.filter(cp => cp?.sps_use_seasonal_values);

          // Find match for snow range
          //There is a case where no match was found, if seasonal yes and no seasonal match then it should be 0
          const matchedSeasonal = this.findClosestSnowTriggerForSeasonalTier(seasonalItems, svc.snow_seasonal_total, svc.snow_inch);
          const matchedSeasonalContractor = this.findClosestSnowTriggerForSeasonalTier(seasonalItemsContractor, svc.snow_seasonal_total, svc.snow_inch);


          if (matchedSeasonal) {
            clientPrice = [matchedSeasonal];
          }
          else {
            //Only get non-seasonal tier rows.
            clientPrice = clientPrice.filter(cp => !cp?.sps_use_seasonal_values);
          }

          if (matchedSeasonalContractor) {
            contractorPrice = [matchedSeasonalContractor];
          }
          else {
            //Discuss Per day with mike
            contractorPrice = contractorPrice.filter(cp => !cp?.sps_use_seasonal_values);
          }

          //
          svc.PricingType = clientPrice.length > 0 ? clientPrice[0].spc_contract_for : "";
          svc.ParentId = clientPrice.length > 0 ? clientPrice[0].spc_id : null;
          svc.TradeId = clientPrice.length > 0 ? clientPrice[0].spc_st_trade_id : "";
          //Service

          svc.PricingTypeContractor = contractorPrice.length > 0 ? contractorPrice[0].spc_contract_for : "";
          svc.ParentIdContractor = contractorPrice.length > 0 ? contractorPrice[0].spc_id : null;
          svc.TradeIdContractor = contractorPrice.length > 0 ? contractorPrice[0].spc_st_trade_id : "";

          svc.ClientContractId = clientPrice.length > 0 ? clientPrice[0].spc_id : 0;
          svc.ContractorContractId = contractorPrice.length > 0 ? contractorPrice[0].spc_id : 0;

          svc.ClientContract = clientPrice.length > 0 ? clientPrice[0].spc_name : "";
          svc.ContractorContract = contractorPrice.length > 0 ? contractorPrice[0].spc_name : "";

          svc.ClientPricingUnit = clientPrice.length < 1 ? "" : (clientPrice[0].sps_contract_type.length > 0 ? clientPrice[0].sps_contract_type : clientPrice[0].sps_service_option_trigger);
          svc.ContractorPricingUnit = contractorPrice.length < 1 ? "" : (contractorPrice[0].sps_contract_type.length > 0 ? contractorPrice[0].sps_contract_type : contractorPrice[0].sps_service_option_trigger);

          svc.Hours = svc.hours === null ? 1 : svc.hours;
          svc.People = svc.people === null ? 1 : svc.people;
          // svc.Price = connected.sps_price;
          svc.Source = !svc.swsd_service_source || svc.swsd_service_source.trim().length === 0 ? 'MOBILE':svc.swsd_service_source;
          svc.Status = svc.swsd_service_status.trim().length === 0 ? 'LOGGED':svc.swsd_service_status;

          if (svc.snow_inch == null || svc.snow_inch == undefined) {
            svc.snow_inch = 0; //defaulting it to 0
          }
          if ( Array.isArray(stormResultsAccuWeather) ) {
            const snowAccuWeather = stormResultsAccuWeather.find( sraw => sraw.sfb_building_id == svc.site_id);
            svc.SnowAccuWeather = snowAccuWeather === undefined ? 0: snowAccuWeather.totalsnow;
          }
          else {
            svc.SnowAccuWeather = 0;
          }

          svc.ClientPricing = 0;
          svc.ContractorPricing = 0;

          const moment1 = moment.unix(svc.Start);
          const moment2 = moment.unix(svc.Stop);
          const duration = moment.duration(moment2.diff(moment1));
          // const durationInSeconds = duration.asSeconds();
          // const durationInMinutes = duration.asMinutes();
          // const durationInHours = duration.asHours();
          const durationFormattedInHoursAndMinutes = moment.utc(duration.as('milliseconds')).format('HH:mm');
          svc.OutIn = durationFormattedInHoursAndMinutes;

          //ServiceDetails
          svc.ServiceDetails = this.parseServiceDetails(svc.ServiceDetails);


          if (svc.Status == 'REJECTED') {
            //Skip all calculations
            svc.ClientPricingUi = 0;
            svc.ContractorPricingUi = 0;
          }
          else {
            //CLIENT PRICING
            if ( svc.WoID > 0 && svc.ClientNte > 0) {
              if ( !workorderListWhereWorkorderPricingAlreadyLoadedClient.includes(svc.WoID) ) {
                svc.ClientPricing = svc.ClientNte;
                svc.ClientPricingUnit = null;
                workorderListWhereWorkorderPricingAlreadyLoadedClient.push(svc.WoID);
              }
            }
            else {
              //Below Client Pricing Starts
              const filteredPrice = clientPrice.filter(cp=> cp.sps_service_option_trigger == svc.ClientPricingUnit);
              if (svc.ClientPricingUnit == 'HOURS'){

                const filterClientPriceByEquipment = filteredPrice.find( cObj => cObj.sps_equipmentservice_id == svc.equipment_id );
                if (filterClientPriceByEquipment)
                {
                  let price = filterClientPriceByEquipment.length < 1 ? 0 : filterClientPriceByEquipment.sps_price;
                  //Here we check
                  //If service hours and people are 0, we use OutIn as Hours and assume people to be 1 employee
                  if ( svc.Hours < 1 && svc.People < 1 ) {
                    if ( filterClientPriceByEquipment.sps_minimum_minutes > 0 &&  duration.asMinutes() < filterClientPriceByEquipment.sps_minimum_minutes ) {
                      svc.ClientPricing = price * filterClientPriceByEquipment.sps_minimum_minutes/60;
                    } else {
                      svc.ClientPricing = price * duration.asHours();
                    }
                  }
                  else {
                    if ( filterClientPriceByEquipment.sps_minimum_minutes > 0 &&  svc.Hours*60 < filterClientPriceByEquipment.sps_minimum_minutes ) {
                      svc.ClientPricing = price * (filterClientPriceByEquipment.sps_minimum_minutes/60) * svc.People;
                    } else {
                      svc.ClientPricing = price * svc.Hours * svc.People;
                    }
                  }
                }
              }
              else if (svc.ClientPricingUnit == 'DAY'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'WEEK'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'MONTH'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'SEASON'){
                //Here we need to implement Per Season Calculations
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price * svc.snow_inch;
              }
              else if (svc.ClientPricingUnit == 'YEAR'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'EVENT'){
                if (svc.snow_inch === null || svc.snow_inch < 0){
                  svc.ClientPricing = 0;
                } else {
                  let filtered = this.findClosestSnowTrigger(filteredPrice, svc.snow_inch);
                  if ( typeof filtered === 'undefined' ) {
                    //Snow range not found
                    svc.ClientPricing = 0;
                  } else {
                    //Snow range found
                    svc.ClientPricing = filtered.sps_price
                  }
                }
              }
              else if (svc.ClientPricingUnit == 'SERVICE'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'PUSH'){
                if (svc.snow_inch === null || svc.snow_inch < 0){
                  svc.ClientPricing = 0;
                } else {
                  let filtered = this.findClosestSnowTrigger(filteredPrice, svc.snow_inch);
                  if ( typeof filtered === 'undefined' ) {
                    //Snow range not found
                    svc.ClientPricing = 0;
                  } else {
                    //Snow range found
                    //Removing people and hour based on mike comments
                    svc.ClientPricing = filtered.sps_price;
                  }
                }
              }
              else if (svc.ClientPricingUnit == 'VISIT'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'PARTIAL_SERVICE'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'ITEM'){
                let materialPrice = Array.isArray(filteredPrice) && svc?.MaterialId != null
                  ? filteredPrice.find(cp => cp.sps_material_id == svc.MaterialId)
                  : null;

                let withoutMaterialPrice = Array.isArray(filteredPrice) && filteredPrice.length > 0
                  ? filteredPrice[0].sps_price
                  : 0;

                let selectedPrice = materialPrice && parseFloat(materialPrice.sps_price) > 0
                  ? parseFloat(materialPrice.sps_price)
                  : parseFloat(withoutMaterialPrice);

                let price = selectedPrice * parseFloat(svc.MaterialQuantity || 0);

                svc.ClientPricing = price;
              }
              else if (svc.ClientPricingUnit == 'INCH'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ClientPricing = price;
              }
            }

            //CONTRACTOR PRICING
            if ( svc.WoID > 0 && svc.ContractorNte > 0) {
              if ( !workorderListWhereWorkorderPricingAlreadyLoaded.includes(svc.WoID) ) {
                svc.ContractorPricing = svc.ContractorNte;
                workorderListWhereWorkorderPricingAlreadyLoaded.push(svc.WoID);
              }
            }
            else {
              const filteredPrice = contractorPrice.filter(cp=> cp.sps_service_option_trigger == svc.ContractorPricingUnit);
              //Below Contractor Pricing Starts
              if (svc.ContractorPricingUnit == 'HOURS'){
                const filterContractorPriceByEquipment = mainPrice.find( cObj => cObj.sps_equipmentservice_id == svc.equipment_id );
                if (filterContractorPriceByEquipment)
                {
                  let price = filterContractorPriceByEquipment.length < 1 ? 0 : filterContractorPriceByEquipment.sps_price;
                  if ( filterContractorPriceByEquipment.sps_minimum_minutes > 0 &&  svc.Hours*60 < filterContractorPriceByEquipment.sps_minimum_minutes ) {
                    svc.ContractorPricing = price * (filterContractorPriceByEquipment.sps_minimum_minutes/60) * svc.People;
                  }
                  else {
                    // svc.ContractorPricing = price * svc.Hours * svc.People;
                    //Below we are enabling out-in for contractor as well
                    if ( svc.Hours < 1 && svc.People < 1 ) {
                      svc.ContractorPricing = price * duration.asHours();
                    }
                    else {
                      svc.ContractorPricing = price * svc.Hours * svc.People;
                    }
                  }
                }
                else {
                  svc.TradeIdContractor = -1; //Setting to -1 so that contract could not be opened from here since no pricing contract match was found
                  svc.ContractorPricing = this.getPayRateFromContractorProfile(svc, duration) ?? 0;
                }
              }
              else if (svc.ContractorPricingUnit == 'DAY'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'WEEK'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'MONTH'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'SEASON'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'YEAR'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'EVENT'){
                if (svc.snow_inch === null || svc.snow_inch < 0){
                  svc.ContractorPricing = 0;
                } else {
                  let filtered = this.findClosestSnowTrigger(filteredPrice, svc.snow_inch);
                  if ( typeof filtered === 'undefined' ) {
                    //Snow range not found
                    svc.ContractorPricing = 0;
                  } else {
                    //Snow range found
                    svc.ContractorPricing = filtered.sps_price
                  }
                }
              }
              else if (svc.ContractorPricingUnit == 'SERVICE'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'PUSH'){
                if (svc.snow_inch === null || svc.snow_inch < 0){
                  svc.ContractorPricing = 0;
                } else {
                  let filtered = this.findClosestSnowTrigger(filteredPrice, svc.snow_inch);
                  if ( typeof filtered === 'undefined' ) {
                    //Snow range not found
                    svc.ContractorPricing = 0;
                  } else {
                    //Snow range found
                    //Removing people and hour based on mike comments
                    svc.ContractorPricing = filtered.sps_price;
                  }
                }
              }
              else if (svc.ContractorPricingUnit == 'VISIT'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'PARTIAL_SERVICE'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'ITEM'){
                let materialPrice = Array.isArray(filteredPrice) && svc?.MaterialId != null
                  ? filteredPrice.find(cp => cp.sps_material_id == svc.MaterialId)
                  : null;

                let withoutMaterialPrice = Array.isArray(filteredPrice) && filteredPrice.length > 0
                  ? filteredPrice[0].sps_price
                  : 0;

                let selectedPrice = materialPrice && parseFloat(materialPrice.sps_price) > 0
                  ? parseFloat(materialPrice.sps_price)
                  : parseFloat(withoutMaterialPrice);

                let price = selectedPrice * parseFloat(svc.MaterialQuantity || 0);
                svc.ContractorPricing = price;
              }
              else if (svc.ContractorPricingUnit == 'INCH'){
                let price = filteredPrice.length < 1 ? 0 : filteredPrice[0].sps_price;
                svc.ContractorPricing = price;
              }
              else {
                //LOGIC: If there is no pricing found for contractor in contracts, get his on profile payrate, if not found, get his default pay rate for the trade.
                //Here we can check if this particular contractor has pricing on his profile
                //svc.ProviderContactId is contractor id
                //And we are applying this logic for
                //svc.vendor_services_service_type_id
                svc.TradeIdContractor = -1;
                svc.ContractorPricing = this.getPayRateFromContractorProfile(svc, duration) ?? 0;
              }
            }

            svc.ClientPricingUi = svc.ClientPricing;
            svc.ContractorPricingUi = svc.ContractorPricing;
          }

          return svc;
        }

        else {
          svc.id = 0;
          svc.PricingType = "";
          svc.ParentId = null;
          svc.TradeId = "";
          svc.PricingTypeContractor = "";
          svc.ParentIdContractor = null;
          svc.TradeIdContractor = "";
          svc.ClientContract = "";
          svc.ContractorContract = "";
          svc.ClientPricingUnit = "";
          svc.ContractorPricingUnit = "";
          svc.Hours = svc.hours === null ? 0 : svc.hours;
          svc.People = svc.people === null ? 0 : svc.people;
          svc.Price = "";
          svc.Source = svc.swsd_service_source;
          svc.Status = svc.swsd_service_status;
          svc.ClientPricing = 0;
          svc.ContractorPricing = 0;
          svc.ClientPricingUi = 0;
          svc.ContractorPricingUi = 0;
          svc.OutIn = "";
          svc.snow_inch = 0;
          return svc; //
        }
      });

      //after all pricing scenarios have been applied, per event has been applied as per push.
      //Now in final data we need to check matching for per event based on same EVENT NAME, CONTRACT AND SERVICE
      //If any multiple rows pass this criteria, show pricing once in first row and default all others as 0
      let duplicates = this.findDuplicates(pricingResults, ["ClientPricingUnit", "vendor_services_service_type_id", "ClientContractId", "swsd_event"]);
      let contractorDuplicates = this.findDuplicates(pricingResults, ["ContractorPricingUnit", "vendor_services_service_type_id", "ContractorContractId", "swsd_event"]);
      //Now that we have duplicates, lets update data for them
      duplicates.forEach(modifiedItem => {
        const index = pricingResults.findIndex(originalItem => {
          return (
            originalItem.ClientPricingUnit === modifiedItem.ClientPricingUnit &&
            originalItem.vs_service_id === modifiedItem.vs_service_id &&
            originalItem.ClientContractId === modifiedItem.ClientContractId &&
            originalItem.swsd_event === modifiedItem.swsd_event
          );
        });

        if (index !== -1) {
          pricingResults[index] = modifiedItem;
        }
      });
      contractorDuplicates.forEach(modifiedItem => {
        const index = pricingResults.findIndex(originalItem => {
          return (
            originalItem.ContractorPricingUnit === modifiedItem.ContractorPricingUnit &&
            originalItem.vs_service_id === modifiedItem.vs_service_id &&
            originalItem.ContractorContractId === modifiedItem.ContractorContractId &&
            originalItem.swsd_event === modifiedItem.swsd_event
          );
        });

        if (index !== -1) {
          pricingResults[index] = modifiedItem;
        }
      });

      let finalData = pricingResults;
      this.originalData = finalData;

      if (this.tableCollapsed) {
        //Table is collapsed
        let dataTreeStructure = this.convertDataToRooferStructure(finalData);
        this.$nextTick(() => {
          if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
            this.$refs.pricingDashboard.tabulator.replaceData(dataTreeStructure);
          }
        });
      } else {
        this.$nextTick(() => {
          if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
            this.$refs.pricingDashboard.tabulator.replaceData(finalData);
          }
        });
      }

      //Load email data as well
      let emailData = await fetch(`/node/services/email-data`);
      emailData = await emailData.json();
      if (emailData != 0) {
        this.emailBody = emailData.spset_email_body;
        this.chBoxIncludePricingColumn = emailData.spset_show_pricing;
      }
      this.getDataLoader = false;
    },
    findDuplicates(arr, keys) {
      arr.sort((a, b) => b.UnixTime - a.UnixTime);
      const seen = {};
      const duplicates = [];

      arr.forEach(item => {
        // Check if swsd_event is not null, undefined, or just spaces
        if (item.ContractorPricingUnit == 'EVENT' || item.ClientPricingUnit == 'EVENT') {
          if (item.swsd_event != null && item.swsd_event.trim() !== '') {
            const itemKeys = keys.map(key => item[key]);
            const itemKeysString = JSON.stringify(itemKeys);

            if (seen[itemKeysString]) {
              duplicates.push(item);
              duplicates.push(...seen[itemKeysString]);  // Include the previously seen item

              // Update ClientPricing for the current duplicate
              item.ClientPricingUi = 0;
              item.ContractorPricingUi = 0;
            } else {
              seen[itemKeysString] = [item];
            }
          }
        }
      });

      return duplicates;
    },
    findClosestSnowTrigger(array, snow) {
      return array.find(obj => {
        let start = obj.sps_snow_trigger_start;
        let end = obj.sps_snow_trigger_end;
        if (snow >= start && snow <= end) {
          return obj
        }
      });
    },
    findClosestSnowTriggerForSeasonalTier(array, snow_seasonal, snow) {
      return array.find(obj => {
        let start_seasonal = obj.sps_snow_season_lo;
        let end_seasonal = obj.sps_snow_season_hi;
        let start = obj.sps_snow_trigger_start;
        let end = obj.sps_snow_trigger_end;
        if (snow_seasonal >= start_seasonal && snow_seasonal <= end_seasonal && snow >= start && snow <= end) {
          return obj
        }
      });
    },
    getPayRateFromContractorProfile(svc, duration){
      if ( svc.ContractorPersonalProfilePricing && svc.ContractorPersonalProfilePricing.length > 0 ){
        let payRateFiltered =
          svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId
              && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id
              && eR.scsp_equipment_id === svc.equipment_id);

        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id);
        }
        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId );
        }
        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === 0 && eR.scsp_vendor_services_service_type_id === 0 && eR.scsp_equipment_id === 0);
        }

        if ( svc.Hours < 1 && svc.People < 1 ) {
          return payRateFiltered.scsp_price * duration.asHours();
        }
        else {
          return payRateFiltered.scsp_price * svc.Hours * svc.People;
        }
      }
    },
    convertDataToTabulatorTreeStructure(data) {
      const formIds = Array.from(new Set(data.map(dt => dt.FormId)));
      const finalArray = formIds.map(formId => {
        const perFormArray = data.filter(dt => dt.FormId === formId);
        return {
          ...perFormArray[0],
          _children: perFormArray.slice(1),
        };
      });
      return finalArray;
    },
    removeDuplicatesFromArray(array, key) {
      var uniqueValues = {};
      return array.filter(function (item) {
        var value = item[key];
        var isUnique = !uniqueValues[value];
        uniqueValues[value] = true;
        return isUnique;
      });
    },
    primaryButtonCallback() {
      this._formSubmit()
    },
    logout() {
      window.location = myBaseURL + '/index/logout'
    },
    //Below we have helper methods
    deepSearchObject(object, key, predicate) {
      if (object.hasOwnProperty(key) && predicate(key, object[key]) === true) return object

      for (let i = 0; i < Object.keys(object).length; i++) {
        let value = object[Object.keys(object)[i]];
        if (typeof value === "object" && value != null) {
          let o = this.deepSearchObject(object[Object.keys(object)[i]], key, predicate)
          if (o != null) return o
        }
      }
      return null
    },
  },

  computed: {
    currentTimezone(){
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    },
    vendorAccessCode() {
      return myvid;
    },
    humanizedRange() {
      let DateTime = luxon.DateTime;
      if (!this.dateRangePicker.range) return null;
      let startLuxon = DateTime.fromISO(this.dateRangePicker.range.start);
      let endLuxon = DateTime.fromISO(this.dateRangePicker.range.end);
      return `${startLuxon.toFormat('d MMM yyyy hh:mm')} - ${endLuxon.toFormat('d MMM yyyy hh:mm')}`;
    },
    //Permissions
    pagePermissions() {
      return window.userpermissions[this.pageID] || [];
    },
    pagePermissionAdmin() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Admin'); //Add/Edit/Approve/Reject,Email
      return permission ? permission['Value'] : true;
    },
    pagePermissionSuperAdmin() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'SuperAdmin'); //Super Admin
      return permission ? permission['Value'] : true;
    },
    pagePermissionAccounting() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Accounting'); //Accounting
      return permission ? permission['Value'] : true;
    },
    //PermissionsEnd
    overallFetch() {
      return this.pricingProps.loaded && this.pricingProps.componentFetchDone;
    },
    showLogicForShowEmailButton() {
      return this.$refs.pricingDashboard.selectedRowsJson.length > 0;
    },
    currentTimeMinusADay: {
      get() {
        const today = dateFns.startOfDay(new Date());
        return dateFns.format(today, 'MM/DD/YY') + ' 12:00:00 AM';
      }
    },
    currentTime: {
      get() {
        const today = dateFns.endOfDay(new Date());
        return dateFns.format(today, 'MM/DD/YY') + ' 11:59:59 PM';
      }
    },

    mini: {
      get() {
        return this.$store.getters.getMini;
      },
      set(value) {
        this.$store.commit('setMini', value)
      }
    },
    cLogo() {
      return mycompanylogo;
    },
    cName() {
      return mycompanyname;
    },
    initials() {
      return myfname.charAt(0) + mylname.charAt(0);
    },
    contacts: {
      get() {
        return this.$store.getters.getContacts;
      },
    }
  },

  async mounted() {
    EventBus.$on('onCancel', this.onCancel)
    EventBus.$on('onComponentFetchDone', this.onComponentFetchDone)

    this.sDate = this.currentTimeMinusADay;
    this.eDate = this.currentTime;

    let start = moment(this.currentTimeMinusADay).toDate();
    let end = moment(this.currentTime).toDate();
    this.range.start = start
    this.range.end = end;

    let reqClients = await fetch(`${myBaseURL}/node/quickbooks/integrated-contacts`)
    this.clients = await reqClients.json()

    const self = this;
    this.tabulatorConfiguration = {
      dataTree: true,
      dataTreeStartExpanded: false,
      dataTreeSelectPropagate: true,
      rowSelection: "checkbox", // Set row selection mode to "checkbox"
      selectableRowsPersistence: false, //uncheck rows on filter changes
      data: this.tableData, //link data to table
      placeholder: "This table is empty.", //display message to user on empty table
      //footerElement:"<button>Custom Button</button>", //add button to footer
      reactiveData: false, //enable data reactivity
      maxHeight: "100%",
      height: "100%",
      movableColumns: true,
      resizableRows: true, // this option takes a boolean value (default = false)
      downloadRowRange: "selected", //change default selector to selected
      movableRows: false,
      persistence: {
        sort: true,
        filter: true,
        columns: true
      },
      initialSort: [{
        column: "UnixTime",
        dir: "desc"
      }],
      persistenceID: "serviceHistoryTabulator1",
      layout: "fitColumns",
      pagination: "local",
      paginationSize: 100,
      paginationSizeSelector: [100, 250, 500, 1000],
      paginationCounter: "rows",
      printStyled: true, //copy table styling to exported table
      printRowRange: "selected", //change default selector to selected
      headerFilterLiveFilterDelay: 0,
      locale: true,
      langs: {
        en: {
          pagination: {
            page_size: "Rows", //label for the page size select element
            first: "<<", //text for the first page button
            first_title: "First Page", //tooltip text for the first page button
            last: ">>",
            last_title: "Last Page",
            prev: "<",
            prev_title: "Prev Page",
            next: ">",
            next_title: "Next Page",
            counter: {
              showing: "",
              of: "of",
              rows: "rows",
              pages: "pages"
            }
          }
        }
      },
      columns: [
        {
          title: "",
          field: "",
          formatter: "rowSelection",
          titleFormatterParams: {
            rowRange: "active"
          },
          titleFormatter: "rowSelection",
          width: 50,
          hozAlign: "center",
          headerSort: false,
          frozen: true,
          print: false,
          download: false,
        },
        // {
        //   responsive:0,
        //   title: "Checkbox",
        //   cssClass: "custom-checkbox-cell",
        //   formatter: "rowSelection",
        //   titleFormatter: "rowSelection",
        //   width: 30,
        //   resizable: false,
        //   hozAlign: "center",
        //   headerSort: false,
        //   frozen: true,
        //   print: false,
        //   download: false,
        //   cellClick: function (e, cell) {
        //     cell.getRow().toggleSelect();
        //   }
        // },
        //     {
        //       title: "Expand",
        //       field: "Expand",
        //       width: 100,
        //       formatter: function(cell, formatterParams, onRendered) {
        //         return "<i class='fas fa-plus'></i>";
        //       },
        //       cellClick: function (e, cell) {
        //         var row = cell.getRow();
        //         var rowElement = row.getElement();
        //         var rowData = row.getData();
        //         rowData.condition = !rowData.condition;
        //
        //         // Check if the expandable row element already exists
        //         var expandRowElement = rowElement.getElementsByClassName("expand-row")[0];
        //         var iconElement = cell.getElement().querySelector("i");
        //
        //         if (!expandRowElement) {
        //           // Create an expandable row element
        //           expandRowElement = document.createElement("div");
        //           expandRowElement.classList.add("expand-row");
        //           expandRowElement.innerHTML = `
        //   <p>Additional details:</p>
        //   <p>Address: ${rowData.address}</p>
        //   <p>Email: ${rowData.email}</p>
        //   <p>Phone: ${rowData.phone}</p>
        // `;
        //
        //           // Append the expandable row to the main row
        //           rowElement.appendChild(expandRowElement);
        //         }
        //
        //         // Toggle the expandable row on click
        //         if (expandRowElement.style.display === "none") {
        //           expandRowElement.style.display = "block";
        //           rowData.condition = true;
        //           iconElement.classList.remove("fas", "fa-plus"); // Remove the plus icon classes
        //           iconElement.classList.add("fas", "fa-minus"); // Add the minus icon classes
        //         } else {
        //           expandRowElement.style.display = "none";
        //           rowData.condition = false;
        //           iconElement.classList.remove("fas", "fa-minus"); // Remove the minus icon classes
        //           iconElement.classList.add("fas", "fa-plus"); // Add the plus icon classes
        //         }
        //       }
        //
        //     },
        {
          title: "Service",
          field: "Service",
          width: 150,
          columnMenu: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: true,
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          headerHozAlign: "center",
          frozen: true,
          resizable: true,
          formatter: function (cell, formatterParams) {
            const value = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.VendorId == myvtem) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
            else {
              return (
                "<span style='color:#000; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          }
        },
        {
          title: "Status",
          field: "Status",
          accessorDownload: function (value, data, type, params, column) {
            if (value == "INVOICED_BILLED") {
              value = "INVOICED / BILLED"
            }
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              valuesLookup: true,
              clearable: true,
              multiselect: true
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            value = value == null ? '' : value;
            if (value == "Logged - Mobile") {
              cell.getElement().style.backgroundColor = "#1976d2";
              cell.getElement().style.color = "white";
            } else if (value == "PENDING") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            } else if (value == "Logged - Admin") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            } else if (value == "SENT") {
              cell.getElement().style.backgroundColor = "#a707ea";
              cell.getElement().style.color = "white";
            } else if (value == "PostDated - Admin") {
              cell.getElement().style.backgroundColor = "#ff5252";
              cell.getElement().style.color = "white";
            } else if (value.toLowerCase() == "logged") {
              cell.getElement().style.backgroundColor = "#1976d2";
              cell.getElement().style.color = "white";
            } else if (value == "APPROVED") {
              cell.getElement().style.backgroundColor = "#4BB543";
              cell.getElement().style.color = "white";
            }
            else if (value == "LOCKED") {
              cell.getElement().style.backgroundColor = "#2a6525";
              cell.getElement().style.color = "white";
            }
            else if (value == "REJECTED") {
              cell.getElement().style.backgroundColor = "#e79c38";
              cell.getElement().style.color = "white";
            }
            else if (value == "INVOICED") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            }
            else if (value == "BILLED") {
              cell.getElement().style.backgroundColor = "#6f3f82";
              cell.getElement().style.color = "white";
            }
            else if (value == "INVOICED_BILLED") {
              cell.getElement().style.backgroundColor = "#27ae60";
              cell.getElement().style.color = "white";
              value = "INVOICED / BILLED";
            }
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";
            return value;
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Updated By",
          field: "UpdatedBy",
          width: 150,
          headerFilter: "input",
          headerHozAlign: "center",
          hozAlign: "center",
          headerFilterFunc: (headerValue, rowValue, rowData) => {
            if (!headerValue) return true;

            const contact = this.contacts.find(ct => ct.sf_contact_id == rowValue);
            if (!contact) return `${myfname} ${mylname}`.toLowerCase().includes(headerValue.toLowerCase());

            const fullName = `${contact.sf_contact_fname} ${contact.sf_contact_lname}`.toLowerCase();
            return fullName.includes(headerValue.toLowerCase());
          },
          formatter: (cell) => {
            const value = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();

            if (rowData.Status == "APPROVED" || rowData.Status == "REJECTED") {
              const contact = this.contacts.find(ct => ct.sf_contact_id == value);
              return contact ? `${contact.sf_contact_fname} ${contact.sf_contact_lname}` : `${myfname} ${mylname}`;
            }
            else {
              return "";
            }
          },
          accessorDownload: (value, data) => {
            if (data.Status == "APPROVED" || data.Status == "REJECTED") {
              const contact = this.contacts.find(ct => ct.sf_contact_id == value);
              return contact ? `${contact.sf_contact_fname} ${contact.sf_contact_lname}` : `${myfname} ${mylname}`;
            }
            else {
              return "";
            }
          }
        },
        {
          title: "Source",
          field: "Source",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: true,
            clearable: true
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Trade",
          field: "TradeTitle",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: true,
            clearable: true
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Site",
          field: "Site",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerFilterLiveFilter: false,
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let siteId = rowData.site_id;
            if (siteId != null) {
              self.navigateToRouteNewTab('sitesid', siteId);
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value == null) {
              value = "";
            }
            return (
              "<span style='color:#1867c0; font-weight:bold;'>" +
              value +
              "</span>"
            );
          }
        },
        {
          title: "Site ID",
          field: "site_id",
          width: 150,
          visible: false,
          download: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "Address",
          field: "Address",
          width: 200,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "City",
          field: "City",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "State",
          field: "State",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Zip",
          field: "Zip",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Zone",
          field: "Zone",
          width: 170,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Service Details",
          field: "ServiceDetails",
          width: 150,
          headerFilter: "input"
        },
        {
          title: "Internal Manager",
          field: "InternalManager",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerHozAlign: "center",
          headerFilter: "list",
          headerFilterParams: { valuesLookup: true, clearable: true },
        },
        {
          title: "Notes",
          field: "Notes",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            self.addUpdateServiceNotes(rowData, newValue)
          }
        },
        {
          title: "Internal WO#",
          field: "WorkOrder",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let value = cell.getValue();
            if (value != null) {
              let workOrderId = rowData.WorkOrder;
              window.open(myBaseURL + `/home#/workorder-view/${workOrderId}`, '_blank');
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value != null) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          }
        },
        {
          title: "Work Order",
          field: "ExternalWorkOrder",
          sorter: "alphanum",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value != null) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let value = cell.getValue();
            if (value != null) {
              if (rowData.WoSystemId == '2') {
                let url = self.getServiceChannelURL(rowData.ExternalWorkOrder)
                window.open(url, '_blank');
              }
            }
          }
        },
        {
          title: "Form ID",
          field: "ParentFormId",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            self.navigateToRouteNewTab('formbuilderid', cell.getValue());
          },
        },
        {
          title: "Form",
          field: "Form",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let formId = rowData.FormId;
            self.openForm(formId);
          },
          formatter: function (cell, formatterParams, onRendered) {
            let row = cell.getRow();
            let rowData = row.getData();
            let value = cell.getValue();
            let formId = rowData.FormId;

            value = value || '';

            cell.getElement().style.color = "white";
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";

            let prevRow = row.getPrevRow();
            let prevRowData = prevRow ? prevRow.getData() : null;

            if (!prevRowData || prevRowData.FormId !== formId) {
              currentColor = (currentColor === colors[0]) ? colors[1] : colors[0];
            }

            cell.getElement().style.backgroundColor = currentColor;
            return `${value} - ${formId}`;
          },
        },
        {
          title: "Client Unit",
          field: "ClientPricingUnit",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list",
          headerFilterParams: { valuesLookup: true, clearable: true, multiselect: true },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerHozAlign: "center",
          formatter: function (cell, formatterParams) {
            let row = cell.getRow();
            let rowData = row.getData();
            let value = cell.getValue() === null ? '' : cell.getValue();
            if (rowData.VendorId == myvtem){
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
            else {
              return (
                "<span style='color:#000; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.ClientPricingUnit != null && rowData.ParentId !== null && rowData.VendorId == myvtem){
              self.openPricingContract(undefined, row);
            }
          },
        },
        {
          title: "Contractor Unit",
          field: "ContractorPricingUnit",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list",
          headerFilterParams: { valuesLookup: true, clearable: true, multiselect: true },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerHozAlign: "center",
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            return (
              "<span style='color:#1867c0; font-weight:bold;'>" +
              value +
              "</span>"
            );
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.ParentIdContractor !== null) {
              self.openPricingContractForContractor(undefined, row);
            }
          },
        },
        {
          title: "People",
          field: "People",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: (cell) => {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.People = newValue;
              self.applyPeopleToBulk(true, [rowData], newValue);
            }
          },
        },
        {
          title: "Hours",
          field: "Hours",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.Hours = newValue;
              self.applyHoursToBulk(true, [rowData], newValue);
            }
          },
        },
        {
          title: "Out-In",
          field: "OutIn",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            let value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          }
        },
        {
          title: "CheckIn",
          field: "Start",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "daterange",
          resizable: false,
          width: 150,
          formatter: (cell, formatterParams, onRendered) => {
            try {
              let rowData = cell.getRow().getData();
              const timezone = this.useCurrentTimeZone ? this.currentTimezone : rowData.Timezone;
              let dt = luxon.DateTime.fromSeconds(cell.getValue()).setZone(timezone);
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: (value, data, type, params, column)=>{

            if (value != null && value !== "") {
              try {
                const timezone = this.useCurrentTimeZone ? this.currentTimezone : data.Timezone;
                let dt = luxon.DateTime.fromSeconds(value).setZone(timezone);
                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
        },
        {
          title: "CheckOut",
          field: "Stop",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "daterange",
          resizable: false,
          width: 150,
          formatter: (cell, formatterParams, onRendered)=> {
            try {
              let rowData = cell.getRow().getData();
              const timezone = this.useCurrentTimeZone ? this.currentTimezone : rowData.Timezone;
              let dt = luxon.DateTime.fromSeconds(cell.getValue()).setZone(timezone);
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: (value, data, type, params, column)=>{

            if (value != null && value !== "") {
              try {
                const timezone = this.useCurrentTimeZone ? this.currentTimezone : data.Timezone;
                let dt = luxon.DateTime.fromSeconds(value).setZone(timezone);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
        },
        {
          title: "Snow API",
          field: "SnowAccuWeather",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 150,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          }
        },
        {
          title: "Snow*",
          field: "snow_inch",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: (cell) => {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.snow_inch = newValue;
              self.applySnowTotalBulk(true, [rowData], newValue);
            }
          },
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Snow Total",
          field: "snow_seasonal_total",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: true,
          width: 110,
          mutatorData: (value) => value ?? 0,
          editor: "input",
          cellEdited: (cell) => {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            this.snowStorms[rowData.site_id] = newValue;
            let table = row.getTable();
            this.fetchData(false);
          }
        },
        {
          title: "Event",
          field: "swsd_event",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "list",
          headerFilterParams: { valuesLookup: true, clearable: true, multiselect: true },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          resizable: false,
          width: 160,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          editor: "list",
          editorParams: {
            valuesLookup: "active"
          },
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.VendorId == myvtem) {
              return true;
            }
            else {
              return false;
            }
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            self.eventName = newValue;
            self.applyEventBulk(true, [rowData], newValue)
          },
        },
        {
          title: "Client Pricing",
          field: "ClientPricingUi",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            value = `$${parseFloat(value).toFixed(2)}`
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Contractor Pricing",
          field: "ContractorPricingUi",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            value = `$${parseFloat(value).toFixed(2)}`
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Provider",
          field: "ProviderText",
          sorter: "string",
          resizable: true,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "Provider ID",
          field: "contractors",
          width: 150,
          visible: false,
          download: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "Client",
          field: "Client",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "QB Invoice ID",
          field: "swsd_qb_invoice_id",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "QB Bill ID",
          field: "swsd_qb_bill_id",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "Material Unit",
          field: "MaterialQuantity",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let rowData = cell.getRow().getData();
            let value = cell.getValue();
            if ( !isNaN(parseFloat(value)) && parseFloat(value) > 0){
              value = `${value} ${rowData.MaterialUnit}`;
            }else {
              value = "";
            }
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Metadata",
          field: "metadata",
          resizable: true,
          width: 200,
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "ID",
          field: "swsd_vendor_sequence_id",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 100,
        },
        {
          title: "Form Submission ID",
          field: "form_submission_id",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 100,
        },
        {
          title: "Timezone",
          field: "Timezone",
          resizable: true,
          width: 150,
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "daterange",
          formatter: (cell, formatterParams, onRendered) => {
            let value;
            if (this.useCurrentTimeZone){
              value = this.currentTimezone;
            }
            else {
              value = cell.getValue();
            }
            const timezoneToAbbreviation = luxon.DateTime.now().setZone(value);
            return timezoneToAbbreviation.offsetNameShort;
          }
        },
        {
          title: "Date",
          field: "UnixTime",
          headerHozAlign: "center",
          sorter: "number",
          hozAlign: "center",
          headerFilter: "daterange",
          resizable: true,
          frozen: true,
          formatter: (cell, formatterParams, onRendered) => {
            try {
              let rowData = cell.getRow().getData();
              const timezone = this.useCurrentTimeZone ? this.currentTimezone : rowData.Timezone;
              let dt = luxon.DateTime.fromSeconds(cell.getValue()).setZone(timezone);
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: (value, data, type, params, column) => {
            if (value != null && value !== "") {
              try {
                const timezone = this.useCurrentTimeZone ? this.currentTimezone : data.Timezone;
                let dt = luxon.DateTime.fromSeconds(value).setZone(timezone);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
          minWidth: 180,

        }

      ]
    }
    this.fetchData();
  },

  asyncComputed: {
    exportFileName: {
      get() {
        return 'PostDatedServices-' + moment().format("MM-DD-YY")
      }
    },
  }
}
