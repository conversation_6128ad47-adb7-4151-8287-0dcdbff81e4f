import tabulatorDatatable from "../../components/tabulator/tabulator-datatable.js";
import FormSubmit from "../form-submit.js";
import pricingEditor from "../../components/pricing-editor.js";
import postdateddashboard from "../../components/postdated/postdateddashboard.js";
import { EventBus } from "../../eventbus.js";
import rangeDatePicker from "../../components/sitefotos/range-date-picker.js";

let formIdToColorMap = {};
let colors = ["#1867c0", "#dabc12"];
let currentColor = colors[0];

//define column header menu as column visibility toggle
export default {
  template: /*html*/`
    <div id="post-dated-services-manager">
    <v-app-bar
          app
          fixed
          elevate-on-scroll
          clipped-left
          class="mainback"
          style="border-bottom:1px solid #e5e5e5;">
        <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
        <div class="">
          <img src="/images/sitefotos_logo_icon.svg" style="width:44px; height:44px;padding-right:10px;">
        </div>
        <span class="page-titles">{{$route.meta.ptitle}}</span>
        <v-spacer></v-spacer>

      <range-date-picker label="Start End Date" style="width: 300px" v-model="range" :default-range="range"></range-date-picker>
      
      <v-spacer></v-spacer>

        <div class="nav-block">
          <v-img :src="cLogo" max-height="36" contain max-width="72" :alt="cName"
                 style="display: inline-block"></v-img>
          <v-menu offset-y bottom style="max-width: 200px">
            <template v-slot:activator="{ on, attrs }">
              <v-avatar color="purple" size="36" class="ml-2" v-bind="attrs" v-on="on">
                <span class="white--text headline">{{initials}}</span>
              </v-avatar>
            </template>
            <v-list>
              <v-list-item to="/account">
                <v-list-item-title>Settings</v-list-item-title>
              </v-list-item>
              <v-list-item @click="logout()">
                <v-list-item-title>Log Off</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
        <v-menu offset-y :close-on-content-click='false'>
          <template v-slot:activator="{ on }">
            <v-app-bar-nav-icon v-on="on" class="hidden-md-and-up">
              <v-icon>more_vert</v-icon>
            </v-app-bar-nav-icon>
          </template>
          <v-card>
            <v-list dense>
            </v-list>
          </v-card>
        </v-menu>
      </v-app-bar>

    <v-progress-linear indeterminate color="primary" v-if="getDataLoader"></v-progress-linear>
    
    <!-- Gmail-style Select All Banner -->
    <v-alert
      v-if="showSelectAllBanner"
      type="info"
      dense
      outlined
      class="mx-4 mb-2"
      border="left"
    >
      <template v-slot:prepend>
        <v-icon>mdi-checkbox-marked</v-icon>
      </template>
      <span v-if="!selectAllAcrossPagesMode">
        All {{currentPageSelectionCount}} items on this page are selected.
        <v-btn
          text
          small
          color="primary"
          @click="enableSelectAllAcrossPages"
          class="ml-2"
        >
          Select all {{totalRowsCount}} items?
        </v-btn>
      </span>
      <span v-else class="font-weight-bold primary--text">
        All {{totalRowsCount}} items are selected.
        <v-btn
          text
          small
          @click="clearAllSelections"
          class="ml-2"
        >
          Clear selection
        </v-btn>
      </span>
    </v-alert>

    <!-- Job Progress Bar -->
    <v-card v-if="jobId" class="mx-4 mb-2">
      <v-card-title>
        Bulk Update in Progress
      </v-card-title>
      <v-card-text>
        <p>Status: <strong>{{ jobStatus }}</strong></p>
        <v-progress-linear
          v-model="jobProgress"
          :buffer-value="100"
          height="25"
          stream
        >
          {{ jobProgress.toFixed(2) }}%
        </v-progress-linear>
        <div v-if="jobStatus === 'processing' && jobEta !== null" class="mt-2">
            Time Remaining: {{ Math.round(jobEta / 60) }} minutes
        </div>
        <div v-if="jobStatus === 'completed' || jobStatus === 'failed'" class="mt-2">
            Job finished in {{ jobElapsed }} seconds.
        </div>
      </v-card-text>
    </v-card>

    <tabulator-datatable
      v-if="tabulatorReady && !loading && tabulatorConfiguration != undefined"
      ref="pricingDashboard"
      :tabulator-configuration="tabulatorConfiguration"
      title="Service History"
      :primary-button-config='{
        "icon": "mdi-plus",
        "title": "New Services"
      }'
      :show-primary-button="pagePermissionAdmin||pagePermissionSuperAdmin"
      :show-clear-all-filters-button="true"
      :show-download-excel-button="true"
      :show-refresh-button="true"
      :name-of-file-export="exportFileName"
      refresh-type="remote"
      @primaryButtonCallback="primaryButtonCallback"
      @rowSelectionChanged="processSelectionCount"
      @remoteRefresh="fetchData"
      :clear-selections-on-downloads="true"
    >
    <template v-slot:custom-header>
          <v-spacer></v-spacer>
      <v-btn :disabled="!tableData || tableData.length <= 0" color="black" icon @click="collapseTable">
        <v-icon v-if="!tableCollapsed">mdi-arrow-collapse</v-icon>
        <v-icon v-else>mdi-arrow-expand</v-icon>
      </v-btn>
          <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="sendEmailDialog=true;" color="black" :disabled="emailButtonDisabled" title="Send email to contractor">
          <v-icon>mdi-email</v-icon>
        </v-btn>
        <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin"  icon @click="snowApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Apply snow total">
        <v-icon>mdi-snowflake</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="peopleApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Change People">
        <v-icon>mdi-account-multiple</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="hoursApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Change Hours">
        <v-icon>mdi-clock-time-nine-outline</v-icon>
      </v-btn>
      <v-btn v-show="false"  icon @click="" color="black" :disabled="emailButtonDisabled" title="Update Status">
        <v-icon>mdi-grease-pencil</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="eventApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Assign to event">
      <v-icon>mdi-weather-snowy</v-icon>
    </v-btn>

      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" :loading="rejectServicesLoading" icon @click="rejectServices" color="black" :disabled="emailButtonDisabled" title="Reject Services">
        <v-icon>mdi-cancel</v-icon>
      </v-btn>
      
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" :loading="approveServicesLoading" icon @click="approveServices('APPROVED')" color="black" :disabled="emailButtonDisabled" title="Approve Services">
        <v-icon>mdi-check-all</v-icon>
      </v-btn>

      
      <v-btn v-if="pagePermissionSuperAdmin" :loading="approveServicesLoading" icon @click="approveServices('LOCKED')" color="black" :disabled="emailButtonDisabled" title="Approve/Lock Services">
        <v-icon>mdi-lock</v-icon>
      </v-btn>

      <v-btn v-if="pagePermissionAccounting" icon color="black" :disabled="invoiceButtonDisabled" :loading="invoiceLoading" title="Create Invoice" @click="createInvoice">
        <v-icon color="rgba(0,0,54,.54)">sitefotos-quickbooks</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAccounting" icon color="black"  :loading="billLoading"  :disabled="emailButtonDisabled" title="Create Bill" @click="createBill">
        <v-icon color="rgba(0,54,0,.54)">sitefotos-quickbooks</v-icon>
      </v-btn>
      <v-btn v-if=" vendorAccessCode == 'f182c4c2e7b4bd91debd2d0d636becac' && pagePermissionSuperAdmin " color="black" icon title="Postdated Emails" @click="openPostdatedDashboard">
        <v-icon>mdi-email-check</v-icon>
      </v-btn>
    </template>
    </tabulator-datatable>
    <v-dialog persistent v-model="snowApplyDialog" max-width="600px">
    <v-card>
      <v-toolbar class="elevation-0 white">
        <v-toolbar-title>
          <h3 class="headline mb-0">Apply Snow Total</h3>
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-toolbar-items></v-toolbar-items>
        <v-btn icon @click="snowApplyDialog=false">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-text-field type="number" label="Snow Total" v-model="snowTotal" ></v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="snowApplyDialog=false">Cancel</v-btn>
        <v-btn color="primary" :loading="snowTotalApplyLoading" @click="applySnowTotalBulk()">Apply</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog persistent v-model="eventApplyDialog" max-width="600px">
    <v-card>
      <v-toolbar class="elevation-0 white">
        <v-toolbar-title>
          <h3 class="headline mb-0">Apply Event</h3>
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-toolbar-items></v-toolbar-items>
        <v-btn icon @click="eventApplyDialog=false">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-text-field outlined hide-details dense type="text" label="Event Name" v-model="eventName" ></v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="eventApplyDialog=false">Cancel</v-btn>
        <v-btn color="primary" :loading="eventApplyLoading" @click="applyEventBulk()">Apply</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

    <v-dialog v-model="peopleApplyDialog" persistent max-width="600px">
      <v-card>
        <v-toolbar class="elevation-0 white">
          <v-toolbar-title>
            <h3 class="headline mb-0">Apply People</h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items></v-toolbar-items>
          <v-btn icon @click="peopleApplyDialog=false">
            <v-icon>close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-text-field type="number" outlined hide-details dense label="People" v-model="peopleTotal" ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="peopleApplyDialog=false">Cancel</v-btn>
          <v-btn color="primary" :loading="peopleTotalApplyLoading" @click="applyPeopleToBulk()">Apply</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="hoursApplyDialog" persistent max-width="600px">
      <v-card>
        <v-toolbar class="elevation-0 white">
          <v-toolbar-title>
            <h3 class="headline mb-0">Apply Hours</h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items></v-toolbar-items>
          <v-btn icon @click="hoursApplyDialog=false">
            <v-icon>close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-text-field type="number" outlined hide-details dense label="Hours" v-model="hoursTotal" ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="hoursApplyDialog=false">Cancel</v-btn>
          <v-btn color="primary" :loading="hoursTotalApplyLoading" @click="applyHoursToBulk()">Apply</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog persistent v-model="sendEmailDialog" fullscreen>
      <div style="display: flex; align-items: center; justify-content: center; background-color: #00000050; height: 100%">
        <div style="height: 600px; width: 600px;">
          <v-card>
            <v-toolbar class="elevation-0 white">
              <v-toolbar-title>
                <h3 class="headline mb-0">Services Approval Email</h3>
              </v-toolbar-title>
              <v-spacer></v-spacer>
              <v-toolbar-items></v-toolbar-items>
              <v-btn icon @click="sendEmailDialog=false; resetDate();">
                <v-icon>close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text>
              <v-col class="mt-5 pa-0">
                <span style="color: black;">Hi [CONTRACTOR],</span>
                <v-textarea
                    style="position: relative"
                    class="mt-5"
                    rows="4"
                    v-model="emailBody"
                    label="Email Text"
                    outlined hide-details dense
                >
                </v-textarea>
                <div class="mt-3" style="display: flex; flex-direction: row; align-items: center; height: 20px;justify-content: end;">
                  <span>
                      <v-checkbox v-model="chBoxSaveEmailBodyAsTemplate" hide-details>
                        <template v-slot:label>
                          <div class='mb-4'>
                            Save Template
                          </div>
                        </template>
                      </v-checkbox>
                    </span>
                </div>
                <v-date-picker
                    is-range
                    mode="dateTime"
                    v-model="dateRangePicker.range"
                    :model-config="dateRangePicker.modelConfig">
                  <template v-slot="{ inputValue, inputEvents, togglePopover }">
                    <div class="flex items-center" style="font-size: 12px;">
                      <v-btn style="text-align: left; height: 36px; width: 100%;" text class="pa-0 v-label input-date theme--light mt-5" @click="togglePopover()">
                        <span style="margin-left: 6px; text-transform: initial; font-size: 0.8rem; font-weight: 500; letter-spacing: normal; border-bottom: 1px solid rgb(0 0 0 / 42%); padding-bottom: 10px; display: flex; justify-content: space-between; width: 100%;">
                          <span style="margin-top: 11px; " v-if="dateRangePicker.range == null">Checkin Checkout Range</span>
                          <span style="margin-top: 11px; color: rgba(0, 0, 0, 0.87); font-weight: 500" v-else>{{humanizedRange}}</span>
                          <v-icon class='mt-2'>arrow_drop_down</v-icon>
                        </span>
                      </v-btn>
                    </div>
                  </template>
                </v-date-picker>
                <br><span class="mt-3" style="color: black;">Any changes or additions are subject to admin approval by {{cName}}</span>
                <br><span style="text-decoration: underline; color: blue; cursor: pointer;">View Service History</span>
              </v-col>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-checkbox v-model="chBoxIncludePricingColumn"  class="mr-5">
                <template v-slot:label>
                  <div class='mb-4'>
                    Include Pricing Column
                  </div>
                </template>
              </v-checkbox>
              <v-btn color="primary" :loading="emailButtonLoading" @click="sendReportInEmail" depressed tile>Send</v-btn>
            </v-card-actions>
          </v-card>
        </div>
      </div>
    </v-dialog>

    <v-dialog scrollable v-model="formSubmitDialog" persistent max-width="500px">
      <form-submit :selectedSiteID="siteIDForFormSubmit" source="ADMIN" device-model="SERVICEHISTORY" @closeFormSubmit="closeFormSubmit($event)"></form-submit>
    </v-dialog>
    
    <v-dialog :key="pricingProps.buildingId" v-model="pricingProps.show" max-width="95vw" :width=" !overallFetch ? '10vw':'95vw' " persistent scrollable>
      <pricing-editor
          v-if="pricingProps.loaded"
          v-bind:pricing-type="pricingProps.pricingType"
          :contract-type=pricingProps.contractType
          :copy-mode="pricingProps.copyMode"
          :parent-id="pricingProps.parentId"
          :trade-id="pricingProps.tradeId"
          :pricingType="pricingProps.pricingType"
          :buildingId="pricingProps.buildingId"
          :slaData="[]"
          :calculatedPricingFromDb="pricingProps.pricingJson"
          :selectedEventRange="[]"
          :customForecasts="[]"
          :serviceLevels="[]"
          :eventRanges="[]"
          :materialPricing="[]"
          :servicePricing="[]"
          :disable-role-selector="true"
          @onCancel="onCancel"
          @onComponentFetchDone="onComponentFetchDone">
      </pricing-editor>
      <v-row v-else style="background-color: white;" justify="center">
        <v-col align="center">
          <v-progress-circular indeterminate></v-progress-circular>
        </v-col>
      </v-row>
    </v-dialog>
    <template>
      
      <v-dialog v-model="info.vDialog" width="500">
        <v-card>
          <v-card-title>{{info.actionPerformed}}</v-card-title>
          <v-card-text>
            <p>{{info.successRows}}</p>
            <p>{{info.failedRows}}</p>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" tile @click="info.vDialog=false">Done</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <v-dialog persistent v-model="dialogPostdatedDashboard" fullscreen :hide-overlay="$vuetify.breakpoint.smAndDown" width="85vw" transition="dialog-bottom-transition">
        <postdateddashboard @dismissPostdated="dialogPostdatedDashboard=false"/>
      </v-dialog>
</template>

    <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
    :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
    {{ snackbar.text }}
    <v-btn color="pink" text @click="snackbar.snackbar = false">
      Close
    </v-btn>
  </v-snackbar>
    </div>
  `,
  components: {
    tabulatorDatatable,
    FormSubmit,
    pricingEditor,
    postdateddashboard,
    rangeDatePicker
  },

  created: function () {

  },

    beforeDestroy() {
    // Clean up event listeners
    EventBus.$off('onCancel', this.onCancel);
    EventBus.$off('onComponentFetchDone', this.onComponentFetchDone);
    
    // Clear polling interval if component is destroyed
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }
  },

  data: function () {
    return {
      tabulatorReady: false, // Added for controlled initialization
      range: {
        start: null,
        end: null,
      },
      dialogPostdatedDashboard: false,
      dateRangePicker: {
        modelConfig: {
          start: {
            type: 'string',
            mask: 'iso',
          },
          end: {
            type: 'string',
            mask: 'iso',
          },
        },
        range: null,
      },
      info: {
        vDialog: false,
        actionPerformed: null,
        successRows: null,
        failedRows: null
      },
      pageID: 24,
      pricingProps: {
        tradeId: 0,
        parentId: 0,
        buildingId: 0,
        show: false,
        loaded: true,
        pricingType: '',
        contractType: 'Snow Removal',
        pricingJson: {},
        componentFetchDone: false,
        copyMode: false
      },

      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'right',
        mode: '',
        timeout: 6000,
        text: ''
      },
      chBoxIncludePricingColumn: true,
      chBoxSaveEmailBodyAsTemplate: true,
      emailBody: 'Please click the link below to view and edit and/or approve all services performed by you and your crews on the sites. Any changes or additions are subject to admin approval.',
      sendEmailDialog: false,
      tableCollapsed: false,
      //FORMSUBMIT VARIABLES
      formSubmitDialog: false,
      siteIDForFormSubmit: false,
      clients: [],

      peopleTotalApplyLoading: false,
      peopleApplyDialog: false,
      peopleTotal: null,

      hoursTotalApplyLoading: false,
      hoursApplyDialog: false,
      hoursTotal: null,

      snowTotalApplyLoading: false,
      snowApplyDialog: false,
      snowTotal: null,

      eventApplyLoading: false,
      eventApplyDialog: false,
      eventName: null,


      rejectServicesLoading: false,
      getDataLoader: false,
      sDate: null,
      eDate: null,
      emailButtonDisabled: true,
      invoiceButtonDisabled: true,
      approveServicesButtonDisabled: true,
      emailButtonLoading: false,
      approveServicesLoading: false,
      billLoading: false,
      invoiceLoading: false,
      loading: false,
      tabulatorConfiguration: {},
      tableData: [

      ],
      originalData: [], // This will no longer hold the full dataset for server-side paging
      filterValues: {}, // To store dynamic filter values
      currentPageUncollapsedData: [],
      
      // Gmail-style select all state variables
      selectAllAcrossPagesMode: false,
      totalRowsCount: 0,
      currentPageSelectionCount: 0,
      showSelectAllBanner: false,
      currentFiltersForCrossPageSelection: null, // Store filters when in cross-page mode
      currentSortForCrossPageSelection: null, // Store sort when in cross-page mode
      
      // Job polling state
      jobId: null,
      jobStatus: null,
      jobProgress: 0,
      pollingInterval: null,
      jobEta: null, // Estimated time remaining in seconds
      jobElapsed: null, // Elapsed time in seconds
      }
    },
  watch: {
    range: {
        handler: 'fetchData', // Re-fetch data when date range changes
        deep: true
    },
    snowApplyDialog(val) {

    this.snowTotal = '';

    },
    
    // Watch for table data changes to auto-clear cross-page selections
    tableData: {
      handler() {
        // Clear selections when table data changes (e.g., page navigation, filtering)
        if (this.selectAllAcrossPagesMode) {
          this.onPageNavigation();
        }
      },
      deep: false
    }
  },

  methods: {
    startPolling(jobId) {
      this.jobId = jobId;
      this.jobStatus = 'processing';
      this.jobProgress = 0;
      this.jobEta = null;
      this.jobElapsed = null;

      this.pollingInterval = setInterval(async () => {
        try {
          const response = await fetch(`/node/services/job-status/${this.jobId}`);
          if (response.ok) {
            const job = await response.json();
            this.jobStatus = job.status;
            this.jobProgress = job.percentage;
            this.jobEta = job.etaSeconds;
            this.jobElapsed = job.elapsedSeconds;

            if (job.status === 'completed' || job.status === 'completed_with_errors' || job.status === 'failed') {
              clearInterval(this.pollingInterval);
              this.pollingInterval = null;

              // Show results in the same dialog as current-page operations
              this.showJobCompletionDialog(job);

              this.fetchData(); // Refresh data after job completion
              setTimeout(() => {
                  this.jobId = null; // Hide the progress bar after a delay
              }, 10000);

            }
          } else {
            console.error('Error fetching job status:', response.statusText);
            this.$refs.pricingDashboard.showAlert(`Error fetching job status.`, 'error');
            clearInterval(this.pollingInterval);
             this.pollingInterval = null;
          }
        } catch (error) {
          console.error('Error in pollJobStatus:', error);
          this.$refs.pricingDashboard.showAlert('An error occurred while polling for job status.', 'error');
          clearInterval(this.pollingInterval);
           this.pollingInterval = null;
        }
      }, 5000); // Poll every 5 seconds
    },

    openPostdatedDashboard(){
      this.dialogPostdatedDashboard = true;
    },
    resetDate() {
      this.dateRangePicker.range = null;
    },
    showInfoDialog(actionPerformed,actionSuccess, actionFailed){
      this.info.actionPerformed = actionPerformed;
      this.info.successRows = actionSuccess;
      this.info.failedRows = actionFailed;
      this.info.vDialog = true;
    },

    showJobCompletionDialog(job) {
      let actionPerformed = 'Bulk Operation Summary';
      let successMessage = '';
      let failureMessage = '';

      if (job.status === 'failed') {
        actionPerformed = 'Bulk Operation Failed';
        failureMessage = job.message || 'The operation failed due to an error.';
      } else {
        // Determine operation type from job data
        if (job.message && job.message.includes('snow')) {
          actionPerformed = 'Snow Total Update Summary';
        } else if (job.message && job.message.includes('Event')) {
          actionPerformed = 'Event Assignment Summary';
        } else if (job.message && job.message.includes('People')) {
          actionPerformed = 'People Update Summary';
        } else if (job.message && job.message.includes('Hours')) {
          actionPerformed = 'Hours Update Summary';
        } else if (job.message && job.message.includes('approved')) {
          actionPerformed = 'Service Approval Summary';
        } else if (job.message && job.message.includes('rejected')) {
          actionPerformed = 'Service Rejection Summary';
        } else if (job.message && job.message.includes('locked')) {
          actionPerformed = 'Service Lock Summary';
        } else {
          actionPerformed = 'Bulk Update Summary';
        }

        if (job.updated > 0) {
          successMessage = `${job.updated} service(s) updated successfully.`;
        }

        if (job.skipped > 0) {
          failureMessage = `${job.skipped} service(s) were skipped due to business logic restrictions (locked/invalid status).`;
        }

        if (job.status === 'completed_with_errors' && job.errors) {
          if (failureMessage) {
            failureMessage += '\n';
          }
          failureMessage += `Some errors occurred during processing.`;
        }
      }

      this.showInfoDialog(actionPerformed, successMessage, failureMessage);
    },
    openPricingContractForContractor(e, row) {
      let rowData = row.getData();
      if (rowData.TradeIdContractor !== -1){
        this.pricingProps.buildingId = rowData.site_id;
        this.pricingProps.pricingType = rowData.PricingTypeContractor;
        this.pricingProps.parentId = rowData.ParentIdContractor;
        this.pricingProps.tradeId = rowData.TradeIdContractor;
        this.pricingProps.copyMode = false;
        this.pricingProps.show = true;
      }
    },
    openPricingContract(e, row) {
      let rowData = row.getData();
      this.pricingProps.buildingId = rowData.site_id;
      this.pricingProps.pricingType = rowData.PricingType;
      this.pricingProps.parentId = rowData.ParentId;
      this.pricingProps.tradeId = rowData.TradeId;
      this.pricingProps.copyMode = false;
      this.pricingProps.show = true;
    },
    onComponentFetchDone() {
      this.pricingProps.componentFetchDone = true;
    },
    onCancel: async function () {
      this.pricingProps = {
        tradeId: 0,
        parentId: 0,
        buildingId: 0,
        show: false,
        loaded: true,
        pricingType: '',
        contractType: 'Snow Removal',
        pricingJson: {},
        componentFetchDone: false,
        copyMode: false
      }
    },
    parseCollapsedRows(selectedRows) {
      if (this.tableCollapsed) {
        // If view is collapsed, use the stored uncollapsed data for the current page
        let dataToFilter = this.currentPageUncollapsedData;
        if (!dataToFilter || dataToFilter.length === 0) {
          // Fallback or error handling if currentPageUncollapsedData is empty
          // This might happen if collapseTable wasn't called or data wasn't populated.
          // For now, returning the selectedRows as is, or you might want to return an empty array
          // or show an error, depending on desired behavior.
          console.warn("currentPageUncollapsedData is empty in parseCollapsedRows while table is collapsed.");
          return selectedRows; // Or handle more gracefully
        }
        let selectedRowsIds = selectedRows.map(od => od.FormId);
        // Filter the current page's uncollapsed data to get the full objects for selected FormIds
        let filteredData = dataToFilter.filter(od => selectedRowsIds.includes(od.FormId));
        return filteredData;
      } else {
        // If table is not collapsed, selectedRows already contains the full data objects
        return selectedRows;
      }
    },

    getServiceChannelURL(id) {
      return `https://www.servicechannel.com/sc/wo/Workorders/index?id=${id}`
    },
    async collapseTable() {
      this.tableCollapsed = !this.tableCollapsed;
      let currentTableData = this.$refs.pricingDashboard.tabulator.getData("active"); // Get current page data

      if (this.tableCollapsed) {
        // Store the current page's uncollapsed data before converting
        this.currentPageUncollapsedData = [...currentTableData]; // Use spread to clone
        let dataTreeStructure = this.convertDataToRooferStructure(currentTableData);
        this.$refs.pricingDashboard.tabulator.replaceData(dataTreeStructure); // Replace current view
        this.$refs.pricingDashboard.showAlert("Table is now in collapsed view (current page data)", "success");
      } else {
        // Clear the stored uncollapsed data when expanding
        this.currentPageUncollapsedData = [];
        // To revert, we need to re-fetch from server or restore previous page data if cached
        // For simplicity now, just re-trigger a fetch for the current page parameters
        // This might not preserve the exact "uncollapsed" state if filters/sort changed,
        // but it's a starting point for server-side data.
        this.$refs.pricingDashboard.tabulator.setData();
        this.$refs.pricingDashboard.showAlert("Table is now in expanded view", "success");
      }
    },

    convertDataToRooferStructure(data) { // This now operates on page data if table is collapsed
      const formIds = Array.from(new Set(data.map(dt => dt.FormId)));
      const finalArray = formIds.map(formId => {
        const perFormArray = data.filter(dt => dt.FormId === formId);

        const combinedObject = perFormArray.reduce((acc, curr) => {
          for (const key in curr) {
            if (acc.hasOwnProperty(key)) {
              if (key === 'Service' || key === 'Notes') {
                acc[key] += `${acc[key] ? ', ' : ''}${curr[key]}`;
              } else if (key === 'ContractorPricing' || key === 'ClientPricing' || key === 'snow_inch' || key === 'People' || key === 'Hours') {
                acc[key] += curr[key]
              }
            } else {
              acc[key] = curr[key];
            }
          }
          return acc;
        }, {});

        return combinedObject;
      });
      return finalArray;
    },
    isStringEmptyOrWhitespace(str) {
      return str.trim().length === 0;
    },
    //Update add service related notes
    async addUpdateServiceNotes(rowData, notes) {
      let serviceId = rowData.swsd_id;
      await fetch(`/node/services/service-notes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          serviceId: serviceId,
          notes: notes
        })
      });
      this.$refs.pricingDashboard.showAlert("Notes have been updated", "success");
    },
    //FORM SUBMIT RELATED METHODS START
    _formSubmit: function (event) {
      this.formSubmitDialog = true;
      this.siteIDForFormSubmit = 0;
    },
    closeFormSubmit: async function (shouldRefreshData) {
      this.formSubmitDialog = false;
      await this.fetchData();
    },
    //FORM SUBMIT RELATED METHODS END

    async applyEventBulk(singleUpdate = false, singleRow = []) {
      try {
        this.eventApplyLoading = true;
        
        // Handle single update case
        if (singleUpdate && singleRow.length && singleRow[0].VendorId == myvtem) {
          let ids = [singleRow[0].swsd_id];
          
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              eventName: this.eventName
            })
          });
          this.getDataLoader = false;
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          this.newData = [singleRow[0]].map(item => ({...item, swsd_event: this.eventName}));
          await this.updateData(this.newData);
          this.showInfoDialog('Event Assignment', `1 service updated successfully.`, '');
          return;
        }
        
        // Validate event name for bulk operations
        if (!this.eventName || this.eventName.length < 4) {
          alert("Event Name should contain at least 4 characters!");
          return;
        }
        
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch('/node/services/update-submitted-service', {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              eventName: this.eventName
            })
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
              // Clear cross-page selection after starting job
              this.clearAllSelections();
            } else {
              // For immediate completion (small datasets), show dialog
              this.showInfoDialog(
                'Event Assignment Summary',
                `${result.updated || 0} service(s) updated successfully.`,
                result.skipped ? `${result.skipped} service(s) were skipped due to business logic restrictions.` : ''
              );
              // Refresh the table data to reflect changes
              this.fetchData();
              this.clearAllSelections();
            }
          } else {
            this.$refs.pricingDashboard.showAlert("Error updating event assignments. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let ids = [];
          let validRows = [];
          let failedRows = [];
          
          let rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
          
          if (ids.length > 0) {
            this.getDataLoader = true;
            const response = await fetch(`/node/services/update-submitted-service`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                ids: ids,
                eventName: this.eventName
              })
            });
            this.getDataLoader = false;
            
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.newData = validRows.map(item => ({...item, swsd_event: this.eventName}));
            await this.updateData(this.newData);
            
            // Show the result dialog
            if (validRows.length > 0 || failedRows.length > 0) {
              let message = 'Event Assignment';
              let successMessage = '';
              let failureMessage = '';

              if (validRows.length > 0) {
                successMessage += `\n${validRows.length} service(s) updated successfully.`;
              }
              if (failedRows.length > 0) {
                failureMessage += `\n${failedRows.length} service(s) is/are locked and cannot be updated.`;
              }

              this.showInfoDialog(message, successMessage, failureMessage);
            }
          } else {
            this.showInfoDialog('Service Update Summary', '', 'No services were eligible for updating.');
          }
        }
      } catch (error) {
        console.error("An error occurred:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while updating event assignments.", "error");
      } finally {
        this.eventApplyLoading = false;
        this.eventApplyDialog = false;
        this.eventName = null;
      }
    },
    async applySnowTotalBulk(singleUpdate = false, singleRow = [], snowTPassed = 0) {
      try {
        this.snowTotalApplyLoading = true;
        
        // Handle single update case
        if (singleUpdate && singleRow[0].VendorId == myvtem) {
          let snowTotal = snowTPassed;
          if (isNaN(snowTotal)) {
            alert("Snow Total should be a number");
            return;
          }
          
          let ids = [singleRow[0].swsd_id];
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              snowTotal: snowTotal
            })
          });
          this.getDataLoader = false;
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          let newData = [singleRow[0]].map(item => ({...item, snow_inch: snowTotal}));
          await this.updateData(newData);
          this.showInfoDialog('Snow Total Update Summary', `1 service snow total updated successfully.`, '');
          return;
        }
        
        // Validate snow total input for bulk operations
        if (isNaN(this.snowTotal)) {
          alert("Snow Total should be a number");
          return;
        }
        
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch('/node/services/update-submitted-service', {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              snowTotal: this.snowTotal
            })
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
              // Clear cross-page selection after starting job
              this.clearAllSelections();
            } else {
              // For immediate completion (small datasets), show dialog
              this.showInfoDialog(
                'Snow Total Update Summary',
                `${result.updated || 0} service(s) updated successfully.`,
                result.skipped ? `${result.skipped} service(s) were skipped due to business logic restrictions.` : ''
              );
              // Refresh the table data to reflect changes
              this.fetchData();
              this.clearAllSelections();
            }
          } else {
            this.$refs.pricingDashboard.showAlert("Error updating snow totals. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let ids = [];
          let snowTotal = this.snowTotal;
          let validRows = [];
          let failedRows = [];
          
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
          
          if (ids.length > 0) {
            this.getDataLoader = true;
            const response = await fetch(`/node/services/update-submitted-service`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                ids: ids,
                snowTotal: snowTotal
              })
            });
            this.getDataLoader = false;
            
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            let newData = validRows.map(item => ({...item, snow_inch: snowTotal}));
            await this.updateData(newData);
            
            // Show the result dialog
            let message = 'Snow Total Update Summary';
            let successMessage = '';
            let failureMessage = '';

            if (validRows.length > 0) {
              successMessage += `\n${validRows.length} service(s) snow total(s) updated successfully.`;
            }
            if (failedRows.length > 0) {
              failureMessage += `\n${failedRows.length} service(s) snow total(s) is/are locked and cannot be updated.`;
            }

            this.showInfoDialog(message, successMessage, failureMessage);
          } else {
            this.showInfoDialog('Snow Total Update Summary', '', 'No snow totals were eligible for updating.');
          }
        }
      } catch (error) {
        console.error("An error occurred:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while updating snow totals.", "error");
      } finally {
        this.snowTotalApplyLoading = false;
        this.snowApplyDialog = false;
        this.snowTotal = null;
      }
    },
    async updateData(data) {

      // This method's reliance on this.originalData needs to change for server-side paging.
      // For now, after a server update, we will re-fetch the current page.
      // Individual row updates can be done with tabulator.updateData([{id:..., ...}])
      // but a full page refresh is simpler after bulk operations.
      if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
          this.$refs.pricingDashboard.tabulator.setData(); // Re-trigger AJAX call for current page
      }
      // The old logic of manipulating this.originalData is removed.
    },
    async applyPeopleToBulk(singleUpdate = false, singleRow = [], peoplePassed = 0) {
      try {
        this.peopleTotalApplyLoading = true;
        
        // Handle single update case
        if (singleUpdate && singleRow.length == 1 && singleRow[0].VendorId == myvtem) {
          let people = peoplePassed;
          let ids = [singleRow[0].swsd_id];
          
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              people: people
            })
          });
          this.getDataLoader = false;
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          let newData = [singleRow[0]].map(item => ({...item, People: people}));
          await this.updateData(newData);
          this.showInfoDialog('People Update Summary', `1 service people count updated successfully.`, '');
          return;
        }
        
        // Validate people input for bulk operations
        if (isNaN(this.peopleTotal)) {
          alert("People should be a number");
          return;
        }
        
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch('/node/services/update-submitted-service', {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              people: this.peopleTotal
            })
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
              // Clear cross-page selection after starting job
              this.clearAllSelections();
            } else {
              // For immediate completion (small datasets), show dialog
              this.showInfoDialog(
                'People Update Summary',
                `${result.updated || 0} service(s) updated successfully.`,
                result.skipped ? `${result.skipped} service(s) were skipped due to business logic restrictions.` : ''
              );
              // Refresh the table data to reflect changes
              this.fetchData();
              this.clearAllSelections();
            }
          } else {
            this.$refs.pricingDashboard.showAlert("Error updating people counts. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let ids = [];
          let people = this.peopleTotal;
          let validRows = [];
          let failedRows = [];
          
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
          
          if (ids.length > 0) {
            this.getDataLoader = true;
            const response = await fetch(`/node/services/update-submitted-service`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                ids: ids,
                people: people
              })
            });
            this.getDataLoader = false;
            
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            let newData = validRows.map(item => ({...item, People: people}));
            await this.updateData(newData);
            
            // Show the result dialog
            let message = 'People Update Summary';
            let successMessage = '';
            let failureMessage = '';

            if (validRows.length > 0) {
              successMessage += `\n${validRows.length} service(s) people total(s) updated successfully.`;
            }
            if (failedRows.length > 0) {
              failureMessage += `\n${failedRows.length} service(s) people total(s) is/are locked and cannot be updated.`;
            }

            this.showInfoDialog(message, successMessage, failureMessage);
          } else {
            this.showInfoDialog('People Update Summary', '', 'No people totals were eligible for updating.');
          }
        }
      } catch (error) {
        console.error("An error occurred:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while updating people counts.", "error");
      } finally {
        this.peopleTotalApplyLoading = false;
        this.peopleApplyDialog = false;
        this.peopleTotal = null;
      }
    },

    async applyHoursToBulk(singleUpdate = false, singleRow = [], hoursPassed = 0) {
      try {
        this.hoursTotalApplyLoading = true;
        
        // Handle single update case
        if (singleUpdate && singleRow.length == 1 && singleRow[0].VendorId == myvtem) {
          let hours = hoursPassed;
          let ids = [singleRow[0].swsd_id];
          
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              hours: hours
            })
          });
          this.getDataLoader = false;
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          this.newData = [singleRow[0]].map(item => ({...item, Hours: hours}));
          await this.updateData(this.newData);
          this.showInfoDialog('Hours Update Summary', `1 service hours updated successfully.`, '');
          return;
        }
        
        // Validate hours input for bulk operations
        if (isNaN(this.hoursTotal)) {
          alert("Hours should be a number");
          return;
        }
        
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch('/node/services/update-submitted-service', {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              hours: this.hoursTotal
            })
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
              // Clear cross-page selection after starting job
              this.clearAllSelections();
            } else {
              // For immediate completion (small datasets), show dialog
              this.showInfoDialog(
                'Hours Update Summary',
                `${result.updated || 0} service(s) updated successfully.`,
                result.skipped ? `${result.skipped} service(s) were skipped due to business logic restrictions.` : ''
              );
              // Refresh the table data to reflect changes
              this.fetchData();
              this.clearAllSelections();
            }
          } else {
            this.$refs.pricingDashboard.showAlert("Error updating hours. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let ids = [];
          let hours = this.hoursTotal;
          let validRows = [];
          let failedRows = [];
          
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
          
          if (ids.length > 0) {
            this.getDataLoader = true;
            const response = await fetch(`/node/services/update-submitted-service`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                ids: ids,
                hours: hours
              })
            });
            this.getDataLoader = false;
            
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.newData = validRows.map(item => ({...item, Hours: hours}));
            await this.updateData(this.newData);
            
            // Show the result dialog
            let message = 'Hours Update Summary';
            let successMessage = '';
            let failureMessage = '';

            if (validRows.length > 0) {
              successMessage += `\n${validRows.length} service(s) hour(s) updated successfully.`;
            }
            if (failedRows.length > 0) {
              failureMessage += `\n${failedRows.length} service(s) hour(s) is/are locked and cannot be updated.`;
            }

            this.showInfoDialog(message, successMessage, failureMessage);
          } else {
            this.showInfoDialog('Hours Update Summary', '', 'No hours were eligible for updating.');
          }
        }
      } catch (error) {
        console.error("An error occurred:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while updating hours.", "error");
      } finally {
        this.hoursTotalApplyLoading = false;
        this.hoursApplyDialog = false;
        this.hoursTotal = null;
      }
    },

    processSelectionCount() {
      if (this.selectAllAcrossPagesMode) {
        // In cross-page mode, buttons are enabled based on total count
        this.invoiceButtonDisabled = false;
        this.emailButtonDisabled = false;
        this.currentPageSelectionCount = this.totalRowsCount;
        this.showSelectAllBanner = true;
        return;
      }
      
      const selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      const filteredSelectedRows = selectedRows.filter(row => row.VendorId == myvtem);
      const currentPageSize = this.$refs.pricingDashboard.tabulator.getData().length;
      
      // Store current page selection count
      this.currentPageSelectionCount = selectedRows.length;
      
      // Show banner when all items on current page are selected (but not in cross-page mode)
      this.showSelectAllBanner = !this.selectAllAcrossPagesMode && 
        selectedRows.length > 0 && 
        selectedRows.length === currentPageSize;
      
      // Enable buttons based on selection
      this.invoiceButtonDisabled = !(selectedRows.length >= 1);
      this.emailButtonDisabled = !(filteredSelectedRows.length >= 1);
    },
    
    // Gmail-style selection methods
    enableSelectAllAcrossPages() {
      // Debug log current state
      console.log('enableSelectAllAcrossPages called - Current totalRowsCount:', this.totalRowsCount);
      
      // If totalRowsCount is 0, try to get it from the tabulator pagination info
      if (this.totalRowsCount === 0 && this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
        const pageData = this.$refs.pricingDashboard.tabulator.getPageMax();
        console.log('Tabulator page data:', pageData);
      }
      
      this.selectAllAcrossPagesMode = true;
      this.showSelectAllBanner = true;
      
      // Store current filter and sort state for cross-page operations
      // Extract only the necessary properties to avoid circular references
      const filters = this.$refs.pricingDashboard.tabulator.getFilters();
      const sorters = this.$refs.pricingDashboard.tabulator.getSorters();
      
      // Clean filters to avoid circular references
      this.currentFiltersForCrossPageSelection = filters.map(filter => ({
        field: filter.field,
        type: filter.type,
        value: filter.value
      }));
      
      // Clean sorters to avoid circular references
      this.currentSortForCrossPageSelection = sorters.map(sorter => ({
        column: sorter.field,
        dir: sorter.dir
      }));
      
      // Update button states
      this.processSelectionCount();
      
      this.$refs.pricingDashboard.showAlert(`All ${this.totalRowsCount} items are now selected across all pages.`, "info");
    },
    
    clearAllSelections() {
      // Exit cross-page selection mode
      this.selectAllAcrossPagesMode = false;
      this.showSelectAllBanner = false;
      this.currentFiltersForCrossPageSelection = null;
      this.currentSortForCrossPageSelection = null;
      
      // Clear tabulator selections
      if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
        this.$refs.pricingDashboard.tabulator.deselectRow();
      }
      
      // Update button states
      this.processSelectionCount();
    },
    
    onPageNavigation() {
      // Automatically clear selections when page changes (Gmail behavior)
      if (this.selectAllAcrossPagesMode) {
        this.clearAllSelections();
        this.$refs.pricingDashboard.showAlert("Selection cleared due to page navigation.", "info");
      }
    },
    async approveServices(status) {
      this.approveServicesLoading = true;
      
      try {
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch('/node/services/service-status', {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              status: status
            })
          });

          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
              // Clear cross-page selection after starting job
              this.clearAllSelections();
            } else {
              // For immediate completion (small datasets), show dialog
              const statusLabel = status === 'APPROVED' ? 'approved' : (status === 'LOCKED' ? 'locked' : status.toLowerCase());
              const actionTitle = status === 'APPROVED' ? 'Service Approval Summary' :
                                 (status === 'LOCKED' ? 'Service Lock Summary' : 'Service Status Update Summary');

              this.showInfoDialog(
                actionTitle,
                `${result.affectedCount || 0} service(s) ${statusLabel} successfully.`,
                result.skipped ? `${result.skipped} service(s) were skipped due to business logic restrictions.` : ''
              );
              // Refresh the table data to reflect status changes
              this.fetchData();
              this.clearAllSelections();
            }
          } else {
            this.$refs.pricingDashboard.showAlert("Error updating service status. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let validRows = [];
          let failedRows = [];

          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              validRows.push(sR);  // Rows that pass the conditions
            } else {
              failedRows.push(sR);  // Rows that fail the conditions
            }
          });
          
          await this.acceptUserChangesNew(validRows, failedRows, status);
        }
      } catch (error) {
        console.error("Error in approveServices:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while updating service status.", "error");
      } finally {
        this.approveServicesLoading = false;
      }
    },
    async acceptUserChangesNew(validRows, failedRows, status) {
      let resp = await fetch('/node/services/service-status', {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "rows": validRows,
          "status": status
        })
      });
      
      if (resp.ok) {
        this.newData = validRows.map(item => ({...item, Status: status}))
        await this.updateData(this.newData);
        
        if (status == 'APPROVED') {
          let message = 'Service Approval Summary';
          let successMessage = '';
          let failureMessage = '';
          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) approved successfully.`;
          }
          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) is/are locked and cannot be approved, as action is restricted for locked services.`;
          }
          this.showInfoDialog('Service Approval Summary', successMessage, failureMessage);
        }
        else if (status == 'LOCKED') {
          this.showInfoDialog('Service Lock Summary', `${validRows.length} services have been locked successfully.`, '');
        }
      } else {
        this.$refs.pricingDashboard.showAlert("Error updating service status.", "error");
      }
    },

    async createBill() {
      this.billLoading = true;
      try {
        let body = {
          selectAllAcrossPages: this.selectAllAcrossPagesMode,
          filters: this.currentFiltersForCrossPageSelection,
          sorters: this.currentSortForCrossPageSelection,
          start: dateFns.parse(this.range.start).getTime() / 1000,
          end: dateFns.parse(this.range.end).getTime() / 1000,
        };

        if (!this.selectAllAcrossPagesMode) {
          let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          if (selectedRows.length < 1) {
            this.$refs.pricingDashboard.showAlert("Please select some rows", "error");
            return;
          }
          body.selectedRows = this.parseCollapsedRows(selectedRows);
        }

        const response = await fetch('/node/quickbooks/bulk-bill', {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body)
        });

        if (response.ok) {
          const result = await response.json();
          if (result.jobId) {
            this.startPolling(result.jobId);
          } else {
            this.$refs.pricingDashboard.showAlert(result.message || 'Bills created successfully.', 'success');
            this.fetchData();
          }
          this.clearAllSelections(); 
        } else {
            const error = await response.json();
            this.$refs.pricingDashboard.showAlert(error.message || "Error creating bills.", "error");
        }
      } catch (error) {
        console.error("Error in createBill:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while creating bills.", "error");
      } finally {
        this.billLoading = false;
      }
    },

    async createInvoice() {
      this.invoiceLoading = true;
      try {
        let body = {
          selectAllAcrossPages: this.selectAllAcrossPagesMode,
          filters: this.currentFiltersForCrossPageSelection,
          sorters: this.currentSortForCrossPageSelection,
          start: dateFns.parse(this.range.start).getTime() / 1000,
          end: dateFns.parse(this.range.end).getTime() / 1000,
        };

        if (!this.selectAllAcrossPagesMode) {
          let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          if (selectedRows.length < 1) {
            this.$refs.pricingDashboard.showAlert("Please select some rows", "error");
            return;
          }
          body.selectedRows = this.parseCollapsedRows(selectedRows);
        }

        const response = await fetch('/node/quickbooks/bulk-invoice', {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body)
        });

        if (response.ok) {
          const result = await response.json();
          if (result.jobId) {
            this.startPolling(result.jobId);
          } else {
            this.$refs.pricingDashboard.showAlert(result.message || 'Invoices created successfully.', 'success');
            this.fetchData();
          }
          this.clearAllSelections();
        } else {
            const error = await response.json();
            this.$refs.pricingDashboard.showAlert(error.message || "Error creating invoices.", "error");
        }
      } catch (error) {
        console.error("Error in createInvoice:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while creating invoices.", "error");
      } finally {
        this.invoiceLoading = false;
      }
    },

    async rejectServices() {
      this.rejectServicesLoading = true;
      
      try {
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch('/node/services/service-status', {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              status: "REJECTED"
            })
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
              // Clear cross-page selection after starting job
              this.clearAllSelections();
            } else {
              // For immediate completion (small datasets), show dialog
              this.showInfoDialog(
                'Service Rejection Summary',
                `${result.affectedCount || 0} service(s) rejected successfully.`,
                result.skipped ? `${result.skipped} service(s) were skipped due to business logic restrictions.` : ''
              );
              // Refresh the table data to reflect status changes
              this.fetchData();
              this.clearAllSelections();
            }
          } else {
            this.$refs.pricingDashboard.showAlert("Error rejecting services. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let message = 'Service Processing Summary';
          let successMessage = '';
          let failureMessage = '';

          // Filtering valid and failed rows
          let validRows = [];
          let failedRows = [];

          let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem &&
              sR.Status !== 'APPROVED' &&
              sR.Status !== 'INVOICED' &&
              sR.Status !== 'BILLED' &&
              sR.Status !== 'INVOICED_BILLED' &&
              sR.Status !== 'LOCKED') {
              validRows.push(sR);  // Rows that meet the conditions
            } else {
              failedRows.push(sR);  // Rows that fail the conditions
            }
          });

          // Generating success and failure messages
          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) rejected successfully.`;
          }

          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) could not be rejected due to one of the following statuses: Approved, Invoiced, Billed, or Locked.`;
          }

          if (validRows.length > 0) {
            await fetch('/node/services/service-status', {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                "rows": validRows,
                "status": "REJECTED"
              })
            });
            
            this.newData = validRows.map(item => ({...item, Status: "REJECTED"}))
            await this.updateData(this.newData);

            this.showInfoDialog('Service Processing Summary', successMessage, failureMessage);
          }
          else {
            this.$refs.pricingDashboard.showAlert("Services cannot be rejected.", "success");
          }
        }
      } catch (error) {
        console.error("Error in rejectServices:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while rejecting services.", "error");
      } finally {
        this.rejectServicesLoading = false;
      }
    },
    async sendBreadcrumb(type, date, accessCode, siteId, profileID, uuid, contractorEmail) {
      let params = new URLSearchParams({
        'type': type,
        'accessCode': accessCode,
        'email': contractorEmail,
        'dt': date,
        'deviceType': 'WEB',
        'deviceModel': 'ADMINDASHBOARD',
        'lat': 0,
        'lon': 0,
        'uuid': uuid,
        'siteid': siteId,
        'profileID': profileID //i guess this is a form id
      });
      let request = await fetch("http://localhost/vpics/uploadbreadcrumb", {
        "headers": {
          "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
        },
        "body": params,
        "method": "POST",
      });
      let data = await request.json();
    },
    isNullOrEmpty(value) {
      return value === null ||
        value === undefined ||
        (typeof value === 'string' && value.trim() === '');
    },
    async sendReportInEmail() {
      this.emailButtonLoading = true;
      
      try {
        const saveTemplate = this.chBoxSaveEmailBodyAsTemplate;
        const includePricingColumn = this.chBoxIncludePricingColumn;
        
        if (this.selectAllAcrossPagesMode) {
          // Cross-page selection mode - use backend to process all filtered services
          const response = await fetch(`/node/services/send-services-postdated`, {
            method: "post",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              selectAllAcrossPages: true,
              filters: this.currentFiltersForCrossPageSelection,
              sorters: this.currentSortForCrossPageSelection,
              start: dateFns.parse(this.range.start).getTime() / 1000,
              end: dateFns.parse(this.range.end).getTime() / 1000,
              checkIn: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.start).unix(),
              checkOut: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.end).unix(),
              saveTemplate: saveTemplate,
              includePricingColumn: includePricingColumn,
              emailBody: this.emailBody
            })
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.jobId) {
              this.startPolling(result.jobId);
            } else {
              this.$refs.pricingDashboard.showAlert(`Emails have been sent to ${result.emailsSent || 'selected'} services.`, "success");
              // Refresh the table data to reflect status changes
              this.fetchData();
            }
            // Clear cross-page selection after successful operation
            this.clearAllSelections();
          } else {
            this.$refs.pricingDashboard.showAlert("Error sending emails. Please try again.", "error");
          }
        } else {
          // Regular selection mode - process currently selected rows
          let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;
          selectedRows = selectedRows.filter(sr => sr.ProviderId != myvtem && sr.VendorId == myvtem && sr.Status != 'LOCKED');
          
          if (selectedRows.length > 0) {
            if (this.tableCollapsed) {
              // If view is collapsed, use the stored uncollapsed data for the current page
              let dataToFilter = this.currentPageUncollapsedData;
              if (!dataToFilter || dataToFilter.length === 0) {
                console.warn("currentPageUncollapsedData is empty in sendReportInEmail while table is collapsed.");
                this.$refs.pricingDashboard.showAlert("Cannot send email for collapsed view without current page data.", "error");
                return;
              }
              let selectedRowsIds = selectedRows.map(od => od.FormId);
              selectedRows = dataToFilter.filter(od => selectedRowsIds.includes(od.FormId));
            }

            await fetch(`/node/services/send-services-postdated`, {
              method: "post",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                selectedRows: selectedRows,
                startDate: moment(this.range.start).format('YYYY/MM/DD HH:mm:ss'),
                endDate: moment(this.range.end).format('YYYY/MM/DD HH:mm:ss'),
                checkIn: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.start).unix(),
                checkOut: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.end).unix(),
                saveTemplate: saveTemplate,
                includePricingColumn: includePricingColumn,
                emailBody: this.emailBody
              })
            });
            
            this.$refs.pricingDashboard.showAlert("Emails have been sent.", "success");
            this.newData = selectedRows.map(item => ({...item, Status: "SENT"}))
            await this.updateData(this.newData);
          } else {
            this.$refs.pricingDashboard.showAlert("Emails not sent. All services selected are read-only.", "success");
          }
        }
        
        this.sendEmailDialog = false;
      } catch (error) {
        console.error("Error in sendReportInEmail:", error);
        this.$refs.pricingDashboard.showAlert("An error occurred while sending emails.", "error");
      } finally {
        this.emailButtonLoading = false;
      }
    },
    openForm(formId) {
      window.open(`${myBaseURL}/vpics/printform?fid=${formId}`, '_blank')
    },
    navigateToRouteNewTab(name, id) {
      const routeURL = this.$router.resolve({ name: name, params: { id } }).href;
      window.open(routeURL, '_blank');
    },
    navigateToRoute(name, id) {
      this.$router.push({ name: name, params: { id } });
    },
    parseServiceDetails(value){
      if (value != null) {
        var co = phpUnserialize(value);
        if (Array.isArray(co)) {
          return co.join(", ")
        } else {
          //return co.substring(1, co.length - 1)
          return co;
        }
      }
    },
    async fetchFilterDropdownValuesInternal() { // Renamed and modified
        if (!this.range.start || !this.range.end) return;
        // this.getDataLoader = true; // Removed: Managed by fetchData
        try {
            const start = dateFns.parse(this.range.start).getTime() / 1000;
            const end = dateFns.parse(this.range.end).getTime() / 1000;
            
            const response = await fetch(`/node/services/service-history-filter-values?start=${start}&end=${end}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const filterValuesFromServer = await response.json();
            this.filterValues = filterValuesFromServer; // Store for potential direct use
        } catch (error) {
            console.error("Error fetching filter values:", error);
            if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) { // Check ref existence
                this.$refs.pricingDashboard.showAlert("Error fetching filter options.", "error");
            } else {
                console.error("pricingDashboard ref not available for filter options error alert.");
            }
            throw error; // Re-throw to be caught by Promise.all in fetchData
        }
        // finally { // Removed
        //     this.getDataLoader = false; // Removed
        // }
    },
    async fetchData() {
      this.getDataLoader = true;
      try {
        const fetchFiltersPromise = this.fetchFilterDropdownValuesInternal();

        const mainDataAndEmailPromise = async () => {
       
          if (!this.tabulatorReady) {
            this.tabulatorReady = true;
            // Need to wait for next tick for the component to render before accessing ref
            await this.$nextTick();
            console.log('Tabulator ready, triggering initial data load');
            // Now trigger the initial data load
            if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
              await this.$refs.pricingDashboard.tabulator.setData();
            }
          } else {
            if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
             
              await this.$refs.pricingDashboard.tabulator.setData();
            }
          }

          // Load email data
          try {
            let emailDataResp = await fetch(`/node/services/email-data`);
            if (emailDataResp.ok) {
                const emailData = await emailDataResp.json();
                if (emailData && emailData != 0) {
                    this.emailBody = emailData.spset_email_body;
                    this.chBoxIncludePricingColumn = emailData.spset_show_pricing;
                }
            }
          } catch (e) {
            console.error("Error fetching email data", e);
           
          }
        };

        await Promise.all([fetchFiltersPromise, mainDataAndEmailPromise()]);

      } catch (error) {
        
        console.error("Error during concurrent data fetching in fetchData:", error);
        if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
            this.$refs.pricingDashboard.showAlert("An error occurred while loading data.", "error");
        } else {
            console.error("pricingDashboard ref not available for general data loading error alert.");
        }
      } finally {
        this.getDataLoader = false;
      }
    },
    findDuplicates(arr, keys) {
      arr.sort((a, b) => b.UnixTime - a.UnixTime);
      const seen = {};
      const duplicates = [];

      arr.forEach(item => {
        // Check if swsd_event is not null, undefined, or just spaces
        if (item.ContractorPricingUnit == 'EVENT' || item.ClientPricingUnit == 'EVENT') {
          if (item.swsd_event != null && item.swsd_event.trim() !== '') {
            const itemKeys = keys.map(key => item[key]);
            const itemKeysString = JSON.stringify(itemKeys);

            if (seen[itemKeysString]) {
              duplicates.push(item);
              duplicates.push(...seen[itemKeysString]);  // Include the previously seen item

              // Update ClientPricing for the current duplicate
              item.ClientPricingUi = 0;
              item.ContractorPricingUi = 0;
            } else {
              seen[itemKeysString] = [item];
            }
          }
        }
      });

      return duplicates;
    },
    findClosestSnowTrigger(array, snow) {
      return array.find(obj => {
        let start = obj.sps_snow_trigger_start;
        let end = obj.sps_snow_trigger_end;
        if (snow >= start && snow <= end) {
          return obj
        }
      });
    },
    getPayRateFromContractorProfile(svc, duration){
      if ( svc.ContractorPersonalProfilePricing && svc.ContractorPersonalProfilePricing.length > 0 ){
        let payRateFiltered =
          svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId
              && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id
              && eR.scsp_equipment_id === svc.equipment_id);

        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id);
        }
        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId );
        }
        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === 0 && eR.scsp_vendor_services_service_type_id === 0 && eR.scsp_equipment_id === 0);
        }

        if ( svc.Hours < 1 && svc.People < 1 ) {
          return payRateFiltered.scsp_price * duration.asHours();
        }
        else {
          return payRateFiltered.scsp_price * svc.Hours * svc.People;
        }
      }
    },
    convertDataToTabulatorTreeStructure(data) {
      const formIds = Array.from(new Set(data.map(dt => dt.FormId)));
      const finalArray = formIds.map(formId => {
        const perFormArray = data.filter(dt => dt.FormId === formId);
        return {
          ...perFormArray[0],
          _children: perFormArray.slice(1),
        };
      });
      return finalArray;
    },
    removeDuplicatesFromArray(array, key) {
      var uniqueValues = {};
      return array.filter(function (item) {
        var value = item[key];
        var isUnique = !uniqueValues[value];
        uniqueValues[value] = true;
        return isUnique;
      });
    },
    primaryButtonCallback() {
      this._formSubmit()
    },
    logout() {
      window.location = myBaseURL + '/index/logout'
    },
    //Below we have helper methods
    deepSearchObject(object, key, predicate) {
      if (object.hasOwnProperty(key) && predicate(key, object[key]) === true) return object

      for (let i = 0; i < Object.keys(object).length; i++) {
        let value = object[Object.keys(object)[i]];
        if (typeof value === "object" && value != null) {
          let o = this.deepSearchObject(object[Object.keys(object)[i]], key, predicate)
          if (o != null) return o
        }
      }
      return null
    },
  },

  computed: {
    vendorAccessCode() {
      return myvid;
    },
    humanizedRange() {
      let DateTime = luxon.DateTime;
      if (!this.dateRangePicker.range) return null;
      let startLuxon = DateTime.fromISO(this.dateRangePicker.range.start);
      let endLuxon = DateTime.fromISO(this.dateRangePicker.range.end);
      return `${startLuxon.toFormat('d MMM yyyy hh:mm')} - ${endLuxon.toFormat('d MMM yyyy hh:mm')}`;
    },
    //Permissions
    pagePermissions() {
      return window.userpermissions[this.pageID] || [];
    },
    pagePermissionAdmin() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Admin'); //Add/Edit/Approve/Reject,Email
      return permission ? permission['Value'] : true;
    },
    pagePermissionSuperAdmin() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'SuperAdmin'); //Super Admin
      return permission ? permission['Value'] : true;
    },
    pagePermissionAccounting() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Accounting'); //Accounting
      return permission ? permission['Value'] : true;
    },
    //PermissionsEnd
    overallFetch() {
      return this.pricingProps.loaded && this.pricingProps.componentFetchDone;
    },
    showLogicForShowEmailButton() {
      return this.$refs.pricingDashboard.selectedRowsJson.length > 0;
    },
    currentTimeMinusADay: {
      get() {
        const today = dateFns.startOfDay(new Date());
        return dateFns.format(today, 'MM/DD/YY') + ' 12:00:00 AM';
      }
    },
    currentTime: {
      get() {
        const today = dateFns.endOfDay(new Date());
        return dateFns.format(today, 'MM/DD/YY') + ' 11:59:59 PM';
      }
    },

    mini: {
      get() {
        return this.$store.getters.getMini;
      },
      set(value) {
        this.$store.commit('setMini', value)
      }
    },
    cLogo() {
      return mycompanylogo;
    },
    cName() {
      return mycompanyname;
    },
    initials() {
      return myfname.charAt(0) + mylname.charAt(0);
    }
  },

  async mounted() {
    console.log('===== POSTDATED-NEW.JS MOUNTED =====', new Date().toISOString());
    console.log('File version: with alert and enhanced logging');
    
    EventBus.$on('onCancel', this.onCancel)
    EventBus.$on('onComponentFetchDone', this.onComponentFetchDone)

    this.sDate = this.currentTimeMinusADay;
    this.eDate = this.currentTime;

    let start = moment(this.currentTimeMinusADay).toDate();
    let end = moment(this.currentTime).toDate();
    this.range.start = start
    this.range.end = end;

    let reqClients = await fetch(`${myBaseURL}/node/quickbooks/integrated-contacts`)
    this.clients = await reqClients.json()

    
    const self = this; // Keep reference to `this` for ajaxParams
    this.tabulatorConfiguration = {
      // dataTree: true, // Server-side data doesn't typically work well with dataTree out-of-the-box
      // dataTreeStartExpanded: false,
      // dataTreeSelectPropagate: true,
      rowSelection: "checkbox",
      selectableRowsPersistence: false,
      // data: this.tableData, // REMOVED: Data will be fetched via AJAX
      ajaxURL: "/node/services/service-history-dashboard2", // ADDED
      ajaxConfig: "POST",
      ajaxContentType: "json",
      ajaxParams: function() { // ADDED: To include dynamic date range and timezone
          const start = dateFns.parse(self.range.start).getTime() / 1000;
          const end = dateFns.parse(self.range.end).getTime() / 1000;
          let timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          console.log('=== AJAX PARAMS CALLED ===', {
              start: start,
              startDate: self.range.start,
              end: end,
              endDate: self.range.end,
              tz: timeZone,
              timestamp: new Date().toISOString()
          });
          return {
              start: start,
              end: end,
              tz: timeZone
          };
      },
      ajaxRequestFunc: async (url, config, params) => {
        console.log('=== AJAX REQUEST FUNC CALLED ===', {
          url,
          config,
          params,
          timestamp: new Date().toISOString()
        });

        const response = await fetch(url, {
          method: config.method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(params)
        });

        const data = await response.json();
        console.log('=== AJAX REQUEST RESPONSE ===', {
          total: data.total,
          dataLength: data.data?.length,
          timestamp: new Date().toISOString()
        });
        
        // IMPORTANT: Process the response here to update totalRowsCount
        // Store table data and total count
        self.tableData = data.data || [];
        
        // Capture total rows count - check multiple possible field names
        const totalCount = data.total || data.recordsTotal || data.totalRecords || 0;
        self.totalRowsCount = totalCount;
        
        // Debug logging to check response structure
        console.log('=== TOTAL ROWS COUNT UPDATED ===', {
            totalRowsCount: self.totalRowsCount,
            responseTotal: data.total,
            dataLength: self.tableData.length,
            timestamp: new Date().toISOString()
        });
        
        // Alert to verify update (remove in production)
        if (totalCount > 0) {
            console.log(`%c✓ Total rows count updated to: ${totalCount}`, 'color: green; font-weight: bold;');
        }
        
        // Force Vue reactivity update
        self.$forceUpdate();
        
        return data;
      },

      placeholder: "This table is empty.",
      reactiveData: false,
      maxHeight: "100%",
      height: "100%",
      movableColumns: true,
      resizableRows: true, // this option takes a boolean value (default = false)
      downloadRowRange: "selected", //change default selector to selected
      movableRows: false,
      persistence: {
        sort: true,
        filter: true,
        columns: true
      },
      initialSort: [{
        column: "UnixTime",
        dir: "desc"
      }],
      persistenceID: "serviceHistoryTabulator1",
      layout: "fitColumns",
      pagination: "remote", 
      paginationMode: "remote", 
      filterMode: "remote", 
      sortMode: "remote", 
      paginationSize: 100,
      paginationSizeSelector: [100, 250, 500, 1000],
      paginationCounter: "rows",
      printStyled: true,
      printRowRange: "selected", //change default selector to selected
      headerFilterLiveFilterDelay: 0,
      locale: true,
      langs: {
        en: {
          pagination: {
            page_size: "Rows", //label for the page size select element
            first: "<<", //text for the first page button
            first_title: "First Page", //tooltip text for the first page button
            last: ">>",
            last_title: "Last Page",
            prev: "<",
            prev_title: "Prev Page",
            next: ">",
            next_title: "Next Page",
            counter: {
              showing: "",
              of: "of",
              rows: "rows",
              pages: "pages"
            }
          }
        }
      },
      columns: [
        {
          title: "",
          field: "",
          formatter: "rowSelection",
          titleFormatterParams: {
            rowRange: "active"
          },
          titleFormatter: "rowSelection",
          width: 50,
          hozAlign: "center",
          headerSort: false,
          frozen: true,
          print: false,
          download: false,
        },
        // {
        //   responsive:0,
        //   title: "Checkbox",
        //   cssClass: "custom-checkbox-cell",
        //   formatter: "rowSelection",
        //   titleFormatter: "rowSelection",
        //   width: 30,
        //   resizable: false,
        //   hozAlign: "center",
        //   headerSort: false,
        //   frozen: true,
        //   print: false,
        //   download: false,
        //   cellClick: function (e, cell) {
        //     cell.getRow().toggleSelect();
        //   }
        // },
        //     {
        //       title: "Expand",
        //       field: "Expand",
        //       width: 100,
        //       formatter: function(cell, formatterParams, onRendered) {
        //         return "<i class='fas fa-plus'></i>";
        //       },
        //       cellClick: function (e, cell) {
        //         var row = cell.getRow();
        //         var rowElement = row.getElement();
        //         var rowData = row.getData();
        //         rowData.condition = !rowData.condition;
        //
        //         // Check if the expandable row element already exists
        //         var expandRowElement = rowElement.getElementsByClassName("expand-row")[0];
        //         var iconElement = cell.getElement().querySelector("i");
        //
        //         if (!expandRowElement) {
        //           // Create an expandable row element
        //           expandRowElement = document.createElement("div");
        //           expandRowElement.classList.add("expand-row");
        //           expandRowElement.innerHTML = `
        //   <p>Additional details:</p>
        //   <p>Address: ${rowData.address}</p>
        //   <p>Email: ${rowData.email}</p>
        //   <p>Phone: ${rowData.phone}</p>
        // `;
        //
        //           // Append the expandable row to the main row
        //           rowElement.appendChild(expandRowElement);
        //         }
        //
        //         // Toggle the expandable row on click
        //         if (expandRowElement.style.display === "none") {
        //           expandRowElement.style.display = "block";
        //           rowData.condition = true;
        //           iconElement.classList.remove("fas", "fa-plus"); // Remove the plus icon classes
        //           iconElement.classList.add("fas", "fa-minus"); // Add the minus icon classes
        //         } else {
        //           expandRowElement.style.display = "none";
        //           rowData.condition = false;
        //           iconElement.classList.remove("fas", "fa-minus"); // Remove the minus icon classes
        //           iconElement.classList.add("fas", "fa-plus"); // Add the plus icon classes
        //         }
        //       }
        //
        //     },
        {
          title: "Service",
          field: "Service",
          width: 150,
          columnMenu: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "list",
          headerFilterParams: () => { 
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => self.filterValues['Service'] || []
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          headerHozAlign: "center",
          frozen: true,
          resizable: true,
          formatter: function (cell, formatterParams) {
            const value = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.VendorId == myvtem) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
            else {
              return (
                "<span style='color:#000; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          }
        },
        {
          title: "Status",
          field: "Status",
          accessorDownload: function (value, data, type, params, column) {
            if (value == "INVOICED_BILLED") {
              value = "INVOICED / BILLED"
            }
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: () => {
            return { 
              valuesLookup: () => self.filterValues['Status'] || [],
              clearable: true,
              multiselect: true
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            value = value == null ? '' : value;
            if (value == "Logged - Mobile") {
              cell.getElement().style.backgroundColor = "#1976d2";
              cell.getElement().style.color = "white";
            } else if (value == "PENDING") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            } else if (value == "Logged - Admin") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            } else if (value == "SENT") {
              cell.getElement().style.backgroundColor = "#a707ea";
              cell.getElement().style.color = "white";
            } else if (value == "PostDated - Admin") {
              cell.getElement().style.backgroundColor = "#ff5252";
              cell.getElement().style.color = "white";
            } else if (value.toLowerCase() == "logged") {
              cell.getElement().style.backgroundColor = "#1976d2";
              cell.getElement().style.color = "white";
            } else if (value == "APPROVED") {
              cell.getElement().style.backgroundColor = "#4BB543";
              cell.getElement().style.color = "white";
            }
            else if (value == "LOCKED") {
              cell.getElement().style.backgroundColor = "#2a6525";
              cell.getElement().style.color = "white";
            }
            else if (value == "REJECTED") {
              cell.getElement().style.backgroundColor = "#e79c38";
              cell.getElement().style.color = "white";
            }
            else if (value == "INVOICED") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            }
            else if (value == "BILLED") {
              cell.getElement().style.backgroundColor = "#6f3f82";
              cell.getElement().style.color = "white";
            }
            else if (value == "INVOICED_BILLED") {
              cell.getElement().style.backgroundColor = "#27ae60";
              cell.getElement().style.color = "white";
              value = "INVOICED / BILLED";
            }
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";
            return value;
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Source",
          field: "Source",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['Source'] || [],
            clearable: true
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Trade",
          field: "TradeTitle",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['TradeTitle'] || [],
            clearable: true
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Site",
          field: "Site",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerFilterLiveFilter: false,
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let siteId = rowData.site_id;
            if (siteId != null) {
              self.navigateToRouteNewTab('sitesid', siteId);
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value == null) {
              value = "";
            }
            return (
              "<span style='color:#1867c0; font-weight:bold;'>" +
              value +
              "</span>"
            );
          }
        },
        {
          title: "Site ID",
          field: "site_id",
          width: 150,
          visible: false,
          download: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "Address",
          field: "Address",
          width: 200,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "City",
          field: "City",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "State",
          field: "State",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Zip",
          field: "Zip",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Zone",
          field: "Zone",
          width: 170,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list", 
          headerFilterParams: {
            valuesLookup: () => self.filterValues['Zone'] || [],
            clearable: true
          },
          headerHozAlign: "center",
        },
        {
          title: "Service Details",
          field: "ServiceDetails",
          width: 150,
          headerFilter: "input"
        },
        {
          title: "Internal Manager",
          field: "InternalManager",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerHozAlign: "center",
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['InternalManager'] || [],
            clearable: true
            },
        },
        {
          title: "Notes",
          field: "Notes",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            self.addUpdateServiceNotes(rowData, newValue)
          }
        },
        {
          title: "Internal WO#",
          field: "WorkOrder",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let value = cell.getValue();
            if (value != null) {
              let workOrderId = rowData.WorkOrder;
              window.open(myBaseURL + `/home#/workorder-view/${workOrderId}`, '_blank');
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value != null) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          }
        },
        {
          title: "Work Order",
          field: "ExternalWorkOrder",
          sorter: "alphanum",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value != null) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let value = cell.getValue();
            if (value != null) {
              if (rowData.WoSystemId == '2') {
                let url = self.getServiceChannelURL(rowData.ExternalWorkOrder)
                window.open(url, '_blank');
              }
            }
          }
        },
        {
          title: "Form ID",
          field: "ParentFormId",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            self.navigateToRouteNewTab('formbuilderid', cell.getValue());
          },
        },
        {
          title: "Form",
          field: "Form",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let formId = rowData.FormId;
            self.openForm(formId);
          },
          formatter: function (cell, formatterParams, onRendered) {
            let row = cell.getRow();
            let rowData = row.getData();
            let value = cell.getValue();
            let formId = rowData.FormId;

            value = value || '';

            cell.getElement().style.color = "white";
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";

            let prevRow = row.getPrevRow();
            let prevRowData = prevRow ? prevRow.getData() : null;

            if (!prevRowData || prevRowData.FormId !== formId) {
              currentColor = (currentColor === colors[0]) ? colors[1] : colors[0];
            }

            cell.getElement().style.backgroundColor = currentColor;
            return `${value} - ${formId}`;
          },
        },
        {
          title: "Client Unit",
          field: "ClientPricingUnit",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list",
          headerFilterParams: {
              valuesLookup: () => self.filterValues['ClientPricingUnit'] || [],
            clearable: true,
            multiselect: true
            },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (Array.isArray(headerValue) && headerValue.includes(rowValue)) { 
              return true;
            }
             return false; 
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerHozAlign: "center",
          formatter: function (cell, formatterParams) {
            let row = cell.getRow();
            let rowData = row.getData();
            let value = cell.getValue() === null ? '' : cell.getValue();
            if (rowData.VendorId == myvtem){
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
            else {
              return (
                "<span style='color:#000; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.ClientPricingUnit != null && rowData.ParentId !== null && rowData.VendorId == myvtem){
              self.openPricingContract(undefined, row);
            }
          },
        },
        {
          title: "Contractor Unit",
          field: "ContractorPricingUnit",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['ContractorPricingUnit'] || [],
            clearable: true,
            multiselect: true
            },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerHozAlign: "center",
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (Array.isArray(headerValue) && headerValue.includes(rowValue)) { 
              return true;
            }
            return false; 
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            return (
              "<span style='color:#1867c0; font-weight:bold;'>" +
              value +
              "</span>"
            );
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.ParentIdContractor !== null) {
              self.openPricingContractForContractor(undefined, row);
            }
          },
        },
        {
          title: "People",
          field: "People",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: (cell) => {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.People = newValue;
              self.applyPeopleToBulk(true, [rowData], newValue);
            }
          },
        },
        {
          title: "Hours",
          field: "Hours",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.Hours = newValue;
              self.applyHoursToBulk(true, [rowData], newValue);
            }
          },
        },
        {
          title: "Out-In",
          field: "OutIn",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            let value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          }
        },
        {
          title: "CheckIn",
          field: "Start",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "daterange",
          resizable: false,
          width: 150,
          formatter: function(cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: function(value, data, type, params, column){

            if (value != null && value !== "") {
              try {

                let dt = luxon.DateTime.fromSeconds(value);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
        },
        {
          title: "CheckOut",
          field: "Stop",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "daterange",
          resizable: false,
          width: 150,
          formatter: function(cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: function(value, data, type, params, column){

            if (value != null && value !== "") {
              try {

                let dt = luxon.DateTime.fromSeconds(value);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
        },
        {
          title: "Snow API",
          field: "SnowAccuWeather",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 150,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          }
        },
        {
          title: "Snow*",
          field: "snow_inch",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.snow_inch = newValue;
              self.applySnowTotalBulk(true, [rowData], newValue);
            }
          },
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Event",
          field: "swsd_event",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['swsd_event'] || [],
            clearable: true,
            multiselect: true
            },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (Array.isArray(headerValue) && headerValue.includes(rowValue)) { // Ensure headerValue is array
              return true;
            }
            return false; // Default if not array or not included
          },
          resizable: false,
          width: 160,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          editor: "list",
          editorParams: {
            valuesLookup: "active"
          },
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.VendorId == myvtem) {
              return true;
            }
            else {
              return false;
            }
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            self.eventName = newValue;
            self.applyEventBulk(true, [rowData], newValue)
          },
        },
        {
          title: "Client Pricing",
          field: "ClientPricingUi",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            value = `$${parseFloat(value).toFixed(2)}`
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Contractor Pricing",
          field: "ContractorPricingUi",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            value = `$${parseFloat(value).toFixed(2)}`
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Provider",
          field: "ProviderText",
          sorter: "string",
          resizable: true,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "Provider ID",
          field: "contractors",
          width: 150,
          visible: false,
          download: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "Client",
          field: "Client",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "QB Invoice ID",
          field: "swsd_qb_invoice_id",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "QB Bill ID",
          field: "swsd_qb_bill_id",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "Material Unit",
          field: "MaterialQuantity",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let rowData = cell.getRow().getData();
            let value = cell.getValue();
            if ( !isNaN(parseFloat(value)) && parseFloat(value) > 0){
              value = `${value} ${rowData.MaterialUnit}`;
            }else {
              value = "";
            }
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Metadata",
          field: "metadata",
          resizable: true,
          width: 200,
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "ID",
          field: "swsd_vendor_sequence_id",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 100,
        },
        {
          title: "Form Submission ID",
          field: "form_submission_id",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 100,
        },

        {
          title: "Date",
          field: "UnixTime",
          headerHozAlign: "center",
          sorter: "number",
          hozAlign: "center",
          headerFilter: "daterange",
          resizable: true,
          frozen: true,
          formatter: function (cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: function (value, data, type, params, column) {

            if (value != null && value !== "") {
              try {

                let dt = luxon.DateTime.fromSeconds(value);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
          minWidth: 180,

        }

      ]
    }
    this.fetchData();
  },

  asyncComputed: {
    exportFileName: {
      get() {
        return 'PostDatedServices-' + moment().format("MM-DD-YY")
      }
    },
  }
}
