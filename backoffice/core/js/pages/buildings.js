import {
  EventBus
} from '../eventbus.js';
import contacts from './contacts.js';
import pricingRouter from '../components/pricing-router.js';
import VuetifyGoogleAutocomplete from '../imports/vga/VuetifyGoogleAutocomplete.js';
import DropdownMultipleDatatableHeader from '../components/dropdownmultipledatatableheader.js';
import DropdownSimpleDatatableHeader from '../components/dropdown-simple-datatable-header.js';
import DataTableExtended from '../components/v-data-table-extended/v-data-table-extended.js'
import TabulatorDatatable from "../components/tabulator/tabulator-datatable.js";
import activityList from "../components/activity/activity-list.js";
import taskList from "../components/task/task-list.js";
import fileList from "../components/file/file-list.js";
import mapsList from "../components/maps/maps-list.js";
import estimateList from "../components/snowestimator/estimate-list.js";
import pricingContractList from "../components/pricing-contracts/pricing-contract-list.js";
import contractorsList from "../components/contacts-assignment/contractors-list.js";
import pricingBulkUploader from "../components/pricing-contracts/pricing-bulk-uploader.js";
import zoneList from "../components/zone/zone-list.js";
import Utilitylib from "../components/utilitylib.js";
import sourceList from "../components/misc/source-list.js";
import statusList from "../components/misc/status-list.js";
import defaultHeader from "../components/default-header.js";
// const mapkit = window.mapkit;
const sitesDataTable = Vue.component('sitesDataTable', {
  template: /*HTML*/`<div style="width:100%">
  <default-header title="Sites"></default-header>

  <tabulator-datatable
      :tabulator-configuration="tabulatorConfigSites"
      title="Sites"
      refresh-type="local"
      @localRefresh="refreshData"
      :primary-button-config='{
          "icon": "mdi-plus",
          "title": "New Site"
        }'
      :show-download-excel-button="true"
      :show-download-pdf-button="true"
      :name-of-file-export="exportFileName"
      id="sites-page-tabulator"
      ref="sitesTable"
      @rowSelectionChanged="processSelectionCount"
      :show-clear-all-filters-button="true"
      :show-refresh-button="true"
      :show-primary-button="pagePermissionAdd()==true"

      @primaryButtonCallback="newSite"
  > <template v-slot:custom-header>
    <v-btn v-if="pagePermissionAdd()==true" rounded color="primary" depressed class="text-center " @click="openSpreadSheetDialog" style="background-color:white;text-transform:none; margin-left:30px;">
    <v-icon style="font-size:24px;" left dark>mdi-cloud-upload</v-icon>
    Upload
  </v-btn>
    <v-spacer></v-spacer>
    <!-- Add merge button -->
  <v-btn 
  v-if="showMergeButton"
  rounded 
  color="warning" 
  @click="openMergeDialog" 
  depressed 
  class="text-center mr-2"
  style="background-color:white;text-transform:none;"
>
  <v-icon left>mdi-call-merge</v-icon>
  Merge Sites
</v-btn>
<v-btn v-if="pagePermissionEdit()==true" rounded color="primary" @click="bulkActionsDialog=true" depressed :disabled="bulkActionDisabled " class="text-center " style="background-color:white;text-transform:none; margin-left:30px;">
  Bulk Actions
</v-btn>
  </template>
    <template v-slot:tabs>
      <v-tabs align-with-title v-model="statusTab">
        <v-tab @click="handleStatusTabClick(item)" v-for="item in statusTabItems" :key="item">
          {{ item }}
        </v-tab>
      </v-tabs>
    </template>
  </tabulator-datatable>
<!-- Add this after your existing dialogs -->
<v-dialog v-model="mergeDialog" max-width="500">
  <v-card>
    <v-card-title>Merge Sites</v-card-title>
    <v-card-text>
      <v-container>
        <v-row>
          <v-col cols="12">
            <p class="subtitle-1">Select source and destination sites:</p>
          </v-col>
          <v-col cols="12">
            <v-select
              v-model="sourceSite"
              :items="selectedSites"
              label="Source Site"
              item-text="SITENAME"
              item-value="SITEID"
              outlined
              dense
            ></v-select>
          </v-col>
          <v-col cols="12">
            <v-select
              v-model="destinationSite"
              :items="selectedSites"
              label="Destination Site"
              item-text="SITENAME"
              item-value="SITEID"
              outlined
              dense
            ></v-select>
          </v-col>
          <v-col cols="12">
            <v-alert type="warning" dense>
              All data from the source site will be merged into the destination site. and the source site will be deactivated. This action cannot be undone.
            </v-alert>
          </v-col>
        </v-row>
      </v-container>
    </v-card-text>
    <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn color="grey darken-1" text @click="mergeDialog = false">Cancel</v-btn>
      <v-btn 
        color="warning" 
        @click="mergeSites" 
        :loading="merging"
        :disabled="!isValidMergeSelection"
      >
        Merge
      </v-btn>
    </v-card-actions>
  </v-card>
</v-dialog>
  <v-dialog v-model="bulkActionsDialog" scrollable transition="dialog-bottom-transition" max-width="900px">

    <v-card v-if="bulkActionsDialog">
      <v-card-title>
        <h3 class="headline mb-0">Bulk Actions</h3>
      </v-card-title>
      <v-card-text>
        <v-container fluid class="pa-0 ma-0">
          <v-row align="center">
            <v-col cols="12" sm="4" class='pa-0 pb-2 pr-2'>
              <v-autocomplete
                  v-model="selectedContractors"
                  :items="contractors"
                  menu-props="auto"
                  hide-details
                  label="Select Contractor(s)"
                  single-line
                  multiple
                  outlined dense
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="4" class='pa-0 pb-2 pr-2'>
              <v-autocomplete
                  v-model="selectedClient"
                  :items="clients"
                  menu-props="auto"
                  hide-details
                  label="Select Client"
                  single-line
                  outlined dense
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="4" class='pa-0 pb-2 pr-2'>
              <v-autocomplete
                  v-model="selectedManager"
                  :items="managers"
                  menu-props="auto"
                  hide-details
                  label="Select Manager"
                  single-line
                  outlined dense
              ></v-autocomplete>
            </v-col>


          </v-row>
          <v-row align="center">
            <v-col cols="12" sm="4" class='pa-0 pb-2 pr-2'>
              <v-autocomplete
                  v-model="selectedTrades"
                  :items="trades"
                  menu-props="auto"
                  hide-details
                  label="Select Trade(s)"
                  single-line
                  multiple
                  outlined dense
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="4" class='pa-0 pb-2 pr-2'>
              <v-autocomplete
                  v-model="selectedForms"
                  :items="forms"
                  menu-props="auto"
                  hide-details
                  label="Select Form(s)"
                  single-line
                  multiple
                  outlined dense
              ></v-autocomplete>
            </v-col>
            <v-col cols="12" sm="4" class='pa-0 pb-2 pr-2'>
              <v-autocomplete hide-details outlined dense v-model="selectedZone" :items="zoneList" item-text="sgz_geo_zone" item-value="sgz_zone_id"  label="Zone"></v-autocomplete>
            </v-col>


          </v-row>

        </v-container>

      </v-card-text>
       <v-row align="center" class='ml-1'>
            <v-col cols="12" sm="4">
              <v-btn @click="openPricingBulkUploader" block color="primary" rounded>
                Upload Snow Pricing Contracts
              </v-btn>
            </v-col>
          </v-row>
      <v-card-actions>
        <v-checkbox hide-details class="ml-4" v-model="overwriteContractors">
        <template v-slot:label>
        <div class='mb-4'>
        Replace Contractors
        </div>
        </template>
        </v-checkbox>
        <v-spacer></v-spacer>
        <v-btn color="blue darken-1" text @click.native="bulkActionsDialog=false">Cancel</v-btn>
        <v-btn color="primary" @click="bulkUpdate" :loading="bulkUpdateLoading">Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <v-dialog persistent v-model="dialogPricingContractsBulk" max-width="95vw" transition="dialog-bottom-transition" scrollable>
    <pricing-bulk-uploader :selected-sites="selectedSitesForPricingBulkUpload" :key="bulkUploaderComponentKey" @dismissBulkUploadDialog="onDismissOfBulkUploadDialog"></pricing-bulk-uploader>
  </v-dialog>

  <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'" :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
    {{ snackbar.text }}
    <v-btn color="pink" text @click="snackbar.snackbar = false"> Close </v-btn>
  </v-snackbar>
  </div>`,
  components: { DataTableExtended, TabulatorDatatable, pricingBulkUploader, defaultHeader },
  inject: { pagePermissionAdd: 'permissionAdd', pagePermissionEdit: 'permissionEdit', pagePermissionDelete: 'permissionDelete' },
  watch: {
    bulkActionsDialog: function (val) {
      this.selectedContractors = []
      this.selectedClient = ''
      this.selectedManager = ''
      this.selectedTrades = []
      this.selectedForms = []
      this.selectedZone = ''
      this.overwriteContractors = false
    }
  },
  data: function () {
    return {
      selectedSites: [],
      statusTab: 0,
      statusTabItems: ['All', 'Active', 'Inactive'],

      dialogPricingContractsBulk: false,
      bulkUploaderComponentKey: 0,
      mergeDialog: false,
      merging: false,
      sourceSite: null,
      destinationSite: null,
      loading: false,
      sites: [],
      tableHeight: 0,
      bulkActionsDialog: false,
      totalSites: 0,
      showMergeButton: false,
      bulkActionDisabled: true,
      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'left',
        mode: '',
        timeout: 2000,
        text: ''
      },
      bulkUpdateLoading: false,

      pageID: 3,
      tabulatorConfigSites: {},
      filters: {
        SiteID: '',
        SiteName: '',
        Address1: '',
        City: '',
        Zip: '',
        ProviderID: '',
        States: [],
        Clients: [],
        Trades: [],
        Own: [],
        Routes: [],
        Forms: [],
        Contractors: [],
        Systems: [],
        Zones: [],
        QuickbooksCustomer: ''
      },
      states: [],
      e2: null,
      firstRequest: true,
      selectedForms: [],
      selectedContractors: [],
      selectedClient: null,
      selectedManager: null,
      selectedTrades: [],
      selectedZone: null,
      overwriteContractors: false,
      selected: [],
      uniqueStates: [],
      uniqueTrades: [],
      uniqueRoutes: [],
      uniqueForms: [],
      uniqueClients: [],
      uniqueZones: [],
      uniqueContractors: [],
      uniqueSystems: [],
      routeFilterValues: [],
      contractorFilterValues: [],
      tradesFilterValues: [],
      stateFilterValues: [],
      zoneFilterValues: [],
      clientFilterValues: [],
      managerFilterValues: [],
      secondaryManagerFilterValues: [],
      thirdManagerFilterValues: [],
      systemsFilterValues: [],
      sourceFilterValues: [],
      formFilterValues: [],
      statusFilterValues: [],
      uniqueOwn: [{ value: 1, text: 'Internal' }, { value: 0, text: 'Contracted' }],
      options: { itemsPerPage: 100, sortBy: ["SITEID"], sortDesc: [true] },
      selectedSitesForPricingBulkUpload: []
    }
  },
  props: [
    'zoneList'
  ],
  mounted() {

    //this.updateData();
    this.$store.dispatch('fetchActivityTaskTypes');
    this.tabulatorConfigSites = {
      rowHeight: 54, //px
      reactiveData: false,
      persistence: {
        sort: true,
        filter: true,
        columns: true
      },
      printStyled: true, //copy table styling to exported table
      height: "100%",
      movableColumns: true,
      resizableRows: true, // this option takes a boolean value (default = false)
      downloadRowRange: "selected", //change default selector to selected
      movableRows: false,
      layout: "fitColumns",
      persistenceID: "sitesMainTable1",
      pagination: true,
      paginationMode: "remote",
      paginationSize: 1000,
      paginationSizeSelector: [1000, 2000, 5000, 10000, true],
      paginationCounter: "rows",
      placeholder: "No Data Available",
      placeholderHeaderFilter: "No Matching Data",
      filterMode: "remote",
      sortMode: "remote",
      ajaxURL: `${myBaseURL}/node/sites/get-sites-tabulator`,
      ajaxConfig: "POST",
      ajaxContentType: "json",
      ajaxParams: () => {
        var params = {};
        if (this.firstRequest) {
          params.cacheBust = true;
          this.firstRequest = false;
        }
        return params;
      },

      initialSort: [{
        column: "SITEID",
        dir: "desc"
      }],
      ajaxResponse: (url, params, response) => {

        this.tradesFilterValues = response.trades;
        this.stateFilterValues = response.states;
        this.routeFilterValues = response.routes;
        this.contractorFilterValues = response.contractors;
        this.clientFilterValues = response.clients;
        this.managerFilterValues = response.managers;
        this.secondaryManagerFilterValues = response.secondaryManager;
        this.thirdManagerFilterValues = response.thirdManager
        this.systemsFilterValues = response.systems;
        this.zoneFilterValues = response.zones;
        // const FormFilter = response.forms;
        this.sourceFilterValues = response.sources;
        this.formFilterValues = response.forms;
        this.statusFilterValues = response.statuses;
        this.loadLocalFilters();
        return response;
      },
      columns: [
        {
          formatter: "rowSelection",
          titleFormatter: "rowSelection",
          hozAlign: "center",
          titleFormatterParams: {
            rowRange: "active"
          },
          headerHozAlign: "center",
          headerSort: false,
          frozen: true,
          download: false,
          cellClick: function (e, cell) {
            cell.getRow().toggleSelect();
          }
        },

        {
          title: "Site Name",
          field: "SITENAME",
          headerFilter: "input",
          width: 250,
          resizable: true,
          columnMenu: true,
          headerFilter: "input",
          frozen: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilterLiveFilter: false,
          formatter: 'linkFormatter',
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            //console.log(rowData)
            this.showDetailView(rowData.SITEID.toString())

          }
        },
        {
          title: "Status",
          field: "STATUS",
          width: 150,
          resizable: true,
          frozen: true,
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return value == 1 ? 'ACTIVE' : 'INACTIVE';
          },
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: false,
              valuesLookup: () => this.statusFilterValues,
            }
          },
          headerFilterLiveFilter: false,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            value = value == 1 ? 'ACTIVE' : 'INACTIVE';
            if (value == "ACTIVE") {
              cell.getElement().style.backgroundColor = "#4BB543";
              cell.getElement().style.color = "white";
            } else if (value == "INACTIVE") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            }
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";
            return value;
          },
        },
        {
          title: "Client",
          field: "site_contact_primary",
          hozAlign: "center",
          sorter: "alphanum",
          resizable: true,
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "list",
          headerFilterParams: { clearable: true, multiselect: true },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.clientFilterValues,
            }
          },
          headerHozAlign: "center",
          formatter: 'linkFormatter',
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.showContactDetail(rowData.CLIENTID)
          }
        },
        {
          title: 'Zone',
          field: 'ZONED',
          minWidth: 150,
          headerSort: false,
          headerHozAlign: "center",
          resizable: true,
          headerFilter: "list",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.zoneFilterValues,
            }
          },
        },
        {
          title: 'Address',
          width: 150,
          resizable: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center",
          field: 'ADDRESS1',
          headerFilterLiveFilter: false
        },
        {
          title: 'City',
          minWidth: 150,
          resizable: true,
          field: 'CITYNAME',
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerFilterLiveFilter: false,
          headerHozAlign: "center"
        },
        {
          title: 'State',
          headerFilter: "list",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.stateFilterValues,
            }
          },
          field: 'STATENAME',
          width: 100,
          resizable: true,
          headerHozAlign: "center"
        },
        {
          title: 'Zip',
          field: 'ZIPCODE',
          hozAlign: "center",
          headerHozAlign: "center",
          headerFilter: "input",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          minWidth: 75,
          resizable: true,
          headerFilterLiveFilter: false
        },
        {
          title: 'Trades',
          field: 'TRADES',
          headerSort: false,
          headerFilter: "list",
          accessorDownload: function (value, data, type, params, column) {
            if (!value) return ""
            return value.join(", ");
          },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.tradesFilterValues,
            }
          },
          width: 150,
          resizable: true,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value.join(", ");
          },
          headerHozAlign: "center"
        },
        {
          title: "Manager 1",
          field: "primary_manager",
          hozAlign: "center",
          sorter: "alphanum",
          resizable: true,
          width: 150,
          headerFilter: "list",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilterParams: { clearable: true, multiselect: true },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.managerFilterValues,
            }
          },
          headerHozAlign: "center",
          formatter: 'linkFormatter',
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.showContactDetail(rowData.MANAGER)
          }
        },
        {
          title: "Manager 2",
          field: "secondary_manager",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          sorter: "alphanum",
          resizable: true,
          width: 150,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.secondaryManagerFilterValues,
            }
          },
          headerHozAlign: "center",
          formatter: 'linkFormatter',
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.showContactDetail(rowData.MANAGER2)
          }
        },
        {
          title: "Manager 3",
          field: "third_manager",
          hozAlign: "center",
          sorter: "alphanum",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          width: 150,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.thirdManagerFilterValues,
            }
          },
          headerHozAlign: "center",
          formatter: 'linkFormatter',
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            this.showContactDetail(rowData.MANAGER3)
          }
        },
        {
          title: 'Contractor',
          field: 'CONTRACTORNAMES',
          width: 150,
          headerSort: false,
          accessorDownload: function (value, data, type, params, column) {
            if (!value) return ""
            return value.join(", ");
          },
          headerHozAlign: "center",
          resizable: true,
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.contractorFilterValues,
            }
          },
          formatter: (cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            let tagData = ""
            if (rowData.OWN == 'Internal') {
              for (let i = 0; i < rowData.CONTRACTORNAMES.length; i++) {
                let resolvedRoute = this.$router.resolve({
                  name: 'contactsid',
                  params: { id: rowData.CONTRACTORIDS[i] }
                });
                tagData += `<a href="${resolvedRoute.href}" target="_blank" style="text-decoration: none;">${rowData.CONTRACTORNAMES[i]}</a></br>`
              }
            }
            cell.getElement().classList.add("custom-sites-cell-tabulator");
            return tagData

          },
          headerFilter: "list"
        },


        {
          title: 'Routes',
          field: 'ROUTENAMES',
          headerSort: false,
          minWidth: 150,
          accessorDownload: function (value, data, type, params, column) {
            if (!value) return ""
            return value.join(", ");
          },
          headerHozAlign: "center",
          resizable: true,

          headerFilter: "list",
          formatter: (cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            let tagData = ""
            if (rowData.OWN == 'Internal') {
              for (let i = 0; i < rowData.ROUTENAMES.length; i++) {
                let resolvedRoute = this.$router.resolve({
                  name: 'routebuilderid',
                  params: { id: rowData.ROUTEIDS[i] }
                });
                tagData += `<a href="${resolvedRoute.href}" target="_blank" style="text-decoration: none;">${rowData.ROUTENAMES[i]}</a></br>`
              }
            }
            cell.getElement().classList.add("custom-sites-cell-tabulator");
            return tagData

          },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.routeFilterValues,
            }
          },
        },
        {
          title: 'Forms',
          headerSort: false,
          accessorDownload: function (value, data, type, params, column) {
            if (!value) return ""
            return value.join(", ");
          },
          field: 'FORMNAMES',
          minWidth: 150,
          headerHozAlign: "center",
          resizable: true,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.formFilterValues,
            }
          },
          formatter: (cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            let tagData = ""
            if (rowData.OWN == 'Internal') {
              for (let i = 0; i < rowData.FORMNAMES.length; i++) {
                let resolvedRoute = this.$router.resolve({
                  name: 'formbuilderid',
                  params: { id: rowData.FORMIDS[i] }
                });
                tagData += `<a href="${resolvedRoute.href}" target="_blank" style="text-decoration: none;">${rowData.FORMNAMES[i]}</a></br>`
              }
            }
            cell.getElement().classList.add("custom-sites-cell-tabulator");
            return tagData

          },
        },
        {
          width: 150,
          resizable: true,
          title: "Quickbooks Customer",
          field: "QUICKBOOKSCUSTOMER",
          headerHozAlign: "center",
          headerFilter: "input",
          headerFilterLiveFilter: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          formatter: (cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.OWN == 'Internal') {
              return cell.getValue() ? cell.getValue() : "";
            }
            else {
              return ""
            }

          },
        },
        {
          width: 220,
          resizable: true,
          formatter: (cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.OWN == 'Internal') {
              return cell.getValue() ? cell.getValue() : "";
            }
            else {
              return ""
            }

          },
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          title: "External System",
          field: "EXTERNALPROVIDER",
          headerFilter: "list",
          headerHozAlign: "center",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.systemsFilterValues,
            }
          },
          width: 160,
        },
        {
          width: 200,
          resizable: true,
          title: "External ID",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          field: "EXTERNALPROVIDERID",
          formatter: function (cell) {
            return cell.getValue() ? cell.getValue() : "";
          },
          headerFilter: "input",
          headerFilterLiveFilter: false
        },
        {
          width: 150,
          resizable: true,
          title: "Site Source",
          field: "OWN",
          download: false,
          headerHozAlign: "center",
          formatter: function (cell) {

            return cell.getValue()
          },
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.sourceFilterValues,
            }
          },
        }, {
          title: "ID",
          field: "SITEID",
          headerHozAlign: "center",
          headerFilter: "input",
          width: 75,
          resizable: false,
          headerFilterLiveFilter: false,
        },
        {
          title: "Created Date",
          field: "ADDEDDATE",
          headerHozAlign: "center",
          sorter: "date",
          hozAlign: "center",
          headerFilter: "daterange",
          headerFilterLiveFilter: false,
          resizable: true,
          width: 180,
          formatter: function (cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          hozAlign: "center",
          headerHozAlign: "center",
        }

      ]
    }


  },
  methods: {
    async loadLocalFilters() {
      await new Promise((resolve, reject) => {
        const loop = () => {
          if (this.$refs.sitesTable?.tabulator === null || this.$refs.sitesTable?.tabulator === undefined) {
            setTimeout(loop, 100);
          } else {
            resolve(true);
          }
        };
        loop();
      });
      //Since filters are persistent, we need to update our tabs at top as well.
      //So there is not a scenario where a filter is inactive and tab selected is active.
      const appliedFilters = this.$refs.sitesTable?.tabulator?.getFilters();
      //Filter the filters by status column
      for (const appliedFilter of appliedFilters) {
        if (appliedFilter.field == "STATUS") {
          //GET ITS VALUE
          const filterValue = appliedFilter.value;
          this.handleStatusTabView(filterValue);
        }
      }
    },
    openMergeDialog() {
      this.selectedSites = this.$refs.sitesTable.tabulator.getSelectedData();
      this.sourceSite = null;
      this.destinationSite = null;
      this.mergeDialog = true;
    },

    async mergeSites() {
      if (!this.isValidMergeSelection) return;

      this.merging = true;
      try {
        const response = await fetch(`${myBaseURL}/node/sites/merge-sites`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            sourceSiteId: this.sourceSite,
            destinationSiteId: this.destinationSite
          })
        });

        const result = await response.json();

        if (result.success) {
          this.snackbar.text = "Sites merged successfully";
          this.snackbar.snackbar = true;
          this.mergeDialog = false;
          this.refreshData();
        } else {
          throw new Error(result.message || 'Failed to merge sites');
        }
      } catch (error) {
        this.snackbar.text = error.message || "An error occurred while merging sites";
        this.snackbar.snackbar = true;
      } finally {
        this.merging = false;
      }
    },
    handleStatusTabClick(tabClicked) {
      if (tabClicked == 'All') {
        this.$refs.sitesTable?.tabulator.clearFilter();
      }
      else if (tabClicked == 'Active') {
        this.$refs.sitesTable?.tabulator.setFilter("STATUS", "=", "1");
      }
      else if (tabClicked == 'Inactive') {
        this.$refs.sitesTable?.tabulator.setFilter("STATUS", "=", "2");
      }
    },
    handleStatusTabView(tabIndex) {
      this.statusTab = parseInt(tabIndex);
    },
    openPricingBulkUploader() {
      let selected = this.$refs.sitesTable.tabulator.getSelectedData();
      let data = [];
      for (const selectedElement of selected) {
        data.push({
          "site_id": selectedElement.SITEID,
          "site_name": selectedElement.SITENAME,
          "address": selectedElement.ADDRESS1,
          "city": selectedElement.CITYNAME,
          "state": selectedElement.STATENAME,
          "zip": selectedElement.ZIPCODE
        })
      };
      this.selectedSitesForPricingBulkUpload = data;
      this.bulkUploaderComponentKey += 1;
      this.dialogPricingContractsBulk = true;
    },
    async onDismissOfBulkUploadDialog() {
      this.dialogPricingContractsBulk = false;
    },
    async downloadPricingTemplateFile() {
      const url = `${myBaseURL}/static/pricing-bulk-template.xlsx`;
      try {
        const response = await fetch(url);
        const arrayBuffer = await response.arrayBuffer();

        // Process the Excel file
        const processedBuffer = await this.processExcelFile(arrayBuffer);

        // Trigger file download
        this.triggerDownload(processedBuffer, 'pricing-bulk-template.xlsx');
      } catch (error) {
        console.error('Error:', error);
      }
    },
    async processExcelFile(buffer) {
      let selected = this.$refs.sitesTable.tabulator.getSelectedData();
      let data = [];
      for (const selectedElement of selected) {
        data.push({
          "store": "",
          "address": selectedElement.ADDRESS1,
          "city": selectedElement.CITYNAME,
          "state": selectedElement.STATENAME,
          "zip": selectedElement.ZIPCODE,
          "id": selectedElement.SITEID
        })
      }
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(buffer); // Load the workbook from a buffer

      const worksheet = workbook.getWorksheet(1); // Assuming we're working with the first worksheet

      // Append data to the worksheet starting from row 4
      data.forEach((row, index) => {
        const rowIndex = index + 4; // Starting from row 4
        worksheet.getCell(`A${rowIndex}`).value = row.store;
        worksheet.getCell(`B${rowIndex}`).value = row.address;
        worksheet.getCell(`C${rowIndex}`).value = row.city;
        worksheet.getCell(`D${rowIndex}`).value = row.state;
        worksheet.getCell(`E${rowIndex}`).value = row.zip;
        worksheet.getCell(`F${rowIndex}`).value = row.id;
      });

      // Apply data validation to column I2 to V2
      const validation = {
        type: 'list',
        allowBlank: true,
        formulae: ['"HOURS,INCH,SERVICE,PARTIAL_SERVICE,MONTH,DAY,WEEK,SEASON,ITEM,YEAR,INCH,EVENT,PUSH,VISIT"']
      };
      for (let col = 9; col <= 22; col++) { // From column I (9) to V (22)
        worksheet.getCell(2, col).dataValidation = validation;
      }

      // Save the workbook to a new buffer
      const newBuffer = await workbook.xlsx.writeBuffer();
      return newBuffer;
    },
    triggerDownload(buffer, filename) {
      const blob = new Blob([buffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const url = window.URL.createObjectURL(blob);
      const anchor = document.createElement('a');
      anchor.href = url;
      anchor.download = filename;
      document.body.appendChild(anchor);
      anchor.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(anchor);
    },

    refreshData() {
      this.$emit("localRefresh")
      this.$refs.sitesTable?.tabulator.setData(this.tabulatorConfigSites.ajaxURL, { cacheBust: true });
    },
    openSpreadSheetDialog() {
      this.$emit('openSpreadSheetDialog');
    },
    processSelectionCount() {

      let selectedRows = this.$refs.sitesTable.tabulator.getSelectedData()

      if (selectedRows.length == 1) {
        this.bulkActionDisabled = false;
      } else if (selectedRows.length > 1) {
        this.bulkActionDisabled = false;
      } else {
        this.bulkActionDisabled = true;
      }

      this.showMergeButton = selectedRows.length === 2 && this.pagePermissionEdit && this.pagePermissionDelete;
      //this.showMergeButton = false;
    },

    newSite() {
      this.$emit('newSite');
    },

    showDetailView(id) {
      this.$emit('showDetailView', id);
    },
    showContactDetail(id) {
      if (!id) return;
      const resolvedRoute = this.$router.resolve({ name: 'contactsid', params: { id: id } });
      window.open(resolvedRoute.href, '_blank')
    },

    clearFilters() {
      const DEFAULT = {
        SiteID: '',
        SiteName: '',
        Address1: '',
        City: '',
        Zip: '',
        ProviderID: '',
        States: [],
        Clients: [],
        Trades: [],
        Routes: [],
        Forms: [],
        Contractors: [],
        Zones: []
      };
      Object.assign(this.filters, DEFAULT);
    },
    clickBulkActions() {
      console.log('clickBulkActions');
    },
    async bulkUpdate() {
      let selected = this.$refs.sitesTable.tabulator.getSelectedData()
      this.bulkUpdateLoading = true;
      let data = []


      for (let site of selected) {

        if (site.OWN == 'Internal') {

          let siteData = {
            siteID: site.SITEID,
            contractors: this.selectedContractors,
            client: this.selectedClient,
            manager: this.selectedManager,
            trades: this.selectedTrades,
            zone: this.selectedZone,
            forms: this.selectedForms,
            overwriteContractors: this.overwriteContractors
          }
          data.push(siteData)
        }
      }
      if (data.length > 0) {
        let res = await fetch(myBaseURL + "/node/sites/bulk-update", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            data
          })
        })
      }
      this.bulkUpdateLoading = false;
      this.snackbar.text = "Bulk Actions have been applied";
      this.snackbar.snackbar = true;
      this.bulkActionsDialog = false
      this.$refs.sitesTable.tabulator.setData(`${myBaseURL}/node/sites/get-sites-tabulator`, { cacheBust: true });


    },


    filterChange(key2, value2) {
      this.filters[key2] = value2;
    },
    deleteSite() {
      console.log('delete site');
    },
    async updateData(cacheBust = false) {
      this.$refs.sitesTable.tabulator.setData(`${myBaseURL}/node/sites/get-sites-tabulator`, { cacheBust: cacheBust });

    }
  },
  asyncComputed: {
    trades: {
      async get() {
        return await fetch(myBaseURL + '/node/vpics/get-trades').then(response => response.json())
      },
      default: []
    },
    forms: {
      async get() {
        try {

          let request = await fetch(myBaseURL + "/node/forms/forms2");
          let data = await request.json();
          if (data == '0') {
            //PHP returns 0 if empty forms. this is for local only. :)
            return []
          }
          let parsedData = [];
          for (const form of data) {
            if (form.sf_external != 1 && form.sf_own == 1)
              parsedData.push({ value: form.sf_id, text: form.sf_form_name })
          }
          return parsedData;
        } catch (ex) {
          console.log(ex)
        }
      }, default: []
    }
  },

  computed: {
    exportFileName: {
      get() {
        return 'Sites-' + moment().format("MM-DD-YY")
      }
    },


    isValidMergeSelection() {
      return this.sourceSite && this.destinationSite && this.sourceSite !== this.destinationSite;
    },
    filteredSites() {
      return this.sites;
    },
    contractors() {
      return this.$store.getters.getContractorsCompanies;
    },
    managers() {
      return this.$store.getters.getManagers;
    },
    clients() {
      return this.$store.getters.getCustomersCompanies;
    }, showBulkActions() {
      return this.selected.length > 0;
    }
  }
});

const sitesLeadsDataTable = Vue.component('sitesLeadsDataTable', {
  template: /*HTML*/`
  <div style="width:100%">
  <default-header title="Sites"></default-header>
   
    <tabulator-datatable
        :tabulator-configuration="tabulatorConfigSites"
        title="Site Leads"
        refresh-type="local"
        @localRefresh="refreshData"
        :primary-button-config='{
          "icon": "mdi-plus",
          "title": "Site Lead"
        }'
        :show-download-excel-button="true"
        :show-download-pdf-button="true"
        :name-of-file-export="exportFileName"
        id="site-leads-page-tabulator"
        ref="sitesLeadsTable"
        @rowSelectionChanged="processSelectionCount"
        :show-clear-all-filters-button="true"
        :show-refresh-button="true"
        :show-primary-button="pagePermissionAdd()==true"
        @primaryButtonCallback="newSite"
       
    >
      <template v-slot:custom-header>
        <v-btn v-if="pagePermissionAdd()==true" rounded color="primary" depressed class="text-center "
               @click="openSpreadSheetDialog" style="background-color:white;text-transform:none; margin-left:30px;">
          <v-icon style="font-size:24px;" left dark>mdi-cloud-upload</v-icon>
          Upload
        </v-btn>
        <v-spacer></v-spacer>
        <v-btn v-show="false" rounded color="primary" @click="bulkActionsDialog=true" depressed :disabled="bulkActionDisabled"
               class="text-center " style="background-color:white;text-transform:none; margin-left:30px;">
          Bulk Actions
        </v-btn>
      </template>
    </tabulator-datatable>

    <v-dialog v-model="bulkActionsDialog" scrollable transition="dialog-bottom-transition" max-width="900px">

      <v-card v-if="bulkActionsDialog">
        <v-card-title>
          <h3 class="headline mb-0">Bulk Actions</h3>
        </v-card-title>
        <v-card-text>
          <v-container fluid class="pa-0 ma-0">
            <v-row align="center">
              <v-col cols="12" sm="4">
                <v-autocomplete
                    v-model="selectedContractors"
                    :items="contractors"
                    menu-props="auto"
                    hide-details
                    label="Select Contractor(s)"
                    single-line
                    multiple
                    outlined
                    dense
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="4">
                <v-autocomplete
                    v-model="selectedClient"
                    :items="clients"
                    menu-props="auto"
                    hide-details
                    label="Select Client"
                    single-line
                    outlined
                    dense
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="4">
                <v-autocomplete
                    v-model="selectedManager"
                    :items="managers"
                    menu-props="auto"
                    hide-details
                    label="Select Manager"
                    single-line
                    outlined
                    dense
                ></v-autocomplete>
              </v-col>


            </v-row>
            <v-row align="center">
              <v-col cols="12" sm="4">
                <v-autocomplete
                    v-model="selectedTrades"
                    :items="trades"
                    menu-props="auto"
                    hide-details
                    label="Select Trade(s)"
                    single-line
                    multiple
                    outlined
                    dense
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="4">
                <v-autocomplete
                    v-model="selectedForms"
                    :items="forms"
                    menu-props="auto"
                    hide-details
                    label="Select Form(s)"
                    single-line
                    multiple
                    outlined
                    dense
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="4">
                <v-text-field
                    single-line outlined
                    dense hide-details rows=3 v-model="selectedZone"
                    list="existing" label="Branch/Zone/Area"></v-text-field>
                <datalist id="existing">
                  <template v-for="(item,key) in zones">
                    <option>{{ item }}</option>
                  </template>
                </datalist>

              </v-col>


            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-checkbox v-model="overwriteContractors">
           <template v-slot:label>
                                             <div class='mb-4'>
                                               Replace Contractors
                                            </div>
                                          </template>
          </v-checkbox>
          <v-spacer></v-spacer>
          <v-btn color="blue darken-1" text @click.native="bulkActionsDialog=false">Cancel</v-btn>
          <v-btn color="primary" @click="bulkUpdate" :loading="bulkUpdateLoading">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
                :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
      {{ snackbar.text }}
      <v-btn color="pink" text @click="snackbar.snackbar = false"> Close</v-btn>
    </v-snackbar>
   </div>`,
  components: { DataTableExtended, TabulatorDatatable, defaultHeader },
  watch: {
    bulkActionsDialog: function (val) {
      this.selectedContractors = []
      this.selectedClient = ''
      this.selectedManager = ''
      this.selectedTrades = []
      this.selectedForms = []
      this.selectedZone = ''
      this.overwriteContractors = false
    }
  },
  data: function () {
    return {
      loading: false,
      sites: [],
      tableHeight: 0,
      bulkActionsDialog: false,
      totalSites: 0,
      bulkActionDisabled: true,
      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'left',
        mode: '',
        timeout: 2000,
        text: ''
      },
      bulkUpdateLoading: false,

      pageID: 33,
      tabulatorConfigSites: {},
      filters: {
        SiteID: '',
        SiteName: '',
        Address1: '',
        City: '',
        Zip: '',
        ProviderID: '',
        States: [],
        Clients: [],
        Trades: [],
        Own: [],
        Routes: [],
        Forms: [],
        Contractors: [],
        Systems: [],
        Zones: [],
        QuickbooksCustomer: ''
      },
      states: [],
      e2: null,
      firstRequest: true,
      selectedForms: [],
      selectedContractors: [],
      selectedClient: null,
      selectedManager: null,
      selectedTrades: [],
      selectedZone: null,
      overwriteContractors: false,
      selected: [],
      uniqueStates: [],
      uniqueTrades: [],
      uniqueRoutes: [],
      uniqueForms: [],
      uniqueClients: [],
      uniqueZones: [],
      uniqueContractors: [],
      uniqueSystems: [],
      tradesFilterValues: [],
      stateFilterValues: [],
      zoneFilterValues: [],
      client1FilterValues: [],
      client2FilterValues: [],
      client3FilterValues: [],
      sourceFilterValues: [],
      uniqueOwn: [{ value: 1, text: 'Internal' }, { value: 0, text: 'Contracted' }],
      siteLeadStatuses: [],
      options: { itemsPerPage: 100, sortBy: ["SITEID"], sortDesc: [true] },
    }
  },
  inject: { pagePermissionAdd: 'permissionAdd', pagePermissionEdit: 'permissionEdit', pagePermissionDelete: 'permissionDelete' },
  async mounted() {
    this.tabulatorConfigSites = {
      reactiveData: false,
      persistence: {
        sort: true,
        filter: true,
        columns: true
      },
      printStyled: true, //copy table styling to exported table
      height: "100%",
      movableColumns: true,
      resizableRows: true, // this option takes a boolean value (default = false)
      downloadRowRange: "selected", //change default selector to selected
      movableRows: false,
      layout: "fitColumns",
      persistenceID: "siteLeadsMainTable",
      pagination: true,
      paginationMode: "remote",
      paginationSize: 100,
      paginationSizeSelector: [100, 250, 500, 1000],
      paginationCounter: "rows",
      placeholder: "No Data Available",
      placeholderHeaderFilter: "No Matching Data",
      filterMode: "remote",
      sortMode: "remote",
      ajaxURL: `${myBaseURL}/node/sites/get-site-leads-tabulator`,
      ajaxConfig: "POST",
      ajaxContentType: "json",
      ajaxParams: () => {
        var params = {};
        if (this.firstRequest) {
          params.cacheBust = true;
          this.firstRequest = false;
        }
        return params;
      },
      initialSort: [{
        column: "SITEID",
        dir: "desc"
      }],
      ajaxResponse: (url, params, response) => {
        this.tradesFilterValues = response.trades;
        this.stateFilterValues = response.states;
        // const routeFilterValues = response.routes;
        // const contractorFilterValues = response.contractors;
        // const systemsFilter = response.systems;
        this.zoneFilterValues = response.zones;
        // const FormFilter = response.forms;
        this.sourceFilterValues = response.sources;
        this.client1FilterValues = response.clients;
        this.client2FilterValues = response.secondaryClients;
        this.client3FilterValues = response.thirdClients;
        this.siteLeadStatuses = response.status;
        return response
      },
      columns: [
        {
          formatter: "rowSelection",
          titleFormatter: "rowSelection",
          hozAlign: "center",
          headerHozAlign: "center",
          headerSort: false,
          frozen: true,
          download: false,
          cellClick: function (e, cell) {
            cell.getRow().toggleSelect();
          }
        },

        {
          title: "Site",
          field: "SITENAME",
          headerFilter: "input",
          width: 250,
          resizable: true,
          columnMenu: true,
          frozen: true,
          headerFilterLiveFilter: false,
          formatter: function (cell) {
            //return '<a href="#" onclick="showDetailView(' + cell.getRow().getData().SITEID + ')">' + cell.getValue() + '</a>';
            return "<span style='color:#1867c0; font-weight:bold;'>" +
              cell.getValue() +
              "</span>"
          },
          cellClick: (e, cell) => {
            let row = cell.getRow();
            let rowData = row.getData();
            //console.log(rowData)
            this.showDetailView(rowData.SITEID.toString())
          }
        },
        {
          title: "ID",
          field: "SITEID",
          width: 100,
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: 'Zone',
          field: 'ZONED',
          minWidth: 150,
          headerSort: false,
          headerHozAlign: "center",
          resizable: true,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.zoneFilterValues,
            }
          },
        },
        {
          title: 'Address',
          width: 150,
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          field: 'ADDRESS1',
          headerFilterLiveFilter: false
        },
        {
          title: 'City',
          minWidth: 150,
          resizable: true,
          field: 'CITYNAME',
          headerFilter: "input",
          headerFilterLiveFilter: false,
          headerHozAlign: "center"
        },
        {
          title: 'State',
          headerFilter: "list",
          hozAlign: "center",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.stateFilterValues,
            }
          },
          field: 'STATENAME',
          width: 100,
          resizable: true,
          headerHozAlign: "center"
        },
        {
          title: 'Trades',
          field: 'TRADES',
          headerSort: false,
          headerFilter: "list",
          download: false,
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.tradesFilterValues,
            }
          },
          width: 150,
          resizable: true,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value.join(", ");
          },
          headerHozAlign: "center"
        },
        {
          title: "Site Notes",
          field: "SITENOTES",
          hozAlign: "center",
          sorter: "alphanum",
          headerFilter: "input",
          resizable: true,
          width: 150,
          headerFilterParams: { valuesLookup: true, clearable: true },
          headerHozAlign: "center"
        },
        {
          title: "Client 1",
          field: "site_contact_primary",
          hozAlign: "center",
          sorter: "alphanum",
          resizable: true,
          width: 150,
          headerFilter: "list",
          headerFilterParams: { clearable: true, multiselect: true },
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.client1FilterValues,
            }
          },
          headerHozAlign: "center"
        },
        {
          title: "Client 2",
          field: "site_contact_secondary",
          hozAlign: "center",
          sorter: "alphanum",
          resizable: true,
          width: 150,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.client2FilterValues,
            }
          },
          headerHozAlign: "center"
        },
        {
          title: "Client 3",
          field: "site_contact_third",
          hozAlign: "center",
          sorter: "alphanum",
          resizable: true,
          width: 150,
          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.client3FilterValues,
            }
          },
          headerHozAlign: "center"
        },
        {
          width: 150,
          resizable: true,
          title: "Source",
          field: "SITELEADSOURCE",
          download: false,
          headerHozAlign: "center",

          headerFilter: "list",
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.sourceFilterValues,
            }
          },
        },
        {
          title: "Status",
          field: "SITELEADSTATUS",
          hozAlign: "center",
          headerHozAlign: "center",
          headerFilter: "list",
          editor: "list",
          validator: ["required"],
          frozen: true,
          headerFilterParams: () => {
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => this.siteLeadStatuses.filter(status => status.ssbs_active == 1).map(status => ({ value: status.ssbs_id, label: status.ssbs_status })),
            }
          },
          editorParams: {
            valuesLookup: () => this.siteLeadStatuses.filter(status => status.ssbs_active == 1).map(status => ({ value: status.ssbs_id, label: status.ssbs_status })),
            sort: "asc",
            allowEmpty: false,
            freetext: false,
          },
          cellEdited: async (cell) => {
            let newValue = cell.getValue();
            const oldValue = cell.getOldValue();
            let rowData = cell.getRow().getData();
            if (newValue != oldValue) {
              const result = await this.updateSiteLeadStatus(rowData.SITEID, newValue);
              if (!result) {
                cell.restoreOldValue();
              }
            }
          },
          formatter: (cell, formatterParams, onRendered) => {
            var value = cell.getValue();
            return this.siteLeadStatuses.find(status => status.ssbs_id === parseInt(value))?.ssbs_status;
          },
          minWidth: 150,
          resizable: false
        }
      ]
    }
  },
  methods: {
    refreshData() {
      this.$emit("localRefresh")
      this.$refs.sitesLeadsTable?.tabulator.setData(this.tabulatorConfigSites.ajaxURL, { cacheBust: true });
    },
    async updateSiteLeadStatus(building, newStatus) {
      const response = await fetch(`/node/sites/update-siteleads-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          buildingStatus: newStatus,
          buildingId: building,
        })
      });
      if (response.ok) {
        return true;
      }
      return false;
    },
    openSpreadSheetDialog() {
      this.$emit('openSpreadSheetDialog');
    },
    processSelectionCount() {

      let selectedRows = this.$refs.sitesLeadsTable.tabulator.getSelectedData()

      if (selectedRows.length == 1) {
        this.bulkActionDisabled = false;
      } else if (selectedRows.length > 1) {
        this.bulkActionDisabled = false;
      } else {
        this.bulkActionDisabled = true;
      }

    },
    newSite() {
      this.$emit('newSite');
    },

    showDetailView(id) {
      this.$emit('showDetailView', id);
    },

    clearFilters() {
      const DEFAULT = {
        SiteID: '',
        SiteName: '',
        Address1: '',
        City: '',
        Zip: '',
        ProviderID: '',
        States: [],
        Clients: [],
        Trades: [],
        Routes: [],
        Forms: [],
        Contractors: [],
        Zones: []
      };
      Object.assign(this.filters, DEFAULT);
    },
    clickBulkActions() {
      console.log('clickBulkActions');
    },
    async bulkUpdate() {
      let selected = this.$refs.sitesLeadsTable.tabulator.getSelectedData()
      this.bulkUpdateLoading = true;
      let data = []


      for (let site of selected) {

        if (site.OWN == 'Internal') {

          let siteData = {
            siteID: site.SITEID,
            contractors: this.selectedContractors,
            client: this.selectedClient,
            manager: this.selectedManager,
            trades: this.selectedTrades,
            zone: this.selectedZone,
            forms: this.selectedForms,
            overwriteContractors: this.overwriteContractors
          }
          data.push(siteData)
        }
      }
      if (data.length > 0) {
        let res = await fetch(myBaseURL + "/node/sites/bulk-update", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            data
          })
        })
      }
      this.bulkUpdateLoading = false;
      this.snackbar.text = "Bulk Actions have been applied";
      this.snackbar.snackbar = true;
      this.bulkActionsDialog = false
      this.$refs.sitesLeadsTable.tabulator.setData(`${myBaseURL}/node/sites/get-sites-tabulator`, { cacheBust: true });


    },


    filterChange(key2, value2) {
      this.filters[key2] = value2;
    },
    async updateData(cacheBust = false) {
      this.$refs.sitesLeadsTable.tabulator.setData(`${myBaseURL}/node/sites/get-site-leads-tabulator`, { cacheBust: cacheBust });
    }
  },
  asyncComputed: {
    trades: {
      async get() {
        return await fetch(myBaseURL + '/node/vpics/get-trades').then(response => response.json())
      },
      default: []
    },
    forms: {
      async get() {
        try {

          let request = await fetch(myBaseURL + "/node/forms/forms2");
          let data = await request.json();
          if (data == '0') {
            //PHP returns 0 if empty forms. this is for local only. :)
            return []
          }
          let parsedData = [];
          for (const form of data) {
            if (form.sf_external != 1 && form.sf_own == 1)
              parsedData.push({ value: form.sf_id, text: form.sf_form_name })
          }
          return parsedData;
        } catch (ex) {
          console.log(ex)
        }
      }, default: []
    }
  },

  computed: {
    exportFileName: {
      get() {
        return 'Sites-' + moment().format("MM-DD-YY")
      }
    },
    filteredSites() {
      return this.sites;
    },
    contractors() {
      return this.$store.getters.getContractorsCompanies;
    },
    managers() {
      return this.$store.getters.getManagers;
    },
    clients() {
      return this.$store.getters.getCustomersCompanies;
    }, showBulkActions() {
      return this.selected.length > 0;
    }
  }
});


let {
  LMap,
  LTileLayer,
  LMarker,
  LPolygon,
  LTooltip,
  LControl
} = Vue2Leaflet;

export default {
  template: /*HTML*/`<div style="width:100%">
  
  <v-app-bar flat app fixed elevate-on-scroll clipped-left class="mainback" style="border-bottom:1px solid #e5e5e5;">
    <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
    <div class="">
      <img src="/images/sitefotos_logo_icon.svg" style="width:44px; height:44px;padding-right:10px;">
    </div>
    <span class="page-titles">Sites</span>
    <v-spacer></v-spacer>
    <v-autocomplete class="customfield1 mr-4 sites hidden-sm-and-down" hide-details dense v-model="currentlyediting" return-object :items="buildings" label="Search sites" content-class="selectmenufix1" item-value="mb_id" item-text="mb_nickname" style="width:300px;max-width:300px!important;" v-if="buildings != null && stage=='map'">
      <template slot="item" slot-scope="data" v-if="buildings.length>0">
        <v-list-item-content class="sitefoto-site-item">
          <v-list-item-title v-if="data.item.mb_external_src ? data.item.mb_external_src=='BossLM' : false" v-html="data.item.mb_nickname+ ' *'"></v-list-item-title>
          <v-list-item-title v-else v-html="data.item.mb_nickname"></v-list-item-title>
        </v-list-item-content>
      </template>
    </v-autocomplete>
    <!-- <v-btn text :title="stage=='map' ? 'Switch to Grid View' : 'Switch to Map View'" @click="changeMode" class="hidden-sm-and-down stopcaps galerylink2 nitems" v-if="this.$store.getters.getAccessCode == '8f85517967795eeef66c225f7883bdcb' || this.$store.getters.getAccessCode=='211fff9e65c0e47a790c629116e32996'"><template v-if="stage=='map'"><v-icon>grid_on</v-icon></template><template v-else><v-icon>map</v-icon></template></v-btn> -->
    <v-btn v-if="editedIndex!=-1 && buildingpoly.length>0 && pagePermissionAdd && stage=='map'" color="primary" class="ma-2 white--text" @click="currentlyediting=null"> Add Site <v-icon right dark> mdi-plus </v-icon>
    </v-btn>
    <div class="nav-block">
      <v-img :src="cLogo" contain max-height="36" max-width="72" :alt="cName" style="display: inline-block"></v-img>
      <v-menu offset-y bottom style="max-width: 200px">
        <template v-slot:activator="{ on, attrs }">
          <v-avatar color="purple" size="36" class="ml-2" v-bind="attrs" v-on="on">
            <span class="white--text headline">{{initials}}</span>
          </v-avatar>
        </template>
        <v-list>
          <v-list-item to="/account">
            <v-list-item-title>Settings</v-list-item-title>
          </v-list-item>
          <v-list-item @click="logout()">
            <v-list-item-title>Log Off</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </div>
    <v-menu offset-y :close-on-content-click='false' v-if="stage=='map'">
      <template v-slot:activator="{ on }">
        <v-toolbar-side-icon v-on="on" class="hidden-md-and-up">
          <v-icon>more_vert</v-icon>
        </v-toolbar-side-icon>
      </template>
      <v-card>
        <v-list dense>
          <v-list-item class="pb-2">
            <v-list-item-content>
              <v-autocomplete class="dense-inputs" hide-details v-model="currentlyediting" return-object :items="buildings" label="Search sites" content-class="selectmenufix1" item-value="mb_id" item-text="mb_nickname">
                <template slot="item" slot-scope="data">
                  <v-list-item-content>
                    <v-list-item-title v-if="data.item.mb_external_src ? data.item.mb_external_src=='BossLM' : false" v-html="data.item.mb_nickname+ ' *'"></v-list-item-title>
                    <v-list-item-title v-else v-html="data.item.mb_nickname"></v-list-item-title>
                  </v-list-item-content>
                </template>
              </v-autocomplete>
            </v-list-item-content>
          </v-list-item>
          <v-list-item v-if="editedIndex!=-1 && buildingpoly.length>0" @click="newsite">
            <v-list-item-action class="reduce-min-width">
              <v-icon small color="rgba(0,0,0,.54)">fiber_new</v-icon>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>New</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-card>
    </v-menu>
  </v-app-bar>
  <contacts v-show="false"></contacts>
  <components :is="pageType=='sites' ? 'sites-data-table' : 'sites-leads-data-table'" ref="dataTable" :zoneList="zoneList" @localRefresh="loadExtraData" @showDetailView="showDetailView" @openSpreadSheetDialog="openSpreadSheetDialog" @newSite="changeModeNew"></components>
  <v-dialog v-model="siteDetailsDialog" v-if="siteDetailsDialog" fullscreen hide-overlay transition="dialog-bottom-transition">
    <v-card color="#fafafa">
      <v-app-bar flat  fixed elevate-on-scroll clipped-left class="mainback" style="background-color: white!important; border-bottom:1px solid #e5e5e5;">
        <v-btn icon color="primary" @click="siteDetailsDialog = false" :disabled="saving">
          <v-icon>mdi-close</v-icon>
        </v-btn>
        <span class="page-titles">{{ editedItem.mb_nickname.length > 0 ? editedItem.mb_nickname : pageType == "sites" ? "Site" : "Site Lead" }}</span>
       
        
        <v-spacer></v-spacer>
        <v-btn v-if="pagePermissionEdit && editedItem.mb_id && pageType == 'sitesLeads'"
        @click="activateSiteLead"
        color="primary"
        depressed
        class="text-center ml-2 mr-2" style="background-color:white;text-transform:none;  ">Activate
    </v-btn>
    
        <v-btn @click.native="save" :loading="saving" :disabled="editedItem.mb_nickname.length==0 || editedItem.mb_own==0 || saving" v-if="buildingpoly.length>0 && ((pagePermissionEdit && editedIndex != -1) || (pagePermissionAdd && editedIndex == -1))" color="success" class="text-center " style="background-color:white;text-transform:none;"> Save </v-btn>
      </v-app-bar>
      <v-container fluid class="fill-height pt-0">
        <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'" :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
          {{ snackbar.text }}
          <v-btn color="pink" text @click="snackbar.snackbar = false"> Close </v-btn>
        </v-snackbar>
        <Utilitylib ref="utilityLib"></Utilitylib>
        <v-layout wrap class="fill-height pa-0" style="margin-top: 64px;">
          <v-card class="elevation-0 transparent pa-0" style="width: 100%;">
            <v-card-text class="pa-0 ma-0">
              <v-row>
                <v-col md="6" cols="12">
                  <v-card>
                    <v-card-title> Geofence Boundary <v-spacer></v-spacer>
                      <v-btn color="primary" @click="enableGeofenceEdit" v-if="editedIndex !== -1 && pagePermissionEdit" v-show="editedItem.mb_own==1 && editedItem.mb_locked == 0">Reposition</v-btn>
                    </v-card-title>
                    <v-card-text>
                      <l-map ref="leaflet" :maxZoom="maxzoom" :zoom="zoom" @load="mapload" :center="center" style="position:sticky!important;height:50vh;z-index:0;" :options="{editable: true, zoomControl: true, doubleClickZoom: false}">
                        <l-control>
                          <vuetify-google-autocomplete v-if="(pagePermissionAdd && currentlyediting==null) || (pagePermissionAdd && geofenceEditEnabled)" v-model="editedItem.mb_address1" :autofocus="editedItem.mb_address1.length == 0" solo append-icon="search" ref="address" id="address" placeholder="Please enter address to add a site" style="width:35vw" v-on:placechanged="getAddressData"></vuetify-google-autocomplete>
                        </l-control>
                        <l-control position="bottomleft">
                          <v-btn-toggle v-model="mapProvider" mandatory dense class="map-provider-toggle">
                            <v-btn small value="maptiler" title="Maptiler" @click="setMaptilerProvider">
                              <v-icon small>mdi-map</v-icon>
                            </v-btn>
                            <v-btn small value="google" title="Google Maps" @click="setGoogleProvider">
                              <v-icon small>mdi-google</v-icon>
                            </v-btn>
                          </v-btn-toggle>
                        </l-control>
                        <l-tile-layer :options="options" :url="url" :attribution="attribution"></l-tile-layer>
                        <l-polygon v-if="buildingpoly.length>0" color="#FF6600" fillColor="yellow" :fillOpacity="fillOpacity" ref="poly" :latLngs="buildingpoly" @click="polyclick"></l-polygon>
                        <!-- <template v-for="build in buildingsgeo"><l-polygon color="#FF6600" fillColor="yellow" :latLngs="build.coords" @click="buildclick(build.bid)"></l-polygon></template> -->
                        <l-marker v-if="marker!==null" :lat-lng="marker"></l-marker>
                      </l-map>
                    </v-card-text>
                  </v-card>
                  <v-card class="my-4" :disabled="buildingpoly.length<=0 || !pagePermissionEdit">
                    <v-card-title> Site Information </v-card-title>
                    <v-card-text>
                      <v-form ref="buildingsform" v-model="validbuilding" lazy-validation class='pl-2'>
                        <v-row>
                          <v-col xs12 sm6 class='pa-0 pb-2 pr-2'>
                            <v-text-field :rules="[rules.required]" ref="sitename" hide-details :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" label="Site Name" type="text" outlined hide-details dense v-model="editedItem.mb_nickname"></v-text-field>
                          </v-col>
                        </v-row>
                        <v-row>
                          <v-col xs12 sm6 class='pa-0 pb-2 pr-2'>
                            <v-text-field hide-details :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" :rules="[rules.required]" label="Address 1" type="text" outlined dense v-model="editedItem.mb_address1"></v-text-field>
                          </v-col>
                          <v-col xs12 sm6 class='pa-0 pb-2 pr-2'>
                            <v-text-field hide-details label="Address 2" :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" type="text" outlined dense v-model="editedItem.mb_address2"></v-text-field>
                          </v-col>
                        </v-row>
                        <v-row>
                          <v-col cols='12' md="2" class='pa-0 pb-2 pr-2'>
                            <v-autocomplete dense :items="countries" outlined hide-details :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" label="Country" item-text="name" item-value="id" v-model="editedItem.mb_country"></v-autocomplete>
                          </v-col>
                          <v-col cols='12' md="4" class='pa-0 pb-2 pr-2'>
                            <v-autocomplete outlined dense :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" :items="statesus" hide-details label="State/Province" v-if="editedItem.mb_country=='254'" v-model="editedItem.statename"></v-autocomplete>
                            <v-autocomplete outlined dense :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" :items="statesca" hide-details label="State/Province" v-if="editedItem.mb_country=='43'" v-model="editedItem.statename"></v-autocomplete>
                            <v-autocomplete outlined dense :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" :items="statesmx" hide-details label="State/Province" v-if="editedItem.mb_country=='159'" v-model="editedItem.statename"></v-autocomplete>
                            <v-autocomplete outlined dense disabled hide-details label="State/Province" v-if="editedItem.mb_country!='254' && editedItem.mb_country!='43' && editedItem.mb_country!='159' "></v-autocomplete>
                          </v-col>
                          <v-col cols='12' md="4" sm6 class='pa-0 pb-2 pr-2'>
                            <v-text-field :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" dense outlined hide-details v-model="editedItem.cityname" :rules="[rules.required]" label="City"></v-text-field>
                          </v-col>
                          <v-col cols='12' md="2" sm6 class='pa-0 pb-2 pr-2'>
                            <v-text-field :disabled="editedItem.mb_own==0 || editedItem.mb_locked == 1" hide-details outlined label="Zip" type="text" dense v-model="editedItem.mb_zip_code"></v-text-field>
                          </v-col>
                        </v-row>
                        <v-row style="align-items: center">
                          <v-col cols='12' md='6' class="pt-0 pb-2 pr-2 pl-0">
                            <v-autocomplete outlined v-model="selectedPropertyTypes" :disabled="editedItem.mb_own==0" :items="propertyTypes" label="Property Type" multiple hint="Property Type" dense hide-details>

                             <template v-slot:selection="{ item, index }">

                                <span v-if="index === 0">{{ item }}</span>

                            <span
                            v-if="index === 1"
                            class="grey--text caption"
                            >
                            &nbsp; (+{{ selectedPropertyTypes.length - 1 }} others)
                            </span>
                        </template>

                            </v-autocomplete>
                          </v-col>

                           <v-col cols='12' md='6' class="pt-0 pb-2 pl-0 pr-2">
                            <v-autocomplete
                                v-model="bcontracts"
                                :items="trades"
                                outlined
                                :disabled="editedItem.mb_own==0"
                                menu-props="auto"
                                dense hide-details
                                label="Select Trade(s)"
                                single-line
                                multiple
                            >
                             <template v-slot:selection="{ item, index }">

                                <span v-if="index === 0">{{ item }}</span>

                            <span
                            v-if="index === 1"
                            class="grey--text caption"
                            >
                            &nbsp; (+{{ bcontracts.length - 1 }} others)
                            </span>
                        </template>

                            </v-autocomplete>
                          </v-col>
                        </v-row>
                        <v-row>
                          <v-col cols='12' md='2' class="pt-0 pb-2 pl-0 pr-1">
                            <v-text-field :rules="[rules.decimal]" outlined hide-details dense v-model="bsqft" :disabled="editedItem.mb_own==0" label="Site Sqft"></v-text-field>
                          </v-col>
                          <v-col cols='12' md='4' class="pt-0 pb-2 pb-1 pr-1 pl-1">
                            <v-autocomplete :disabled="editedItem.mb_own!=1" hide-details dense outlined v-model="bzone" :items="zoneList" item-text="sgz_geo_zone" item-value="sgz_zone_id" label="Zone" append-outer-icon="mdi-pencil" @click:append-outer="dialogZones = true"></v-autocomplete>
                          </v-col>
                          <v-col cols='12' md='3' class="pt-0 pb-2 pb-1 pr-2 pl-1">
                            <v-text-field outlined dense :disabled="editedItem.mb_own==0" class="  " hide-details v-model="bhours" name="input-7-1" label="Operating Hours"></v-text-field>
                          </v-col>
                          <v-col cols='12' md='3' class="pt-0 pb-2 pb-1 pr-2 pl-1">
                            <v-text-field v-if="buildingpoly && buildingpoly.length > 0" outlined dense disabled hide-details v-model="timezone" name="input-7-1" label="Timezone"></v-text-field>
                          </v-col>
                        </v-row>
                        <!--
                        <v-row>
                          <v-col cols='12' md='4' class="pt-0 pb-2 pl-0 pr-1">
                            <v-text-field outlined hide-details dense v-model="bradius" inputType="number" :disabled="editedItem.mb_own==0" label="Site CheckIn/Out Radius(meters)"></v-text-field>
                          </v-col>
                          <v-col cols='12' md='8' class="pt-0 pb-2 pb-1 pr-1 pl-1">
                              info field 
                            <v-label>Site CheckIn/Out Radius applies to all the forms of type checkin/out for this site. This will stop user from checkin/out on a form if they are this distance away from the site center.</v-label>
                          </v-col>
                        </v-row>
                        -->
                        <v-row>
                          <v-col class='pa-0 pb-2 pr-2'>
                            <v-textarea dense hide-details outlined name="input-7-1" :disabled="editedItem.mb_own==0" label="Site Notes" v-model="bnotes" rows="3"></v-textarea>
                          </v-col>
                        </v-row>
                        <v-row style="align-items: center">
                          <v-col class='pa-0 pr-2'>
                          <v-checkbox hide-details v-if="editedItem.mb_own==1" class="reducespacing-1" true-value="1" false-value="2" v-model="editedItem.mb_min_zoom">
                          <template v-slot:label>
                            <div class='mb-1'>Automatically Share Photos</div>
                            </template>
                          </v-checkbox>
                          <v-textarea outlined hide-details class="customfield1 reducespacing-1" rows=3 v-if="editedItem.mb_min_zoom==1 && editedItem.mb_own==1" v-model="bemails" hint="Enter emails (separated by comma) of clients who should receive photos in their account." label="Share Emails"></v-textarea>
                          </v-col>
                        </v-row>
                      </v-form>
                    </v-card-text>
                  </v-card>
                  <v-card v-if="editedItem.mb_own == 1" class="my-4" :disabled="buildingpoly.length<=0 || !pagePermissionEdit" :key="editedItem.mb_id+'clt'">
                    <v-card-title> Client Information <v-spacer></v-spacer>
                      <v-tooltip bottom color="primary">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon @click="openExternalPage('client')" color="primary" v-bind="attrs" v-on="on"> mdi-launch </v-icon>
                        </template>
                        <span>Open Contacts</span>
                      </v-tooltip>
                    </v-card-title>
                    <v-card-text>
                      <v-row class='pl-2'>
                        <v-col cols='12' md='4' class='pa-0 pb-2 pr-2'>
                          <v-autocomplete clearable dense hide-details outlined v-model="primaryClient" :items="primaryClients" label="Primary Client Contact" :item-text="item => item.displayName" item-value="sf_contact_id"></v-autocomplete>
                        </v-col>
                        <v-col cols='12' md='4' class='pa-0 pb-2 pr-2'>
                          <v-autocomplete clearable dense hide-details outlined v-model="secondaryClient" :items="secondaryClients" label="Secondary Client Contact" :item-text="item => item.displayName" item-value="sf_contact_id"></v-autocomplete>
                        </v-col>
                        <v-col cols='12' md='4' class='pa-0 pb-2 pr-2'>
                          <v-autocomplete clearable dense hide-details outlined v-model="thirdClient" :items="thirdClients" label="Third Client Contact" :item-text="item => item.displayName" item-value="sf_contact_id"></v-autocomplete>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <v-card v-if="editedItem.mb_own == 1" class="my-4" :disabled="buildingpoly.length<=0 || !pagePermissionEdit" :key="editedItem.mb_id+'mngr'">
                    <v-card-title class="cardjuniortitle"> Internal Managers <v-spacer></v-spacer>
                      <v-tooltip bottom color="primary">
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon @click="openExternalPage('managers')" color="primary" v-bind="attrs" v-on="on"> mdi-launch </v-icon>
                        </template>
                        <span>Open Employees</span>
                      </v-tooltip>
                    </v-card-title>
                    <v-card-text>
                      <v-row class='pl-2'>
                        <v-col cols='12' md='4' class='pa-0 pb-2 pr-2'>
                          <v-autocomplete outlined dense hide-details clearable :items="primaryManager" v-model="bemployee" label="Primary Internal Manager"></v-autocomplete>
                        </v-col>
                        <v-col cols='12' md='4' class='pa-0 pb-2 pr-2'>
                          <v-autocomplete outlined dense hide-details clearable :items="secondaryManager" v-model="bemployeesecondary" label="Secondary Internal Manager"></v-autocomplete>
                        </v-col>
                        <v-col cols='12' md='4' class='pa-0 pb-2 pr-2'>
                          <v-autocomplete outlined dense hide-details clearable :items="thirdTertiaryManager" v-model="bemployeethird" label="Tertiary Internal Manager"></v-autocomplete>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <v-card class="my-4" :disabled="!pagePermissionEdit" v-if="(providers.length > 0 || integrations.qbStatus) && pageType == 'sites'">
                    <v-card-title class="cardjuniortitle"> API Integrations <v-spacer></v-spacer>
                      <v-btn class="mt-2" color="primary" :disabled="!pagePermissionEdit" v-if="integrations.scPinStatus && editedIndex!=-1 && buildingpoly.length>0" @click.native="scPinDialog=true">Associate with SC PIN</v-btn>
                    </v-card-title>
                    <v-card-text>
                      <v-row class='pl-2'>
                        <v-col xs12 sm6 class='pa-0 pr-2'>
                          <v-autocomplete outlined v-if="providers.length > 0" :rules="[validateExternalSystem]" content-class="selectmenufix2" dense prepend-icon="launch" class="customfield1 reducespacing-1" hide-details label="External Provider" clearable :items="providers" v-model="bexternal"></v-autocomplete>
                        </v-col>
                        <v-col xs12 sm6 class='pa-0 pr-2'>
                          <v-text-field outlined prepend-icon="mdi-numeric" v-if="providers.length > 0" class="customfield1 reducespacing-1" hide-details v-model="bexternalid" label="External Site ID" dense :append-icon="externalSiteSearchIcon" @click:append="searchExternalSiteClicked"></v-text-field>
                        </v-col>
                      </v-row>
                      <v-row class='pl-2'>
                        <v-col xs12 sm6 class='pa-0 pr-2'>
                          <v-autocomplete outlined dense hide-details clearable prepend-icon="sitefotos-quickbooks" v-if="integrations.qbStatus" label="Quickbooks Customer" :key="bquickbooksid" v-model="bquickbooksid" :items="quickbooksCustomers" item-text="sqc_display_name" item-value="sqc_id" />
                        </v-col>
                        <v-col xs12 sm6 class='pa-0  pr-2'></v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <v-card class="my-4" :disabled="!editedItem.mb_id" v-if="editedItem.mb_own == 1 && pageType == 'sites'">
                    <contractors-list @changeContractorAssignments="changeContractorAssignments" :key="'site-' + (editedItem.mb_id ?? '') + 'ctl'" :siteId="editedItem.mb_id?.toString() ?? ''" :already-assigned-contractors-prop="bcontractor"></contractors-list>
                  </v-card>
                </v-col>
                <v-col md="6" cols="12">
                <v-card v-if="pageType == 'sitesLeads'"   class="overflow-hidden mb-5" :disabled="!pagePermissionEdit">
                <v-card-title> Status </v-card-title>
                <v-card-text >
                <v-row>
                <v-col xs12 sm6 class='pa-0 pb-2 pr-2'>
                          <v-autocomplete :rules="[rules.required]"
                          :loading="loadingExtraData"
                          :disabled="!pagePermissionEdit"
                          v-model="b_source"
                          :items="b_source_items"
                          item-text="ssst_source"
                          item-value="ssst_id"
                          label="Lead Source"
                          hide-details
                          outlined
                          dense
                          v-if="pageType == 'sitesLeads'"
                          append-outer-icon="mdi-pencil"
                          @click:append-outer="dialogSource = true"
                          style="max-width:200px;"></v-autocomplete>
                          </v-col>
                          <v-col xs12 sm12 class='pa-0 pb-2 pr-2'>   
          <v-autocomplete v-if="pageType == 'sitesLeads'" :rules="[rules.required]"
              :loading="loadingExtraData"
              :disabled="!pagePermissionEdit"
              hide-details
              v-model="b_site_status"
              :items="b_site_status_items"
              item-text="ssbs_status"
              item-value="ssbs_id"
              label="Lead Status"
              append-outer-icon="mdi-pencil"
              @click:append-outer="dialogStatus = true"
              style="max-width:200px;"
              outlined
              dense
              ></v-autocomplete>
              </v-col>
                        </v-row>
                      
                </v-card-text>
                
              </v-card>
                  <activity-list v-if="editedItem.mb_own==1" :key="editedItem.mb_id+'act'" v-bind=" editedItem.mb_id && {siteID: editedItem.mb_id.toString()}"></activity-list>
                  <!--                  <task-list :key="editedItem.mb_id+'task'" :source="'SITES'" v-bind=" editedItem.mb_id && {referenceID: editedItem.mb_id.toString()} " class="mt-5"></task-list>-->
                  <maps-list v-if="editedItem.mb_own==1" :key="editedItem.mb_id+'maps'" :source="'SITES'" v-bind=" editedItem.mb_id && {referenceID: editedItem.mb_id.toString()} " class="mt-5" :building-poly="buildingpoly" :building-details="editedItem" @changeBuildingMaps="changeBuildingMaps" :selectedMaps="bmaps"></maps-list>
                  <!--                  <estimate-list :key="editedItem.mb_id+'est'" :source="'SITES'" v-bind=" editedItem.mb_id && {referenceID: editedItem.mb_id.toString()} " class="mt-5"></estimate-list>-->
                  <file-list v-if="editedItem.mb_own==1 && pageType == 'sites'" :key="editedItem.mb_id+'file'" :source="'SITES'" v-bind=" editedItem.mb_id && {siteID: editedItem.mb_id.toString()} " class="mt-5"></file-list>
                  <pricing-contract-list v-if="editedItem.mb_own==1 && pageType == 'sites'" :key="editedItem.mb_id+'pc'" v-bind=" editedItem.mb_id && {siteID: editedItem.mb_id.toString()} " class="mt-5"></pricing-contract-list>
                </v-col>
              </v-row>
              <v-row>
                <v-col class="mx-4 d-flex justify-center align-center">
                  <v-btn v-if="editedIndex != -1 && pagePermissionEdit && editedItem.mb_status == 1" :disabled="editedItem.mb_own == 0 || editedItem.mb_locked == 1" color="warning" @click="deactivateSite" depressed class="text-center " style="background-color:white;text-transform:none; margin-right:10px;">De-Activate</v-btn>
                 
                  <v-btn v-if="editedIndex != -1 && pagePermissionEdit && editedItem.mb_status == 2" :disabled="editedItem.mb_own == 0 || editedItem.mb_locked == 1" color="warning" @click="reactivateSite" depressed class="text-center " style="background-color:white;text-transform:none; margin-right:10px;">Re-Activate</v-btn>
                  <v-btn v-if="editedIndex != -1 && pagePermissionDelete" :disabled="editedItem.mb_own == 0 || editedItem.mb_locked == 1" color="error" @click="deleteSite" depressed class="text-center " style="background-color:white;text-transform:none; margin-right:10px;">Delete</v-btn>
                  <v-btn  color="error" depressed class="text-center " style="background-color:white;text-transform:none; margin-right:10px;" title="Disconnect this shared building from your account" v-if="editedIndex!=-1 && buildingpoly.length>0 && (editedItem.mb_own==0 && editedItem.CLIENTVIEW == 0) && pagePermissionDelete" @click.native="disconnectItem">Disconnect </v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-layout>
      </v-container>
    </v-card>
  </v-dialog>
  <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'" :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">{{ snackbar.text
    }}
    <v-btn color="pink" text @click="snackbar.snackbar = false">Close</v-btn>
  </v-snackbar>
  <v-dialog v-model="loadingmain" persistent width="300">
    <v-card color="primary" dark>
      <v-card-text>Loading... <v-progress-linear indeterminate color="white" class="mb-0"></v-progress-linear>
      </v-card-text>
    </v-card>
  </v-dialog>
  <pricing-router v-model="pricingVisible" v-if="pricingVisible" v-bind:building-id="editedIndex" pricing-type="CLIENT" @close="closePricing()"></pricing-router>
  <pricing-router v-model="pricingContractorVisible" v-if="pricingContractorVisible" v-bind:building-id="editedIndex" pricing-type="CONTRACTOR" @close="closePricingContractor()"></pricing-router>
  <v-dialog v-model="contracttypeadddialog" persistent max-width="600px">
    <v-card>
      <v-card-title>
        <span class="headline">New Contract Type</span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-text-field label="Contract Type" v-model="newContractType"></v-text-field>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="blue darken-1" text @click="contracttypeadddialog = false"> Cancel </v-btn>
        <v-btn color="primary" @click="addnewContractType"> Create </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog v-model="scPinDialog" persistent max-width="320px">
    <v-card>
      <v-card-title>
        <span class="headline"></span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-text-field label="PIN" v-model="scPin"></v-text-field>
            <v-text-field label="Store ID" v-model="scStoreId"></v-text-field>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="blue darken-1" text @click="scPinDialog = false"> Cancel </v-btn>
        <v-btn color="primary" :loading="scPinLoading" @click="savePin"> Link </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog v-model="spreadSheetDialog" v-if="spreadSheetDialog" persistent>
    <v-card>
      <v-card-title> Add Sites <v-spacer></v-spacer>
        <v-btn v-if="showVerifySitesButton" color="primary" outlined tile depressed style="padding: 0 20px; text-transform:none; font-size:1rem;" @click="callGeoCodeApi">
          <v-icon>check</v-icon>Verify Sites
        </v-btn>
        <v-btn v-if="json_array.length > 0 && !showVerifySitesButton && !dialogLoading" @click="startBulkUpload" color="primary" outlined tile depressed style="padding: 0 20px; text-transform:none; font-size:1rem;">Add Sites</v-btn>
      </v-card-title>
      <v-card-text>
        <div id="drop-area" v-if="json_array.length == 0">
          <div class="drop-rect" @dragover="dragOver" @dragleave="dragLeave" @click="rectClick" @drop="drop" style="margin-top:30px;">
            <table style="width:100%; border-bottom: 1px solid #bbb;" class="mb-5">
              <tr>
                <th style="color: #00000099; font-size: 10.5px;">Name</th>
                <th style="color: #00000099; font-size: 10.5px;">Address</th>
                <th style="color: #00000099; font-size: 10.5px;">City</th>
                <th style="color: #00000099; font-size: 10.5px;">State</th>
                <th style="color: #00000099; font-size: 10.5px;">Zip</th>
                <th style="color: #00000099; font-size: 10.5px;">Lat</th>
                <th style="color: #00000099; font-size: 10.5px;">Lng</th>
                <th style="color: #00000099; font-size: 10.5px;">Status</th>
                <th style="color: #00000099; font-size: 10.5px;">Map</th>
              </tr>
            </table>
            <div class="drop-rect-text" ref="recttext">
              <v-row no-gutters style="display: flex;flex-direction: row;align-items: center;justify-content: center;">
                <v-col style="position: relative">
                  <div style="position: absolute; z-index: 2; width: 100%; text-align: center; margin-top: 4%;">
                    <h6 class="mt-0 pa-0" style="color: #1976d2; font-weight: bold; font-size: 1.6rem;">Copy & Paste or Drag your Spreadsheet File Here!</h6>
                  </div>
                </v-col>
              </v-row>
            </div>
            <textarea class="csv-area" @paste="onPaste" v-model="csv_data" ref="csvarea" style="width:100%;color:black!important;display:none;"></textarea>
          </div>
        </div>
        <v-data-table id="drop-area-dt" v-if="json_array.length > 0" style="border: 1px solid #bbb; margin-top:30px; height: auto!important;" virtual-rows dense loading-text="Parsing addresses... Please wait" :loading="xlsxloading" :headers="xlsxheaders" :items="json_array" :items-per-page="-1" class="elevation-0 my-data-table" sort-by="status" hide-default-footer>
          <template v-slot:item.name="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.name }}
              <template v-slot:input>
                <v-text-field v-model="props.item.name" label="Edit Name"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.address="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.address }}
              <template v-slot:input>
                <v-text-field v-model="props.item.address" label="Edit Address"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.city="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.city }}
              <template v-slot:input>
                <v-text-field v-model="props.item.city" label="Edit City"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.state="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.state }}
              <template v-slot:input>
                <v-text-field v-model="props.item.state" label="Edit State"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.zip="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.zip }}
              <template v-slot:input>
                <v-text-field v-model="props.item.zip" label="Edit Zip"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.lat="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.lat }}
              <template v-slot:input>
                <v-text-field v-model="props.item.lat" label="Edit Latitude"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.lng="props">
            <v-edit-dialog @save="saveXls" @cancel="cancel" @open="open">
              {{ props.item.lng }}
              <template v-slot:input>
                <v-text-field v-model="props.item.lng" label="Edit Longitude"></v-text-field>
              </template>
            </v-edit-dialog>
          </template>
          <template v-slot:item.status="props">
            <template v-if="showIconsInDatatabe">
              <v-icon v-if="props.item.status == 1" color="green">mdi-check</v-icon>
              <v-icon v-if="props.item.status == 0" color="red">mdi-alert-circle-outline</v-icon>
              <v-icon v-if="props.item.status == -1" color="yellow">mdi-alert-circle-outline</v-icon>
            </template>
          </template>
          <template v-slot:item.map="{ item }">
            <template v-if="showIconsInDatatabe">
              <v-icon v-if="item.status == 1" @click="showD(item)" color="#7f3f9f" dark>map</v-icon>
            </template>
          </template>
          <template v-slot:item.delete="{ item }">
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-icon @click="deleteRow(item)" color="#1976d2" dark v-bind="attrs" v-on="on"> delete </v-icon>
              </template>
              <span>Delete</span>
            </v-tooltip>
          </template>
        </v-data-table>
        <div style="width: 100%; text-align: center">
          <span>( Don't forget to include column headers - We suggest using our <a :href="baseUrlComputed+'/static/sites.xlsx'">Spreadsheet Template</a> . ) </span>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" text @click="spreadSheetDialog=false">Cancel</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog persistent v-model="dialogLoading" width="500">
    <v-card>
      <v-card-title class="headline lighten-2" primary-title> Please Wait </v-card-title>
      <v-card-text>
        <v-row>
          <v-progress-linear v-if="loaderType.progress" v-model="percentLoader" color="blue-grey" height="25">
            <template v-slot:default="{ value }">
              <strong>{{ Math.ceil(value) }}%</strong>
            </template>
          </v-progress-linear>
          <v-progress-circular v-if="loaderType.circle" class="mx-5" indeterminate color="primary"></v-progress-circular>
          <p>{{dialogLoadingText}}</p>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
  <v-dialog absolute scrollable v-if="gridSelectExternalSite.dialog" v-model="gridSelectExternalSite.dialog" :fullscreen="$vuetify.breakpoint.smAndDown" width="70vw" :hide-overlay="$vuetify.breakpoint.smAndDown" transition="dialog-bottom-transition">
    <v-card height="62vh" class="elevation-0 overflow-hidden">
      <v-progress-linear v-if="gridSelectExternalSite.loading" color="blue" indeterminate></v-progress-linear>
      <v-card-text height="50vh" style="padding-left:0px;padding-right:0px;" class="overflow-hidden">
        <tabulator-datatable :tabulator-configuration="gridSelectExternalSite.tabulatorConfigExternalSites" title="External Sites" refresh-type="local" @localRefresh="refreshExternalSites" id="externalSitesTabulator" ref="externalSitesTabulator" :show-clear-all-filters-button="true" :show-refresh-button="true">
          <template v-slot:back-button>
            <v-btn icon @click="gridSelectExternalSite.dialog = false">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </template>
        </tabulator-datatable>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" :loading="loading" @click="selectExternalSite">Select</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog absolute v-model="dialogSource" :fullscreen="$vuetify.breakpoint.smAndDown" :hide-overlay="$vuetify.breakpoint.smAndDown" width="40vw">
  <source-list :key="dialogSource" :items="b_source_items" @close="dialogSource = false" @add-update="addUpdateSource" @delete="deleteSource" ></source-list>
</v-dialog>
<v-dialog absolute v-model="dialogStatus" :fullscreen="$vuetify.breakpoint.smAndDown" :hide-overlay="$vuetify.breakpoint.smAndDown" width="40vw">
  <status-list :key="dialogStatus" :items="b_site_status_items" @close="dialogStatus = false" @add-update="addUpdateStatus" @delete="deleteStatus"></status-list>
</v-dialog>
  <v-dialog absolute v-model="dialogZones" :fullscreen="$vuetify.breakpoint.smAndDown" :hide-overlay="$vuetify.breakpoint.smAndDown" width="40vw">
    <zone-list :key="dialogZones" @close="dialogZones = false" :zones="zoneList" @zoneUpdated="$refs.dataTable.updateData(true);"></zone-list>
  </v-dialog>
</div>`,

  data: function () {
    return {

      zoneList: [],
      stage: "grid",
      geocoded: false,
      geofenceEditEnabled: false,
      loaderType: {
        circle: false,
        progress: false
      },
      percentLoader: 0,
      siteDetailsDialog: false,
      aspireSites: [],
      smsoneSites: [],
      command7Sites: [],
      caseFMSSites: [],
      corrigoProSites: [],
      appleJwt: undefined,
      dialogLoading: false,
      dialogLoadingText: "Loading",
      xlsxloading: false,
      scPin: "",
      scPinDialog: false,
      scStoreId: "",
      scPinLoading: false,
      xlsxheaders: [
        {
          align: "center",
          sortable: false,
          text: "Name",
          value: "name",
          width: "15%"
        },
        {
          align: "center",
          sortable: false,
          text: "Address",
          value: "address",
          width: "15%"
        },
        {
          align: "center",
          sortable: false,
          text: "City",
          value: "city",
          width: "10%"
        },
        {
          align: "center",
          sortable: false,
          text: "State",
          value: "state",
          width: "10%"
        },
        {
          align: "center",
          sortable: false,
          text: "Zip",
          value: "zip",
          width: "10%"
        },
        {
          align: "center",
          sortable: false,
          text: "Lat",
          value: "lat",
          width: "8%"
        },
        {
          align: "center",
          sortable: false,
          text: "Lng",
          value: "lng",
          width: "8%"
        },
        {
          align: "center",
          text: "Status",
          value: "status",
          minwidth: "20%",
          width: "8%"
        },
        {
          align: "right",
          text: "Action",
          value: "delete",
          width: "8%"
        }
      ],
      json_array: [],
      csv_file: null,
      csv_data: "",
      spreadSheetDialog: false,
      gridSelect: {
        dialog: false,
        filter: {
          company_name: '',
          fname: '',
          lname: '',
          email: '',
          cell: ''
        },
        pagination: { itemsPerPage: -1, sortBy: ["mb_nickname"] },
        headers: [
          {
            text: 'Company',
            align: 'start',
            sortable: false,
            value: 'company_name'
          },
          {
            text: 'First',
            align: 'start',
            sortable: false,
            value: 'fname'
          },
          {
            text: 'Last',
            align: 'start',
            sortable: false,
            value: 'lname'
          },
          {
            text: 'Email',
            align: 'start',
            sortable: false,
            value: 'email'
          },
          {
            text: 'Cell',
            align: 'start',
            sortable: false,
            value: 'cell'
          }
        ],
        showSelected: false,
        search: '',
        selected: []
      },
      gridSelectContractor: {
        dialog: false,
        filter: {
          group: '',
          contractor: '',
          lname: '',
          fname: '',
          cell: ''
        },
        pagination: { itemsPerPage: -1, sortBy: ["value"] },
        headers: [
          {
            text: 'Company',
            align: 'start',
            sortable: false,
            value: 'group',
            width: '25%'
          },
          {
            text: 'First',
            align: 'start',
            sortable: false,
            value: 'fname',
            width: '23%'
          },
          {
            text: 'Last',
            align: 'start',
            sortable: false,
            value: 'lname',
            width: '22%'
          },
          {
            text: 'Email',
            align: 'start',
            sortable: false,
            value: 'email',
            width: '20%'
          },
          {
            text: 'Mobile Phone',
            align: 'start',
            sortable: false,
            value: 'cell',
            width: '20%'
          }
        ],
        showSelected: false,
        search: '',
        tempSelection: [],
        selected: []
      },
      gridSelectExternalSite: {
        tabulatorConfigExternalSites: {
          selectableRows: 1,
          selectableRowsRollingSelection: true,
          placeholder: "No Data Available",
          placeholderHeaderFilter: "No Matching Data",
          reactiveData: false,
          height: "50vh",
          layout: "fitColumns",
          pagination: true,
          paginationMode: "local",
          paginationSize: 100,
          paginationSizeSelector: [100, 250, 500, 1000],
          paginationCounter: "rows",
          filterMode: "local",
          sortMode: "local",
          index: 'externalID',
          columns: [
            {
              formatter: "rowSelection",
              titleFormatter: "",
              hozAlign: "center",
              headerSort: false,
            },
            {
              title: "Site Name",
              field: "name",
              headerFilter: "input",
              headerHozAlign: "center",
              minWidth: 200,
              headerFilterParams: { clearable: true },
            },
            {
              title: "Address",
              field: "address",
              headerFilter: "input",
              headerHozAlign: "center",
              minWidth: 200,
              headerFilterParams: { clearable: true },
            },
            {
              title: "City",
              field: "city",
              headerFilter: "list",
              headerHozAlign: "center",
              minWidth: 200,
              headerFilterParams: { valuesLookup: true, multiselect: true, clearable: true },
            }, {
              title: "State",
              field: "state",
              headerFilter: "list",
              headerHozAlign: "center",
              minWidth: 200,
              headerFilterParams: { valuesLookup: true, multiselect: true, clearable: true },
            }, {
              title: "Zip",
              field: "zip",
              headerFilter: "input",
              headerHozAlign: "center",
              minWidth: 200,
              headerFilterParams: { clearable: true },
            }
          ]
        },
        selected: undefined,
        dialog: false,
        loading: false,
      },
      gridSelectAccountManager: {
        dialog: false,
        filter: {
          fname: '',
          lname: '',
          email: '',
          cell: ''
        },
        pagination: { itemsPerPage: -1, sortBy: ["value"] },
        headers: [
          {
            text: 'First',
            align: 'start',
            sortable: false,
            value: 'fname'
          },
          {
            text: 'Last',
            align: 'start',
            sortable: false,
            value: 'lname'
          },
          {
            text: 'Email',
            align: 'start',
            sortable: false,
            value: 'email'
          },
          {
            text: 'Cell',
            align: 'start',
            sortable: false,
            value: 'cell'
          }
        ],
        showSelected: false,
        search: '',
        selected: []
      },
      componentKey: 0,
      panel: [true, false, false],
      zoom: 13,
      cmode: 1,
      pageID: 3,
      loadingExtraData: false,
      loadingmain: false,
      activetab: null,
      contracttypeadddialog: false,
      newContractType: "",
      center: L.latLng(41.881832, -87.623177),
      mapProvider: 'maptiler',
      url: '',
      attribution: '',
      options: {
        maxZoom: 20,
      },
      optionsbutton: {
        position: 'top-left'
      },
      pricingVisible: false,
      pricingContractorVisible: false,
      rules: {
        required: value => !!value || 'Required.',
        email: value => {
          const pattern = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          return pattern.test(value) || 'Invalid e-mail.'
        },
        requiredobj: value => {
          return value.length > 0 || 'Required.'
        },
        decimal: value => {
          const pattern = /^\d*(\.\d+)?$/;
          return value === '' || value === null || pattern.test(value) || 'Must be a decimal number.';
        }
      },
      valid: true,
      validbuilding: true,
      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'left',
        mode: '',
        timeout: 2000,
        text: ''
      },
      currentlyediting: null,
      activesaving: false,
      marker: null,
      maxzoom: 20,
      fillOpacity: 0.3,
      bcontractor: [],
      cpdialog: false,
      bappprofile: [],
      bemployee: '',
      bemployeesecondary: '',
      bemployeethird: '',

      profilesaving: false,
      bnotes: '',
      bzone: '',
      bradius: '',
      bemails: '',
      bsharesetting: '2',


      bhours: '',
      bsqft: '',
      bmaps: [],
      bcontracts: [],
      bexternal: '',
      bexternalid: '',
      bquickbooksid: null,

      bappnotes: '',
      search: '',
      buildingpoly: [],
      dialog: false,
      customdialog: false,
      customvarname: '',
      customvarvalue: '',
      loading: true,
      timesPostHasBeenUpdated: 0,
      createcopy: null,
      saving: false,
      buildingdetailed: [],
      position: null,
      customfields: [],
      currentbuildingid: null,
      customvarheaders: [{
        text: 'Name',
        align: 'left',
        value: 'mbek_key',
        width: "5"
      },
      {
        text: 'Value',
        align: 'left',
        value: 'mbev_value',
        width: "5"
      },
      {
        text: 'Actions',
        value: 'name',
        align: 'right',
        width: "100",
        sortable: false

      }
      ],
      mycontracttypes: [],
      headers: [{
        text: 'Name',
        align: 'left',
        value: 'mb_nickname',
        class: 'tableheader'
      },
      {
        text: 'Address',
        align: 'left',
        value: 'mb_address1',
        class: 'tableheader'
      },
      {
        text: 'City',
        align: 'left',
        value: 'cityname',
        class: 'tableheader'
      },
      {
        text: 'State',
        align: 'left',
        value: 'statename',
        class: 'tableheader'
      },
      {
        text: 'Zip',
        align: 'left',
        value: 'mb_zip_code',
        class: 'tableheader'
      },
      {
        text: 'Country',
        align: 'left',
        value: 'mb_country',
        class: 'tableheader'
      },
      {
        text: 'Actions',
        value: 'name',
        align: 'right',
        width: "100",
        sortable: false,
        class: 'tableheader'

      }
      ],
      integrations: {},
      providers: [],
      desserts: [],
      editedIndex: -1,
      timezone: '',
      editedItem: {
        mb_nickname: '',
        mb_address1: '',
        cityname: '',
        statename: '',
        mb_zip_code: '',
        mb_country: '',
        mb_own: 1,
        mb_locked: 0,
      },
      defaultItem: {
        mb_nickname: '',
        mb_address1: '',
        cityname: '',
        statename: '',
        mb_zip_code: '',
        mb_country: '',
        mb_own: 1,
        mb_locked: 0,
      },
      bossUser: false,
      qbContacts: [],

      companies: [],
      primaryClient: undefined,
      secondaryClient: undefined,
      thirdClient: undefined,
      b_site_status: undefined,
      b_site_status_items: [],
      b_source: undefined,
      b_source_items: [],
      propertyTypes: ['Retail (freestanding)', 'Retail Inline', 'Office', 'Industrial', 'Hotel', 'Hospital', 'Multi-Family', 'Residential'],
      selectedPropertyTypes: [],

      dialogSource: false,
      dialogStatus: false,

      countries: [
        {
          id: "43",
          name: "Canada",
          initials: "CA"
        },
        {
          id: "254",
          name: "United States",
          initials: "US"
        },
        {
          id: "159",
          name: "Mexico",
          initials: "MX"
        },
      ], //these are from our db
      statesus: ['AL', 'AK', 'AS', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'DC', 'FM', 'FL', 'GA', 'GU', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MH', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'MP', 'OH', 'OK', 'OR', 'PW', 'PA', 'PR', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VI', 'VA', 'WA', 'WV', 'WI', 'WY'],
      statesca: ['AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT'],
      statesmx: ['AG', 'BN', 'BS', 'CH', 'CI', 'CL', 'CP', 'CS', 'DF', 'DG', 'GE', 'GJ', 'HD', 'JA', 'MC', 'MR', 'MX', 'NA', 'NL', 'OA', 'PU', 'QE', 'QI', 'SI', 'SL', 'SO', 'TA', 'TB', 'TL', 'VC', 'YU', 'ZA'],

      dialogZones: false
    };
  },
  components: {
    fileList,
    activityList,
    taskList,
    mapsList,
    estimateList,
    pricingContractList,
    contractorsList,
    zoneList,
    Utilitylib,
    LMap,
    sourceList,
    statusList,
    LTileLayer,
    LMarker,
    LPolygon,
    LTooltip,
    VuetifyGoogleAutocomplete,
    LControl,
    contacts,
    sitesDataTable,
    sitesLeadsDataTable,
    'pricing-router': pricingRouter,
    DropdownSimpleDatatableHeader,
    DropdownMultipleDatatableHeader
  },
  updated() {

  },
  props: {
    pageType: {
      type: String,
      default: 'sites'
    },
  },

  async mounted() {
    if (this.pageType == 'sites') {
      this.pageID = 3;
    } else {
      this.pageID = 33;
    }
    if (localStorage.cmode) {
      this.cmode = parseInt(localStorage.cmode);
    }

    const contactListener = (contactID) => {
      let k = _.find(this.contacts, function (o) {
        return o.sf_contact_id == contactID;
      });
      if (k.sf_contact_type == "Employee") {
        let obj = {};

        obj.text = k.sf_contact_fname + " " + k.sf_contact_lname;
        obj.value = k.sf_contact_id;
        this.bemployee = obj;
      }
      if (k.sf_contact_type == "Client") {
        this.primaryClient = k.sf_contact_id;
      }
    };

    const buildingDetailedListener = async (buildingdetailed) => {
      {

        await new Promise((resolve, reject) => {
          const loop = () => this.initialDataLoaded ? resolve(true) : setTimeout(loop)
          loop();
        });

        let buildingid = buildingdetailed.propertyid;

        this.currentbuildingid = buildingid;
        this.bemails = buildingdetailed.propertyemails;

        let tnotes = buildingdetailed.propertynotes.replace(/\\+/g, '\\')
        this.bnotes = tnotes.replace(/(\\r\\n|\\n|\\r)/gm, "\n");

        this.primaryClient = buildingdetailed.propertyclient;
        this.secondaryClient = buildingdetailed.propertyclientsecondary;
        this.thirdClient = buildingdetailed.propertyclientthird;

        this.editedItem.mb_country = buildingdetailed.propertycountryid;

        this.selectedPropertyTypes = buildingdetailed.propertykind.split(",")

        this.b_site_status = buildingdetailed.mbbuildingstatus || 'Active';
        //New changes end

        this.bzone = buildingdetailed.propertyzoneid ? parseInt(buildingdetailed.propertyzoneid) : '';
        this.bemployee = buildingdetailed.propertyman;

        this.bemployeesecondary = buildingdetailed.propertymansecond;
        this.bemployeethird = buildingdetailed.propertymanthird;
        this.scPin = buildingdetailed.propertyscpin || "";
        this.scStoreId = buildingdetailed.propertyscstoreid || "";

        this.timezone = buildingdetailed.btimezone;

        try {
          this.bcontractor = JSON.parse(buildingdetailed.propertycon.replace(/\\/g, ""));
        } catch (ex) {
          this.bcontractor = [];
        }
        this.bcontractor.forEach((item) => {
          let _tempConItem = this.contractors.find(a => a.value === item)
          if (_tempConItem !== undefined) {
            this.gridSelectContractor.selected.push(_tempConItem)
          }
        });
        const radius = buildingdetailed.bradius ? parseFloat(buildingdetailed.bradius) : 0;
        this.bradius = radius == 0 || radius == NaN ? '' : radius;
        let thours = buildingdetailed.propertyhours.replace(/\\+/g, '\\')
        this.bhours = thours.replace(/(\\r\\n|\\n|\\r)/gm, "\n");
        this.bsqft = buildingdetailed.sqft;

        if (typeof buildingdetailed.propertymaps === "string") {
          if (buildingdetailed.propertymaps.trim().length > 1) {
            let maps = buildingdetailed.propertymaps.replace(/\\/g, "");
            maps = JSON.parse(maps);
            maps = maps.map((item) => parseInt(item));
            maps = [...new Set(maps)];
            this.bmaps = maps;

          }
          else
            this.bmaps = [];
        } else
          this.bmaps = [];
        if (typeof buildingdetailed.propertycontracts === "string") {
          if (buildingdetailed.propertycontracts.trim().length > 1)
            this.bcontracts = buildingdetailed.propertycontracts.split(',');
          else
            this.bcontracts = [];
        } else
          this.bcontracts = [];
        this.bexternal = buildingdetailed.propertyexsrc;
        this.bexternalid = buildingdetailed.propertyexid;
        this.$nextTick(() => {
          this.bquickbooksid = buildingdetailed.propertyqbid == null || buildingdetailed.propertyqbid == 0 ? null : parseInt(buildingdetailed.propertyqbid);
        })



        this.marker = window.L.latLng(buildingdetailed.propertylat, buildingdetailed.propertylng);
        this.buildingpoly = [];
        for (let i = 0; i < buildingdetailed.coords.length; i++) {
          this.buildingpoly.push(buildingdetailed.coords[i].split(','));

        }

        // let temp2 = this.$refs.leaflet
        let temp3 = this.buildingpoly
        this.$nextTick(() => {
          const promise1 = new Promise((resolve, reject) => {
            const loop = () => this.$refs.leaflet !== undefined ? resolve(this.$refs.leaflet) : setTimeout(loop)
            loop();
          });

          promise1.then((value) => {


            //This is in refernce to SITEFOTOS-FRONTEND-11, where the user quickly closes the dialog
            if (this.$refs.leaflet) {
              let temp = this.$refs.poly
              this.$refs.leaflet.mapObject.fitBounds(temp3);
              if (this.editedItem.mb_own == 1 && this.editedItem.mb_locked != 1)
                temp.mapObject.enableEdit();
            }


            this.loadingmain = false;
          });

        })


        if (this.editedItem.mb_min_zoom != null)
          this.editedItem.mb_min_zoom = this.editedItem.mb_min_zoom.toString();

      }
    };
    EventBus.$on('ContactAdded', contactListener);
    EventBus.$on('BuildingDetailLoaded', buildingDetailedListener);
    this.$once("hook:beforeDestroy", () => {

      EventBus.$off('ContactAdded', contactListener);
      EventBus.$off('BuildingDetailLoaded', buildingDetailedListener);
    });
    this.appleJwt = await this.appleToken();
    if (navigator.geolocation) {
      if (this.stage == 'map') {
        navigator.geolocation.getCurrentPosition(position => {
          // this.position = position.coords;
          if (typeof this.$refs.leaflet !== "undefined" && this.editedIndex == -1 && this.buildingpoly.length == 0)
            this.$refs.leaflet.mapObject.setView([position.coords.latitude, position.coords.longitude], 18)

        }, error => {
          // this.$refs.leaflet.mapObject.setView([41.881832, -87.623177], 14)
        })
      }
    }
  },
  async created() {
    this.setMaptilerProvider();
    this.loadExtraData();
  },
  methods: {
    deleteSiteLead() {
      this.$refs.utilityLib.open(
        "Delete",
        "Are you sure you want to delete this Site Lead?", "Delete", "Cancel", true).then(async function (result) {
          if (result) {
            await fetch(`/node/sites/mb-status`, {
              method: "PUT",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                newStatus: "0",
                buildingId: this.editedIndex,
              })
            });
            this.siteDetailsDialog = false
            this.$refs.dataTable.updateData(true);
          }
        }.bind(this));
    },
    activateSiteLead() {
      this.$refs.utilityLib.open(
        "Activate",
        "Are you sure you want to activate this Site Lead?", "Activate", "Cancel", true).then(async function (result) {
          if (result) {
            await fetch(`/node/sites/mb-status`, {
              method: "PUT",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                newStatus: "1",
                buildingId: this.editedIndex,
              })
            });
            this.siteDetailsDialog = false
            this.$refs.dataTable.updateData(true);
            //change site lead status to active in store
            this.$store.commit('updateSiteStatus', { id: this.editedIndex, status: '1' });
          }
        }.bind(this));
    },
    addUpdateSource(item) {
      const existingItem = this.b_source_items.findIndex(existingItem => existingItem.ssst_id == item.ssst_id)
      if (~existingItem) {
        this.b_source_items.splice(existingItem, 1, item);
      } else {
        this.b_source_items.push(item);
      }
    },
    deleteSource(item) {
      const existingItem = this.b_source_items.findIndex(existingItem => existingItem.ssst_id == item.ssst_id)
      if (~existingItem) {
        this.b_source_items.splice(existingItem, 1);
      }
    },
    addUpdateStatus(item) {
      const existingItem = this.b_site_status_items.findIndex(existingItem => existingItem.ssbs_id == item.ssbs_id)
      if (~existingItem) {
        this.b_site_status_items.splice(existingItem, 1, item);
      } else {
        this.b_site_status_items.push(item);
      }
    },
    deleteStatus(item) {
      const existingItem = this.b_site_status_items.findIndex(existingItem => existingItem.ssbs_id == item.ssbs_id)
      if (~existingItem) {
        this.b_site_status_items.splice(existingItem, 1);
      }
    },
    async uploadSiteLeadsInBulk(sitesToUpload) {
      try {
        const response = await fetch(`${myBaseURL}/node/sites/bulkuploadsiteleads`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(sitesToUpload)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Bulk upload response:", data);
        return { success: true };
      } catch (error) {
        console.error("Error uploading sites in bulk:", error);
        return { success: false };
      }
    },
    setMaptilerProvider() {
      this.mapProvider = 'maptiler';
      this.url = `/crux/maptiler/maps/144df475-27fd-49c5-8458-2aff9460e66f/256/{z}/{x}/{y}@2x.jpg`;
      this.attribution = '© MapTiler © OpenStreetMap contributors';
      this.options = {
        maxZoom: 20,
        tileSize: 512,
        zoomOffset: -1,
      };
      this.updateMap();
    },
    setGoogleProvider() {
      this.mapProvider = 'google';
      this.url = 'https://www.google.com/maps/vt?lyrs=s,h@189&gl=cn&x={x}&y={y}&z={z}';
      this.attribution = '© Google';
      this.options = {
        maxZoom: 20,
      };
      this.updateMap();
    },
    updateMap() {
      this.$nextTick(() => {
        if (this.$refs.leaflet) {
          const leafletMap = this.$refs.leaflet.mapObject;
          leafletMap.invalidateSize();

         const attributionControl = leafletMap.attributionControl;


         leafletMap.eachLayer((layer) => {
           if (layer.getAttribution) {
             attributionControl.removeAttribution(layer.getAttribution());
           }
         });


         attributionControl.addAttribution(this.attribution);

        }
      });
    },
    enableGeofenceEdit() {
      this.geofenceEditEnabled = true;
    },
    changeBuildingMaps(data) {

      this.bmaps = data;
    },
    changeContractorAssignments(data) {

      this.bcontractor = data;
    },
    async loadExtraData() {
      if (this.loadingExtraData) return;
      this.loadingExtraData = true;
      const [zones, integrations, sources, status] = await Promise.all([
        fetch(myBaseURL + '/node/sites/zones'),
        fetch(myBaseURL + '/node/vpics/integrations'),
        fetch(`${myBaseURL}/node/sites/sources`),
        fetch(`${myBaseURL}/node/sites/building-status`),
      ]);
      const [zonesJSON, data, sourcesJSON, statusesJSON] = await Promise.all([
        zones.json(),
        integrations.json(),
        sources.json(),
        status.json()
      ]);
      this.zoneList = zonesJSON;
      this.bossUser = data.bossStatus || data.bosstmStatus
      this.integrations = Object.assign({}, this.integrations, data);
      this.b_source_items = sourcesJSON;
      this.b_site_status_items = statusesJSON;
      this.b_site_status = this.b_site_status_items[0]?.ssbs_id;
      this.b_source = this.b_source_items[0]?.ssst_id;
      this.providers = [];
      if (this.integrations.aspireStatus) {
        this.providers.push('Aspire');
      }
      if (this.integrations.bosstmStatus) {
        this.providers.push('BossLM');
      }
      if (this.integrations.caseFMSStatus) {
        this.providers.push('CaseFMS');
      }
      if (this.integrations.corrigoproStatus) {
        this.providers.push('Corrigopro');
      }
      if (this.integrations.command7Status) {
        this.providers.push('Command7');
      }
      if (this.integrations.fmpStatus) {
        this.providers.push('FMPilot');
      }
      if (this.integrations.smsOneStatus) {
        this.providers.push('SMSOne');
      }
      if (this.integrations.scStatus || this.integrations.scPinStatus) {
        this.providers.push('ServiceChannel');
      }
      if (this.integrations.qbStatus) {
        const qbRequest = await fetch(`${myBaseURL}/node/quickbooks/get-contacts`)
        const qbContacts = await qbRequest.json()
        this.qbContacts = qbContacts
      }
      this.loadingExtraData = false;
    },
    async deactivateSite() {
      let resp = await fetch(`/node/sites/mb-status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          buildingId: this.editedIndex,
          newStatus: "2"
        })
      });
      resp = await resp.json();
      this.showAlert("Site has been de-activated");
      this.$store.commit('deactivateSite', this.editedIndex);
      this.$refs.dataTable.updateData(true);
      this.siteDetailsDialog = false;
    },
    async reactivateSite() {
      let resp = await fetch(`/node/sites/mb-status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          buildingId: this.editedIndex,
          newStatus: "1"
        })
      });
      resp = await resp.json();
      this.showAlert("Site has been re-activated");
      this.$store.commit('reactivateSite', this.editedIndex);
      this.$refs.dataTable.updateData(true);
      this.siteDetailsDialog = false;
    },
    async deleteSite() {
      if (!confirm('Are you sure you want to delete this site? \n\nWARNING: Any data associated with the site including photos, form submissions, site records will be lost forever on our next scheduled data scrub if you continue with this action.\n\nBy continuing with this action you agree to deletion of historical records associated with this site.')) {
        return
      }
      let resp = await fetch(`/node/sites/mb-status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          buildingId: this.editedIndex,
          newStatus: "0"
        })
      });
      resp = await resp.json();
      this.showAlert("Site has been deleted");
      this.$refs.dataTable.updateData(true);
      this.siteDetailsDialog = false;
    },
    contractsUpdated(data) {
      this.gridSelectContractor.selected = [];
    },
    openExternalPage(type) {
      if (type == 'client') {
        window.open('/home#/contacts/client', '_blank')
      } if (type == 'managers') {
        window.open('/home#/contacts/employee', '_blank')
      } else if (type == 'maps') {
        window.open('/home#/mapbuilder/filemanager', '_blank')
      } else if (type == 'estimates') {
        window.open('/home#/estimator', '_blank')
      }
    },
    showAlert(message) {
      this.snackbar.text = message;
      this.snackbar.snackbar = true;
    },

    selectExternalSite() {
      const selected = this.$refs.externalSitesTabulator?.tabulator?.getSelectedData();
      if (selected && selected.length > 0) {
        this.bexternalid = selected[0].externalID;
        this.gridSelectExternalSite.selected = selected[0].externalID;
        //Display message for duplicate external id.
        const externalpropertyid = this.bexternalid;
        const externalprovider = this.bexternal;
        const duplicateEntry = this.buildings.find((item) => {
          if (item.mb_external_id == externalpropertyid && item.mb_external_src == externalprovider && (this.editedIndex == -1 || item.mb_id != this.editedItem.mb_id)) {
            return item;
          }
        });
        if (duplicateEntry && this.editedIndex > -1) {
          // Ask user to merge this site with duplicate one. if yes then merge and if no then continue
          const sourceName = duplicateEntry.mb_nickname ?? duplicateEntry.mb_address1;
          const targetName = this.editedItem.mb_nickname ?? this.editedItem.mb_address1;
          if (!confirm(`Are you sure you want to merge ${sourceName} into ${targetName} and remove ${sourceName}?`)) {
            this.bexternalid = "";
            this.gridSelectExternalSite.selected = undefined;
          } else {
            this.gridSelectExternalSite.dialog = false;
          }
        } else if (duplicateEntry) {
          //Just alert that this site already exist.
          const sourceName = duplicateEntry.mb_nickname ?? duplicateEntry.mb_address1;
          alert(`Selected external site is already linked to ${sourceName}. Please select another site.`);
          this.bexternalid = '';
          this.gridSelectExternalSite.selected = undefined;
        } else {
          this.gridSelectExternalSite.dialog = false;
        }
      } else {
        this.bexternalid = '';
        this.gridSelectExternalSite.selected = undefined;
        this.gridSelectExternalSite.dialog = false;
      }
    },
    refreshExternalSites() {
      if (this.bexternal == 'Aspire') {
        this.fetchAspireSites();
      } else if (this.bexternal == 'CaseFMS') {
        this.fetchCaseFMSSites();
      } else if (this.bexternal == 'SMSOne') {
        this.fetchSMSOneSites();
      } else if (this.bexternal == 'Command7') {
        this.fetchCommand7Sites();
      } else if (this.bexternal == 'Corrigopro') {
        this.fetchCorrigoProSites();
      }
    },
    async fetchCommand7Sites() {
      this.gridSelectExternalSite.loading = true;
      let result = await fetch(myBaseURL + '/node/sites/command7');
      if (result.ok) {
        this.command7Sites = await result.json();
      }
      this.gridSelectExternalSite.loading = false;
    },
    async fetchCorrigoProSites() {
      this.gridSelectExternalSite.loading = true;
      let result = await fetch(myBaseURL + '/node/sites/corrigopro');
      if (result.ok) {
        this.corrigoProSites = await result.json();
      }
      this.gridSelectExternalSite.loading = false;
    },
    async fetchSMSOneSites() {
      this.gridSelectExternalSite.loading = true;
      let result = await fetch(myBaseURL + '/node/sites/smsone');
      if (result.ok) {
        this.smsoneSites = await result.json();
      }
      this.gridSelectExternalSite.loading = false;
    },
    async fetchAspireSites() {
      this.gridSelectExternalSite.loading = true;
      let result = await fetch(myBaseURL + '/node/sites/aspire');
      if (result.ok) {
        this.aspireSites = await result.json();
      }
      this.gridSelectExternalSite.loading = false;
    },
    async searchExternalSiteClicked() {
      if (this.bexternal == 'Aspire') {
        if (this.aspireSites.length == 0) {
          this.fetchAspireSites();
        }
      } else if (this.bexternal == 'CaseFMS') {
        if (this.caseFMSSites.length == 0) {
          this.fetchCaseFMSSites();
        }
      } else if (this.bexternal == 'SMSOne') {
        if (this.smsoneSites.length == 0) {
          this.fetchSMSOneSites();
        }
      } else if (this.bexternal == 'Command7') {
        if (this.command7Sites.length == 0) {
          this.fetchCommand7Sites();
        }
      } else if (this.bexternal == 'Corrigopro') {
        if (this.corrigoProSites.length == 0) {
          this.fetchCorrigoProSites();
        }
      }

      this.gridSelectExternalSite.dialog = true;
      if (this.gridSelectExternalSite.selected != this.bexternalid) {
        this.gridSelectExternalSite.selected = this.bexternalid;
      }
      await new Promise((resolve, reject) => {
        const loop = () => {
          if (
            this.$refs.externalSitesTabulator === undefined ||
            this.$refs.externalSitesTabulator.tabulator === null
          ) {
            setTimeout(loop, 100);
          } else {
            resolve(true);
          }
        };
        loop();
      });
      this.$refs.externalSitesTabulator?.tabulator?.deselectRow();
      if (this.gridSelectExternalSite.selected) {
        this.$refs.externalSitesTabulator.tabulator.selectRow(this.gridSelectExternalSite.selected.toString());
        this.$refs.externalSitesTabulator.tabulator.scrollToRow(this.gridSelectExternalSite.selected.toString(), "middle", false).catch(() => { });
      }
    },
    async fetchCaseFMSSites() {
      this.gridSelectExternalSite.loading = true;
      const requestURL = new URL(myBaseURL + '/node/sites/casefms');
      let result = await fetch(requestURL);
      if (result.ok) {
        this.caseFMSSites = await result.json();
      }
      this.gridSelectExternalSite.loading = false;
    },
    showDetailView(id) {
      this.siteDetailsDialog = true;
      const promise1 = new Promise((resolve, reject) => {
        const loop = () => this.buildings !== null && this.$refs.leaflet !== undefined ? resolve(true) : setTimeout(loop)
        loop();
      });
      promise1.then(a => {
        this.currentlyediting = this.buildings.find(a => a.mb_id.toString() == id);
      })


    },
    validateExternalSystem() {
      if (this.bexternalid && !this.bexternal) {
        return "Please select external system."
      }
      return true;
    },
    changeModeNew() {
      //const routeData = this.$router.resolve({ name: 'sitesid',params: {id: 0}})
      // window.open(routeData.href, '_blank');
      this.siteDetailsDialog = true;
      this.currentlyediting = null;
      Vue.nextTick(() => {
        const promise1 = new Promise((resolve, reject) => {
          const loop = () => this.$refs.leaflet !== undefined && this.$refs.address !== undefined ? resolve(this.$refs.leaflet) : setTimeout(loop)
          loop();
        });

        promise1.then((value) => {


          this.editedIndex = -1;
          this.$refs.address.$el.focus();
          this.$refs.leaflet.mapObject.invalidateSize();


          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(position => {
              // this.position = position.coords;
              this.$refs.leaflet.mapObject.setView([position.coords.latitude, position.coords.longitude], 18)

            }, error => {
              // this.$refs.leaflet.mapObject.setView([41.881832, -87.623177], 14)
            })
          }

        })

      })


    },
    changeMode() {
      if (this.stage == 'map') {
        this.stage = 'grid';
        /* Vue.nextTick(function () {
           this.onResize();
         }.bind(this)) */
      } else {
        this.stage = 'map';

        /* setTimeout(function () {
           if (typeof this.$refs.leaflet3 != 'undefined') {
             this.$refs.leaflet3.mapObject.fitBounds(this.$refs.markergroup2.mapObject.getBounds(), {
               padding: [30, 30]
             });
           }
         }.bind(this), 300); */

      }
    },
    async initApple() {
      let token = await fetch(`${myBaseURL}/node/maps/apple-token`);
      token = await token.json();
      return token;
    },
    async appleToken() {
      let token = fetch('https://maps-api.apple.com/v1/token', {
        "headers": {
          "authorization": `Bearer ${(await this.initApple()).token}`
        },
        "method": "GET",
      })
      let accessToken = (await token).json();
      return accessToken;
    },
    openSpreadSheetDialog() {
      this.spreadSheetDialog = true
      this.json_array = []
      this.geocoded = false
    },


    async getMapLatLng(site) {
      let mapLatLng = '';
      for (let j = 0; j < site.parsedpoly.length; j++) {
        mapLatLng += `(${site.parsedpoly[j][1]},${site.parsedpoly[j][0]})`;
        if (j != site.parsedpoly.length - 1) {
          mapLatLng += ',';
        }
      }
      if (site.parsedpoly.length == 0) {
        const lat = parseFloat(site.lat);
        const lng = parseFloat(site.lng);

        mapLatLng = "(" + (lat - 0.001) + ", " + (lng + 0.0015) + "),(" + (lat + 0.001) + ", " + (lng + 0.0015) + "),(" + (lat + 0.001) + ", " + (lng - 0.0015) + "),(" + (lat - 0.001) + ", " + (lng - 0.0015) + ")";
      }
      return mapLatLng;
    },
    async startBulkUpload() {
      try {
        this.snackbar.text = "Adding Sites to your Account in Background";
        this.snackbar.snackbar = true;
        this.spreadSheetDialog = false;
        let sitesToUpload = [];

        for (let i = 0; i < this.json_array.length; i++) {
          let site = this.json_array[i];
          if (site.status == 1) {
            let mapLatLng = await this.getMapLatLng(site);
            let payload = {
              name: site.name,
              address: site.address,
              city: site.city,
              country: site.country,
              state: site.state,
              zip: parseInt(site.zip),
              lat: site.lat,
              lng: site.lng,
              contractor: JSON.stringify([]),
              maps: JSON.stringify([])
            };
            sitesToUpload.push(payload);
          }
        }

        if (sitesToUpload.length > 0) {
          const result = await this.uploadSitesInBulk(sitesToUpload, this.pageType);
          if (result.success) {
            this.snackbar.text = "Sites have been successfully uploaded.";
          } else {
            this.snackbar.text = "An error occurred while uploading sites. Please try again later.";
          }
          this.snackbar.snackbar = true;
        }
      } catch (error) {
        console.error("Error in addGeocodedSitesWithoutApi:", error);
        this.snackbar.text = "An error occurred while processing sites.";
        this.snackbar.snackbar = true;
      }
    },
    async uploadSitesInBulk(sitesToUpload, pageType = 'sites') {
      try {
        const url = pageType === 'sitesLeads' ? `${myBaseURL}/node/sites/bulkuploadsiteleads` : `${myBaseURL}/node/sites/bulkupload`
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(sitesToUpload)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Bulk upload response:", data);
        return { success: true };
      } catch (error) {
        console.error("Error uploading sites in bulk:", error);
        return { success: false };
      }
    },
    async callGeoCodeApi() {
      //This method gets called when we click geocode button on front-end
      this.loaderType.circle = false
      this.loaderType.progress = true
      this.dialogLoading = true;
      this.dialogLoadingText = "Geocoding your sheet";

      let json_array_tmp = [...this.json_array];
      let temp = [];
      for (let i = 0; i < json_array_tmp.length; i++) {
        let item = json_array_tmp[i];

        this.percentLoader = (100 * i + 1) / json_array_tmp.length;

        if (item.status == 1) {
          //Already geocoded
          temp.push(item)
          continue;
        }

        else if (item.lat != undefined && item.lng != undefined) {
          item.parsedpoly = await this.getParcels(item.lat, item.lng);

          // //here we need to check if address, city, state, zip are present, if not we need to reverse geocode
          if (item.address == undefined || item.city == undefined || item.state == undefined || item.zip == undefined) {

            const response = await fetch(`https://maps-api.apple.com/v1/reverseGeocode?loc=${item.lat},${item.lng}`, {
              headers: {
                Authorization: "Bearer " + this.appleJwt.accessToken
              },
              method: "GET"
            });
            const reverseGeocodedApple = await response.json();
            if (reverseGeocodedApple.error) {
              if (reverseGeocodedApple.error.message == 'Not Authorized') {
                //We need to refresh token
                this.appleJwt = await this.appleToken();
              }
            }
            else if (reverseGeocodedApple.results.length > 0) {
              //Apple Geocoding
              item.address = reverseGeocodedApple.results[0].name;
              item.city = reverseGeocodedApple.results[0].structuredAddress.locality;
              item.state = reverseGeocodedApple.results[0].structuredAddress.administrativeAreaCode;
              item.zip = reverseGeocodedApple.results[0].structuredAddress.postCode;
              item.status = 1;
              temp.push(item);
              continue;
            }
            else {
              //apple failed, fall to google
              const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${item.lat},${item.lng}&key=AIzaSyA5RJ6ybPQAOIVNtTI8O9xuyAuOAnrnMg8`);
              const googleReverseGeocodeForWhichAppleFailed = await response.json();

              if (googleReverseGeocodeForWhichAppleFailed.status === "OK" && googleReverseGeocodeForWhichAppleFailed.results.length > 0) {
                const addressComponents = googleReverseGeocodeForWhichAppleFailed.results[0].address_components;
                let address = "";
                let city = "";
                let state = "";
                let zip = "";

                for (const component of addressComponents) {
                  if (component.types.includes("street_address")) {
                    address = component.long_name;
                  } else if (component.types.includes("locality")) {
                    city = component.long_name;
                  } else if (component.types.includes("administrative_area_level_1")) {
                    state = component.short_name;
                  } else if (component.types.includes("postal_code")) {
                    zip = component.long_name;
                  }
                }

                // If address is still empty, assign it the combined street number and route
                if (address.trim() === "") {
                  for (const component of addressComponents) {
                    if (component.types.includes("street_number")) {
                      address += component.long_name + " "; // Concatenate street number
                    } else if (component.types.includes("route")) {
                      address += component.long_name; // Concatenate route
                    }
                  }
                }
                address = address.trim();
                item.address = address;
                item.city = city;
                item.state = state;
                item.zip = zip;
                item.status = 1;
                temp.push(item);
                continue;
              }

            }
          }

          else {
            item.status = 1;
            temp.push(item);
            continue;
          }

        }


        let geocoded = await fetch(`https://maps-api.apple.com/v1/geocode?q=${item.address} ${item.city} ${item.state} ${item.zip}`, {
          "headers": {
            "authorization": "Bearer " + this.appleJwt.accessToken
          },
          "body": null,
          "method": "GET",

        });
        let geojson = await geocoded.json();
        if (geojson.error) {
          if (geojson.error.message == 'Not Authorized') {
            //We need to refresh token
            this.appleJwt = await this.appleToken();
          }
        }
        else if (geojson.results.length > 0) {
          //Apple Geocoding
          let coOrdinate = geojson.results[0].coordinate;
          item.lat = parseFloat(coOrdinate.latitude).toFixed(4);
          item.lng = parseFloat(coOrdinate.longitude).toFixed(4);
          item.status = 1;
          item.parsedpoly = await this.getParcels(item.lat, item.lng);
        }
        else {
          //Google geocoding
          let googleAddressForWhichAppleFailed = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${item.address} ${item.city} ${item.state} ${item.zip}&key=AIzaSyA5RJ6ybPQAOIVNtTI8O9xuyAuOAnrnMg8`)
          googleAddressForWhichAppleFailed = await googleAddressForWhichAppleFailed.json();
          if (googleAddressForWhichAppleFailed && googleAddressForWhichAppleFailed.results && googleAddressForWhichAppleFailed.results[0]) {
            //If no error this block will execute
            //Then we need to call parcel server and fetch relevent info
            let coOrdinate = googleAddressForWhichAppleFailed.results[0].geometry.location;
            item.lat = parseFloat(coOrdinate.lat).toFixed(4);
            item.lng = parseFloat(coOrdinate.lng).toFixed(4);
            item.status = 1;
            item.parsedpoly = await this.getParcels(item.lat, item.lng);
          } else {
            //Failed sites
            item.status = 0;
          }
        }
        temp.push(item)
        this.geocoded = true

      }
      this.json_array = temp
      this.dialogLoading = false
    },
    async getParcels(lat, lon) {
      const response = await fetch(`https://parcels.sitefotos.com/1/getdata?lat=${lat}&lon=${lon}`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + mytoken
        }
      });


      if (response.status === 200) {
        const data = await response.json();
        if (data.type === "Polygon") {
          return data.coordinates[0];
        } else if (data.type === "MultiPolygon") {
          const parsedPoly = [];
          for (const polygon of data.coordinates) {
            parsedPoly.push(...polygon[0]);
          }
          return parsedPoly;
        }
      }

      return [];
    },

    deleteRow(item) {
      this.json_array.splice(this.json_array.findIndex(ja => (ja.name == item.name && ja.address == item.address)), 1);
    },
    dragOver(event) {
      event.preventDefault();
      this.showEffect();
    },
    dragLeave(event) {
      event.preventDefault();
      this.resetEffect();
    },
    drop(event) {
      event.preventDefault();
      let files = event.target.files || (event.dataTransfer && event.dataTransfer.files);
      this.csv_file = files[0]
      this.resetEffect();
      this.onChange();
    },
    showEffect() {
      document.querySelector(".drop-rect").style.border = "2px #1976d2 solid";
    },
    resetEffect() {
      document.querySelector(".drop-rect").style.border = "1px gray solid";
    },
    rectClick() {
      this.$refs.csvarea.style.display = "";
      this.$refs.csvarea.style.width = "100%";
      this.$refs.csvarea.style.height = "100%";
      this.$refs.csvarea.focus();
      this.$refs.csvarea.select();
      this.$refs.recttext.style.height = "0";
      this.$refs.recttext.style.display = "none";
    },
    onPaste(event) {
      let self = this;
      setTimeout(async function () {
        try {
          self.dialogLoading = true
          self.dialogLoadingText = "Parsing your sheet"
          let tmp_array = await CSVJSON.csv2json(event.target.value, {
            parseNumbers: true
          });
          let tmp = tmp_array[0];
          if (Object.entries(tmp).length < 4) {
            console.log("csv data must be 4 columns...");
          } else {
            self.json_array = self.convertKeysToLowerCase(tmp_array);
            self.json_array = self.json_array.map(v => ({ ...v, status: v.lat > 0 ? 4 : 0 }));
            setTimeout(function () {
              self.dialogLoading = false
              self.callGeoCodeApi();
            }, 1000);
          }
        } catch (e) {
          console.log("onBlur exception", e);
        } finally {
          self.$refs.csvarea.style.display = "none";
        }
      }, 1000);
    },
    onChange() {
      this.loaderType.circle = true
      this.loaderType.progress = false
      this.dialogLoading = true
      this.dialogLoadingText = "Parsing your sheet"
      let self = this;
      let reader = new FileReader();
      setTimeout(function () {
        reader.onload = async function (e) {
          let data = new Uint8Array(e.target.result);
          let workbook = XLSX.read(data, { type: "array" });
          let sheetName = workbook.SheetNames[0];
          let worksheet = workbook.Sheets[sheetName];

          let columns = self.getSheetHeader(worksheet);

          if (columns > 4) {
            let tmp = await XLSX.utils.sheet_to_json(worksheet);
            self.json_array = await self.convertKeysToLowerCase(tmp);
            self.json_array = self.json_array.map(v => ({ ...v, status: v.lat > 0 ? 4 : 0 }));
          }

          setTimeout(function () {
            self.dialogLoading = false
            self.callGeoCodeApi();
          }, 1000);
        };
        reader.readAsArrayBuffer(self.csv_file);
      }, 1000);
    },
    getSheetHeader(sheet) {
      let columns = 0;
      let range = XLSX.utils.decode_range(sheet["!ref"]);

      for (let C = range.s.c; C <= range.e.c; ++C) {
        columns++;
      }
      return columns;
    },
    convertKeysToLowerCase(arr) {
      let output = [];
      for (let i = 0; i < arr.length; i++) {
        let tmp = {};
        for (let obj of Object.entries(arr[i])) {
          if (!obj) continue;
          //Here we will check if headers in sheet are broken and fix them
          let key = obj[0].toString().toLowerCase();
          if (key.includes("nam")) {
            key = "name";
          }
          if (key.includes("add")) {
            key = "address";
          }
          if (key.includes("cit")) {
            key = "city";
          }
          if (key.includes("sta")) {
            key = "state";
          }
          if (key.includes("zi")) {
            key = "zip";
          }
          if (key.includes("la") || key.includes("lat")) {
            key = "lat";
          }
          if (key.includes("ln") || key.includes("long")) {
            key = "lng";
          }
          let val = obj[1].toString();
          tmp[key] = val;
        }
        output.push(tmp);
      }
      return output;
    },
    saveXls() {
      this.snack = true;
      this.snackColor = "success";
      this.snackText = "Data saved";
    },
    cancel() {
      this.snack = true;
      this.snackColor = "error";
      this.snackText = "Canceled";
    },
    open() {
      this.snack = true;
      this.snackColor = "info";
      this.snackText = "Dialog opened";
    },
    //Above are spreadsheet methods
    preOpenContractorsDialog() {
      this.gridSelectContractor.tempSelection = [...this.gridSelectContractor.selected];
      this.gridSelectContractor.dialog = true;
    },
    saveContractors() {
      this.gridSelectContractor.selected = [...this.gridSelectContractor.tempSelection];
      this.gridSelectContractor.dialog = false;
    },
    cancelContractorDialog() {
      this.gridSelectContractor.dialog = false;
    },
    updateSelected(event) {
      this.bcontractor = event.map(a => a.value)
    },
    filterCustomers() {
      let k = this.gridSelect.showSelected ? this.gridSelect.selected : this.customers;
      if (this.gridSelect.filter.company_name)
        if (this.gridSelect.filter.company_name.length > 0) {
          k = k.filter(item => (item.company_name !== null) && item.company_name.toLowerCase().includes(this.gridSelect.filter.company_name.toLowerCase()));
        }

      if (this.gridSelect.filter.fname)
        if (this.gridSelect.filter.fname.length > 0) {
          k = k.filter(item => (item.fname !== null) && item.fname.toLowerCase().includes(this.gridSelect.filter.fname.toLowerCase()));
        }

      if (this.gridSelect.filter.lname)
        if (this.gridSelect.filter.lname.length > 0) {
          k = k.filter(item => (item.lname !== null) && item.lname.toLowerCase().includes(this.gridSelect.filter.lname.toLowerCase()));
        }

      if (this.gridSelect.filter.email)
        if (this.gridSelect.filter.email.length > 0) {
          k = k.filter(item => (item.email !== null) && item.email.toLowerCase().includes(this.gridSelect.filter.email.toLowerCase()));
        }

      if (this.gridSelect.filter.cell)
        if (this.gridSelect.filter.cell.length > 0) {
          k = k.filter(item => (item.cell !== null) && item.cell.toLowerCase().includes(this.gridSelect.filter.cell.toLowerCase()));
        }
      return k;
    },
    filterContractors() {
      let k = this.gridSelectContractor.showSelected ? this.gridSelectContractor.tempSelection : this.contractors;
      if (this.gridSelectContractor.filter.group)
        if (this.gridSelectContractor.filter.group.length > 0) {
          k = k.filter(item => (item.group !== null) && item.group.toLowerCase().includes(this.gridSelectContractor.filter.group.toLowerCase()));
        }

      if (this.gridSelectContractor.filter.fname)
        if (this.gridSelectContractor.filter.fname.length > 0) {
          k = k.filter(item => (item.fname !== null) && item.fname.toLowerCase().includes(this.gridSelectContractor.filter.fname.toLowerCase()));
        }

      if (this.gridSelectContractor.filter.lname)
        if (this.gridSelectContractor.filter.lname.length > 0) {
          k = k.filter(item => (item.lname !== null) && item.lname.toLowerCase().includes(this.gridSelectContractor.filter.lname.toLowerCase()));
        }

      if (this.gridSelectContractor.filter.email)
        if (this.gridSelectContractor.filter.email.length > 0) {
          k = k.filter(item => (item.email !== null) && item.email.toLowerCase().includes(this.gridSelectContractor.filter.email.toLowerCase()));
        }

      if (this.gridSelectContractor.filter.contractor)
        if (this.gridSelectContractor.filter.contractor.length > 0) {
          k = k.filter(item => (item.contractor !== null) && item.cell.toLowerCase().includes(this.gridSelectContractor.filter.contractor.toLowerCase()));
        }
      if (this.gridSelectContractor.filter.cell)
        if (this.gridSelectContractor.filter.cell.length > 0) {
          k = k.filter(item => (item.cell !== null) && item.cell.toLowerCase().includes(this.gridSelectContractor.filter.cell.toLowerCase()));
        }
      return k;
    },
    filterEmployees() {
      let k = this.gridSelectAccountManager.showSelected ? this.gridSelectAccountManager.selected : this.employees;
      if (this.gridSelectAccountManager.filter.fname)
        if (this.gridSelectAccountManager.filter.fname.length > 0) {
          k = k.filter(item => (item.fname !== null) && item.fname.toLowerCase().includes(this.gridSelectAccountManager.filter.fname.toLowerCase()));
        }

      if (this.gridSelectAccountManager.filter.lname)
        if (this.gridSelectAccountManager.filter.lname.length > 0) {
          k = k.filter(item => (item.lname !== null) && item.lname.toLowerCase().includes(this.gridSelectAccountManager.filter.lname.toLowerCase()));
        }

      if (this.gridSelectAccountManager.filter.email)
        if (this.gridSelectAccountManager.filter.email.length > 0) {
          k = k.filter(item => (item.email !== null) && item.email.toLowerCase().includes(this.gridSelectAccountManager.filter.email.toLowerCase()));
        }

      if (this.gridSelectAccountManager.filter.cell)
        if (this.gridSelectAccountManager.filter.cell.length > 0) {
          k = k.filter(item => (item.contractor !== null) && item.cell.toLowerCase().includes(this.gridSelectAccountManager.filter.cell.toLowerCase()));
        }
      return k;
    },
    closePricing() {
      this.pricingVisible = false;
    },
    closePricingContractor() {
      this.pricingContractorVisible = false;
    },
    async savePin() {
      if (this.scPin.length < 2) {
        this.snackbar.text = "Please enter a valid pin";
        this.snackbar.snackbar = true;
        return;
      }
      if (this.scStoreId.length < 2) {
        this.snackbar.text = "Please enter a valid store id";
        this.snackbar.snackbar = true;
        return;
      }
      if (this.bexternalid || this.bexternal) {
        let m = confirm("Are you sure you want to link this site to a different external site? This will unlink the site from the current external site.")
        if (!m) {
          return;
        }

      }

      this.scPinLoading = true;
      let req = await fetch(myBaseURL + `/node/workorder/link-sc-pin-site?pin=${this.scPin}&storeid=${this.scStoreId}&siteid=${this.editedItem.mb_id}`)
      if (req.ok) {
        let res = await req.json();
        if (res.id) {
          this.bexternalid = res.id;
          this.bexternal = 'ServiceChannel'
          this.snackbar.text = "Pin linked successfully";
          this.snackbar.snackbar = true;
          this.scPinLoading = false;
          this.scPinDialog = false;
          return;
        }
        else {
          this.snackbar.text = "Invalid pin or store id";
          this.snackbar.snackbar = true;
          this.scPinLoading = false;
          return;
        }
      }
      else {
        this.snackbar.text = "Invalid pin or store id";
        this.snackbar.snackbar = true;
        this.scPinLoading = false;
        return;
      }
      this.scPinLoading = false;


    },
    addnewContractType() {
      let rolename = this.newContractType.trim();
      let contract = this.mycontracttypes.find(a => a == rolename)
      if (typeof contract != 'undefined') {
        this.snackbar.text = "Contract type already exists.";
        this.snackbar.snackbar = true;
        return;
      }
      else {
        this.mycontracttypes.push(rolename);
        this.bcontracts.push(rolename);
        this.contracttypeadddialog = false;

      }


    },
    logout() {
      window.location = myBaseURL + '/index/logout'
    },

    newsite() {
      this.currentlyediting = null

    },

    getAddressData: async function (addressData, result, id) {
      if (result !== undefined) {
        if (!result.geometry) {
          this.snackbar.text = 'Location not found';
          this.snackbar.snackbar = true;

          return;
        }
        if (this.geofenceEditEnabled) {
          // Only update the geofence coordinates
          this.buildingpoly = [];
          let response = await fetch(`https://parcels.sitefotos.com/1/getdata?lat=${result.geometry.location.lat()}&lon=${result.geometry.location.lng()}`, {
            method: 'GET',
            headers: {
              'Authorization': 'Bearer ' + mytoken
            }
          })

          if (response.status == 200) {
            response = await response.json();
            let poly = {}
            if (response.type === 'MultiPolygon') {
              for (let i = 0; i < response.coordinates.length; i++) {
                poly = {
                  'type': 'Polygon',
                  'coordinates': response.coordinates[i]
                };
              }
            }
            else {
              poly = response;
            }
            let latlngs = L.GeoJSON.coordsToLatLngs(poly.coordinates, 1)
            this.buildingpoly = JSON.parse(JSON.stringify(latlngs[0]));
          } else {
            this.buildingpoly = [];
            this.buildingpoly.push([result.geometry.location.lat() - 0.001, result.geometry.location.lng() + 0.0015]);
            this.buildingpoly.push([result.geometry.location.lat() + 0.001, result.geometry.location.lng() + 0.0015]);
            this.buildingpoly.push([result.geometry.location.lat() + 0.001, result.geometry.location.lng() - 0.0015]);
            this.buildingpoly.push([result.geometry.location.lat() - 0.001, result.geometry.location.lng() - 0.0015]);
            this.marker = window.L.latLng(result.geometry.location.lat(), result.geometry.location.lng());
          }

          setTimeout(function afterTwoSeconds() {
            let temp = this.$refs.poly
            this.$refs.leaflet.mapObject.fitBounds(this.buildingpoly);
            temp.mapObject.enableEdit();
            this.$refs.leaflet.mapObject.openPopup("Drag the corners of the shape to adjust geofence", this.$refs.leaflet.mapObject.getCenter())
          }.bind(this), 400)

          this.geofenceEditEnabled = false;
        } else {
          // this.$refs.poly.mapObject.disableEdit()
          this.buildingpoly = [];
          let response = await fetch(`https://parcels.sitefotos.com/1/getdata?lat=${result.geometry.location.lat()}&lon=${result.geometry.location.lng()}`, {
            method: 'GET',
            headers: {
              'Authorization': 'Bearer ' + mytoken
            }
          })


          if (response.status != 200) {

            this.buildingpoly.push([result.geometry.location.lat() - 0.001, result.geometry.location.lng() + 0.0015]);
            this.buildingpoly.push([result.geometry.location.lat() + 0.001, result.geometry.location.lng() + 0.0015]);
            this.buildingpoly.push([result.geometry.location.lat() + 0.001, result.geometry.location.lng() - 0.0015]);
            this.buildingpoly.push([result.geometry.location.lat() - 0.001, result.geometry.location.lng() - 0.0015]);
            this.marker = window.L.latLng(result.geometry.location.lat(), result.geometry.location.lng());

            if (result.address_components) {

              for (let i = 0; i < result.address_components.length; i++) {
                if (result.address_components[i].types[0] == "administrative_area_level_1")
                  this.editedItem.statename = [(result.address_components[i] && result.address_components[i].short_name || '')].join(' ');
                if (result.address_components[i].types[0] == "locality")
                  this.editedItem.cityname = [(result.address_components[i] && result.address_components[i].short_name || '')].join(' ');
                if (result.address_components[i].types[0] == "postal_code")
                  this.editedItem.mb_zip_code = [(result.address_components[i] && result.address_components[i].short_name || '')].join(' ');
                if (result.address_components[i].types[0] == "country") {
                  const short_name = (result.address_components[i] && result.address_components[i].short_name || '')
                  const country = this.countries.find(c => c.initials == short_name)

                  this.editedItem.mb_country = country.id || "254"
                }
              }
              this.editedItem.mb_address1 = [(result.address_components[0] && result.address_components[0].short_name || ''), (result.address_components[1] && result.address_components[1].short_name || '')].join(' ')
              if (this.editedIndex == -1)
                this.editedItem.mb_nickname = "";

            }

            this.$nextTick(() => {
              let temp = this.$refs.poly
              this.$refs.leaflet.mapObject.fitBounds(this.buildingpoly);
              temp.mapObject.enableEdit();
              this.$refs.leaflet.mapObject.openPopup("Drag the corners of the rectangle to adjust site", this.$refs.leaflet.mapObject.getCenter())
              this.$refs.sitename.focus()
            })
          } else {
            response = await response.json();
            let poly = {}
            if (response.type === 'MultiPolygon') {
              for (let i = 0; i < response.coordinates.length; i++) {
                poly = {
                  'type': 'Polygon',
                  'coordinates': response.coordinates[i]
                };
              }
            }
            else {
              poly = response;
            }
            let latlngs = L.GeoJSON.coordsToLatLngs(poly.coordinates, 1)

            this.buildingpoly = JSON.parse(JSON.stringify(latlngs[0]));
            this.marker = window.L.latLng(result.geometry.location.lat(), result.geometry.location.lng());

            if (result.address_components) {

              for (let i = 0; i < result.address_components.length; i++) {

                if (result.address_components[i].types[0] == "administrative_area_level_1")
                  this.editedItem.statename = [(result.address_components[i] && result.address_components[i].short_name || '')].join(' ');
                if (result.address_components[i].types[0] == "locality")
                  this.editedItem.cityname = [(result.address_components[i] && result.address_components[i].short_name || '')].join(' ');
                if (result.address_components[i].types[0] == "postal_code")
                  this.editedItem.mb_zip_code = [(result.address_components[i] && result.address_components[i].short_name || '')].join(' ');
                if (result.address_components[i].types[0] == "country") {
                  const short_name = (result.address_components[i] && result.address_components[i].short_name || '')
                  const country = this.countries.find(c => c.initials == short_name)

                  this.editedItem.mb_country = country.id || "254"

                }
              }
              this.editedItem.mb_address1 = [(result.address_components[0] && result.address_components[0].short_name || ''), (result.address_components[1] && result.address_components[1].short_name || '')].join(' ')
              if (this.editedIndex == -1)
                this.editedItem.mb_nickname = "";

            }

            setTimeout(function afterTwoSeconds() {
              let temp = this.$refs.poly
              this.$refs.leaflet.mapObject.fitBounds(this.buildingpoly);
              temp.mapObject.enableEdit();
              this.$refs.leaflet.mapObject.openPopup("Drag the corners of the shape to adjust geofence", this.$refs.leaflet.mapObject.getCenter())
              this.$refs.sitename.focus()
            }.bind(this), 400)


          }
        }
      }
    },
    buildclick(evt) {

    },
    polyclick(evt) {

    },
    addcontractor() {
      EventBus.$emit('AddContact', "Contractor");
    },
    addemployee() {
      EventBus.$emit('AddContact', "Employee");
    },
    addcustomer() {
      EventBus.$emit('AddContact', "Client");
    },

    parseEmails(emails) {
      let parsedEmails = "";
      let k = emails.toLowerCase().split(/[\s,;\t\n]+/);
      for (let i = 0; i < k.length; i++) {
        if (this.isValidEmail(k[i]))
          parsedEmails += k[i] + ",";
      }
      return parsedEmails.slice(0, -1);
    },

    isValidEmail(email) {
      let regex = /\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b/i;
      return (regex.test(email)) ? true : false;
    },

    removeDiacritics(str) {

      //remove tab from string
      str = str.replace('\t', ' ');

      let defaultDiacriticsRemovalMap = [{
        'base': 'A',
        'letters': /[\u0041\u24B6\uFF21\u00C0\u00C1\u00C2\u1EA6\u1EA4\u1EAA\u1EA8\u00C3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\u00C4\u01DE\u1EA2\u00C5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F]/g
      },
      {
        'base': 'AA',
        'letters': /[\uA732]/g
      },
      {
        'base': 'AE',
        'letters': /[\u00C6\u01FC\u01E2]/g
      },
      {
        'base': 'AO',
        'letters': /[\uA734]/g
      },
      {
        'base': 'AU',
        'letters': /[\uA736]/g
      },
      {
        'base': 'AV',
        'letters': /[\uA738\uA73A]/g
      },
      {
        'base': 'AY',
        'letters': /[\uA73C]/g
      },
      {
        'base': 'B',
        'letters': /[\u0042\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0182\u0181]/g
      },
      {
        'base': 'C',
        'letters': /[\u0043\u24B8\uFF23\u0106\u0108\u010A\u010C\u00C7\u1E08\u0187\u023B\uA73E]/g
      },
      {
        'base': 'D',
        'letters': /[\u0044\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018B\u018A\u0189\uA779]/g
      },
      {
        'base': 'DZ',
        'letters': /[\u01F1\u01C4]/g
      },
      {
        'base': 'Dz',
        'letters': /[\u01F2\u01C5]/g
      },
      {
        'base': 'E',
        'letters': /[\u0045\u24BA\uFF25\u00C8\u00C9\u00CA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\u00CB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E]/g
      },
      {
        'base': 'F',
        'letters': /[\u0046\u24BB\uFF26\u1E1E\u0191\uA77B]/g
      },
      {
        'base': 'G',
        'letters': /[\u0047\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E]/g
      },
      {
        'base': 'H',
        'letters': /[\u0048\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D]/g
      },
      {
        'base': 'I',
        'letters': /[\u0049\u24BE\uFF29\u00CC\u00CD\u00CE\u0128\u012A\u012C\u0130\u00CF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197]/g
      },
      {
        'base': 'J',
        'letters': /[\u004A\u24BF\uFF2A\u0134\u0248]/g
      },
      {
        'base': 'K',
        'letters': /[\u004B\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2]/g
      },
      {
        'base': 'L',
        'letters': /[\u004C\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780]/g
      },
      {
        'base': 'LJ',
        'letters': /[\u01C7]/g
      },
      {
        'base': 'Lj',
        'letters': /[\u01C8]/g
      },
      {
        'base': 'M',
        'letters': /[\u004D\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C]/g
      },
      {
        'base': 'N',
        'letters': /[\u004E\u24C3\uFF2E\u01F8\u0143\u00D1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u0220\u019D\uA790\uA7A4]/g
      },
      {
        'base': 'NJ',
        'letters': /[\u01CA]/g
      },
      {
        'base': 'Nj',
        'letters': /[\u01CB]/g
      },
      {
        'base': 'O',
        'letters': /[\u004F\u24C4\uFF2F\u00D2\u00D3\u00D4\u1ED2\u1ED0\u1ED6\u1ED4\u00D5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\u00D6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\u00D8\u01FE\u0186\u019F\uA74A\uA74C]/g
      },
      {
        'base': 'OI',
        'letters': /[\u01A2]/g
      },
      {
        'base': 'OO',
        'letters': /[\uA74E]/g
      },
      {
        'base': 'OU',
        'letters': /[\u0222]/g
      },
      {
        'base': 'P',
        'letters': /[\u0050\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754]/g
      },
      {
        'base': 'Q',
        'letters': /[\u0051\u24C6\uFF31\uA756\uA758\u024A]/g
      },
      {
        'base': 'R',
        'letters': /[\u0052\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782]/g
      },
      {
        'base': 'S',
        'letters': /[\u0053\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784]/g
      },
      {
        'base': 'T',
        'letters': /[\u0054\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786]/g
      },
      {
        'base': 'TZ',
        'letters': /[\uA728]/g
      },
      {
        'base': 'U',
        'letters': /[\u0055\u24CA\uFF35\u00D9\u00DA\u00DB\u0168\u1E78\u016A\u1E7A\u016C\u00DC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244]/g
      },
      {
        'base': 'V',
        'letters': /[\u0056\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245]/g
      },
      {
        'base': 'VY',
        'letters': /[\uA760]/g
      },
      {
        'base': 'W',
        'letters': /[\u0057\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72]/g
      },
      {
        'base': 'X',
        'letters': /[\u0058\u24CD\uFF38\u1E8A\u1E8C]/g
      },
      {
        'base': 'Y',
        'letters': /[\u0059\u24CE\uFF39\u1EF2\u00DD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE]/g
      },
      {
        'base': 'Z',
        'letters': /[\u005A\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762]/g
      },
      {
        'base': 'a',
        'letters': /[\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250]/g
      },
      {
        'base': 'aa',
        'letters': /[\uA733]/g
      },
      {
        'base': 'ae',
        'letters': /[\u00E6\u01FD\u01E3]/g
      },
      {
        'base': 'ao',
        'letters': /[\uA735]/g
      },
      {
        'base': 'au',
        'letters': /[\uA737]/g
      },
      {
        'base': 'av',
        'letters': /[\uA739\uA73B]/g
      },
      {
        'base': 'ay',
        'letters': /[\uA73D]/g
      },
      {
        'base': 'b',
        'letters': /[\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253]/g
      },
      {
        'base': 'c',
        'letters': /[\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184]/g
      },
      {
        'base': 'd',
        'letters': /[\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A]/g
      },
      {
        'base': 'dz',
        'letters': /[\u01F3\u01C6]/g
      },
      {
        'base': 'e',
        'letters': /[\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD]/g
      },
      {
        'base': 'f',
        'letters': /[\u0066\u24D5\uFF46\u1E1F\u0192\uA77C]/g
      },
      {
        'base': 'g',
        'letters': /[\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F]/g
      },
      {
        'base': 'h',
        'letters': /[\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265]/g
      },
      {
        'base': 'hv',
        'letters': /[\u0195]/g
      },
      {
        'base': 'i',
        'letters': /[\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131]/g
      },
      {
        'base': 'j',
        'letters': /[\u006A\u24D9\uFF4A\u0135\u01F0\u0249]/g
      },
      {
        'base': 'k',
        'letters': /[\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3]/g
      },
      {
        'base': 'l',
        'letters': /[\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747]/g
      },
      {
        'base': 'lj',
        'letters': /[\u01C9]/g
      },
      {
        'base': 'm',
        'letters': /[\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F]/g
      },
      {
        'base': 'n',
        'letters': /[\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5]/g
      },
      {
        'base': 'nj',
        'letters': /[\u01CC]/g
      },
      {
        'base': 'o',
        'letters': /[\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275]/g
      },
      {
        'base': 'oi',
        'letters': /[\u01A3]/g
      },
      {
        'base': 'ou',
        'letters': /[\u0223]/g
      },
      {
        'base': 'oo',
        'letters': /[\uA74F]/g
      },
      {
        'base': 'p',
        'letters': /[\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755]/g
      },
      {
        'base': 'q',
        'letters': /[\u0071\u24E0\uFF51\u024B\uA757\uA759]/g
      },
      {
        'base': 'r',
        'letters': /[\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783]/g
      },
      {
        'base': 's',
        'letters': /[\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B]/g
      },
      {
        'base': 't',
        'letters': /[\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787]/g
      },
      {
        'base': 'tz',
        'letters': /[\uA729]/g
      },
      {
        'base': 'u',
        'letters': /[\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289]/g
      },
      {
        'base': 'v',
        'letters': /[\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C]/g
      },
      {
        'base': 'vy',
        'letters': /[\uA761]/g
      },
      {
        'base': 'w',
        'letters': /[\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73]/g
      },
      {
        'base': 'x',
        'letters': /[\u0078\u24E7\uFF58\u1E8B\u1E8D]/g
      },
      {
        'base': 'y',
        'letters': /[\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF]/g
      },
      {
        'base': 'z',
        'letters': /[\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763]/g
      }
      ];

      for (let i = 0; i < defaultDiacriticsRemovalMap.length; i++) {
        str = str.replace(defaultDiacriticsRemovalMap[i].letters, defaultDiacriticsRemovalMap[i].base);
      }

      return str;

    },
    mapload(evt) {

      const zoomControl = new window.L.control.zoom().addTo(this.$refs.leaflet.mapObject);

    },

    async editItem(item) {
      await new Promise((resolve, reject) => {
        const loop = () => this.initialDataLoaded ? resolve(true) : setTimeout(loop)
        loop();
      });
      this.bappnotes = "";
      this.bappprofile = [];
      this.bcontractor = [];
      this.bemployee = "";


      this.bemails = "";
      this.bnotes = "";
      this.bemployee = "";
      this.bcontractor = [];
      this.bhours = "";
      this.bradius = "";
      this.bsqft = "";
      this.bmaps = [];
      this.bcontracts = [];
      this.bexternal = "";
      this.bexternalid = "";
      this.bquickbooksid = null;
      this.bzone = "";
      this.bsharesetting = "2";
      this.primaryClient = this.secondaryClient = this.thirdClient = undefined;
      this.b_source = this.b_site_status = undefined;
      this.editedIndex = this.buildings.indexOf(item)
      this.editedItem = Object.assign({}, item)
      let payload = {
        bid: item.mb_id,
      }
      this.$store.commit('getBuildingDetailed', payload);
      this.dialog = true
    },
    async disconnectItem(item) {
      if (confirm('Are you sure you want to disconnect this site? \n\nWARNING: This site has been contracted to you and if you disconnect it, it will no longer appear in your account and your team will no longer be able to work on it in the field.')) {
        await fetch(`${myBaseURL}/node/sites/disconnect-site?id=${this.editedItem.mb_id}`)
        this.snackbar.text = "Site Disconnected"
        this.snackbar.snackbar = true;

        this.siteDetailsDialog = false;
        this.$refs.dataTable.updateData(true);
        this.loadingmain = false;
      }
    },
    closecustom() {
      this.customdialog = false;
      this.customvarname = "";
      this.customvarvalue = "";
      this.valid = true;
    },

    savecustom() {
      if (this.$refs.customform.validate()) {
        let obj = {}
        obj.key = this.customvarname.replace(/ /g, "_");
        obj.value = this.customvarvalue;
        obj.label = this.customvarname;
        obj.kid = 0;
        obj.vid = 0;
        this.customfields.push(obj);
        this.closecustom();
      }
    },

    close() {

      this.dialog = false
      setTimeout(() => {
        this.editedItem = Object.assign({}, this.defaultItem)
        this.editedIndex = -1
        this.$refs.poly.mapObject.disableEdit();
        this.marker = null;
        this.buildingpoly = [];
        this.center = L.latLng(41.881832, -87.623177);
        this.zoom = 3;
        this.$refs.leaflet.mapObject.setView([41.881832, -87.623177], 14)
      }, 400)
    },

    showPricing() {
      this.componentKey += 1;
      this.pricingVisible = true;
    },
    showPricingContractor() {
      this.componentKey += 1;
      this.pricingContractorVisible = true;
    },

    save() {

      // this.bcontractor = this.gridSelectContractor.selected.map(a => a.value);
      try {
        let radius = this.bradius ? parseFloat(this.bradius) : 0;
        if (radius == NaN) radius = 0;
        if (this.$refs.buildingsform.validate()) {
          let country = this.countries.find(c => c.id == this.editedItem.mb_country);
          let payload = {
            bid: 0,
            name: this.removeDiacritics(this.editedItem.mb_nickname),
            address: this.editedItem.mb_address1,
            city: this.editedItem.cityname,
            country: country == undefined ? '' : country.initials,
            state: this.editedItem.statename,
            zip: this.editedItem.mb_zip_code,
            emails: this.parseEmails(this.bemails),
            geo: this.$refs.poly.mapObject.getLatLngs().toString().replace(/LatLng/g, ''),
            lat: this.$refs.poly.mapObject.getBounds().getCenter().lat,
            lng: this.$refs.poly.mapObject.getBounds().getCenter().lng,
            autoshare: this.editedItem.mb_min_zoom,
            zone: this.bzone,
            client: this.primaryClient,
            client_secondary: this.secondaryClient,
            client_third: this.thirdClient,
            contractor: JSON.stringify(this.bcontractor),

            manager: this.bemployee,
            managersecondary: this.bemployeesecondary,
            managerthird: this.bemployeethird,

            maps: JSON.stringify(this.bmaps),
            contracts: JSON.stringify(this.bcontracts),
            ohours: this.bhours,
            bradius: this.bradius || 0,
            sqft: this.bsqft,
            notes: this.bnotes.replace(/\*{3,}/g, "**"),
            externalpropertyid: this.bexternalid,
            externalquickbooksid: this.bquickbooksid,
            scpin: this.scPin,
            scstoreid: this.scStoreId,
            externalprovider: this.bexternalid ? this.bexternal : undefined,


            mb_status: this.pageType == 'sites' ? '1' : '3',
            mb_kind: JSON.stringify(this.selectedPropertyTypes)
          }
          if (this.pageType == 'sitesLeads') {
            payload.mb_source = this.b_source;
            payload.mb_building_status = this.b_site_status;
          }
          if (this.editedIndex > -1) {
            const externalpropertyid = this.bexternalid;
            const externalprovider = this.bexternal;
            //Display message for duplicate external id.
            const duplicateEntry = this.buildings.find((item) => {
              if (item.mb_external_id == externalpropertyid && item.mb_external_src == externalprovider && item.mb_id != this.editedItem.mb_id) {
                return item;
              }
            });
            //Merge only if it's one of the systems which allows external site selection.
            const buildingSaved = () => {
              this.snackbar.text = "Building Saved"
              this.snackbar.snackbar = true;
              this.saving = false;
              this.$refs.dataTable.updateData(true);
            }
            let taskCount = 1;
            if (duplicateEntry && this.externalSiteSearchIcon) {
              taskCount++;
              let mergePayload = {
                sourcebid: duplicateEntry.mb_id,
                destinationbid: this.editedItem.mb_id,
              }
              this.$store.dispatch('copySiteResources', mergePayload).then(function (value) {
                taskCount--;
                if (taskCount == 0) {
                  buildingSaved.call(this);
                }
              }.bind(this))
            }
            payload.bid = this.editedIndex;
            this.saving = true;
            this.$store.dispatch('saveBuilding', payload).then(function (value) {
              this.timesPostHasBeenUpdated++;
              let b = _.find(this.buildings, function (o) {
                return o.mb_id == value;
              });
              this.currentlyediting = b;
              taskCount--;
              if (taskCount == 0) {
                buildingSaved.call(this);
              }
            }.bind(this)).catch(function () {
              this.saving = false;
            }.bind(this))
          } else {
            this.saving = true;
            this.$store.dispatch('saveBuilding', payload).then(function (value) {
              let b = _.find(this.buildings, function (o) {
                return o.mb_id == value;
              });
              this.currentlyediting = b;
              this.snackbar.text = "Building Saved"
              this.snackbar.snackbar = true;
              this.saving = false;
              this.$refs.dataTable.updateData(true);
            }.bind(this)).catch(function () {
              this.saving = false;
            }.bind(this))


          }
        }
      } catch (e) {
        console.log(e);
        this.saving = false;
      }
    }
  },
  watch: {
    siteDetailsDialog(val) {
      if (val == false) {
        this.currentlyediting = null;
      }
    },
    externalSites() {
      this.$refs.externalSitesTabulator?.tabulator?.replaceData(this.externalSites);
      this.$refs.externalSitesTabulator?.tabulator?.deselectRow();
      this.gridSelectExternalSite.tabulatorConfigExternalSites.data = this.externalSites;
      if (this.gridSelectExternalSite.selected) {
        this.$refs.externalSitesTabulator?.tabulator?.selectRow(this.gridSelectExternalSite.selected);
        this.$refs.externalSitesTabulator?.tabulator?.scrollToRow(this.gridSelectExternalSite.selected, "middle", false).catch(() => { });
      }
    },
    cmode(val) {
      localStorage.cmode = val;
    },

    currentlyediting(val) {

      if (val != null) {
        this.loadingmain = true;
        this.bappnotes = "";
        this.bappprofile = [];
        this.bcontractor = [];
        this.gridSelectContractor.selected = [];
        this.bemployee = "";

        this.bemails = "";
        this.bnotes = "";
        this.bnotes = "";
        this.bemployee = "";
        this.bcontractor = [];
        this.bhours = "";
        this.bradius = "";
        this.bsqft = "";
        this.bmaps = [];
        this.bcontracts = [];
        this.bexternal = "";
        this.bexternalid = "";
        this.scPin = "";
        this.scStoreId = "";
        this.bquickbooksid = null;
        this.bzone = "";
        this.bsharesetting = "2";
        this.buildingpoly = [];
        this.marker = null;
        this.primaryClient = this.secondaryClient = this.thirdClient = undefined;
        this.b_site_status = this.b_site_status_items[0]?.ssbs_id;
        this.b_source = this.b_source_items[0]?.ssst_id;
        this.editedIndex = val.mb_id;

        this.editedItem = Object.assign({}, val)
        let payload = {
          bid: val.mb_id,
        }
        this.$store.commit('getBuildingDetailed', payload);
        this.dialog = true;

        let k = this.extendedkeys;

        this.customfields = [];

      } else if (val == null) {
        this.customfields = [];
        this.bappnotes = "";
        this.bappprofile = [];
        this.bcontractor = [];
        this.gridSelectContractor.selected = [];
        this.bemployee = "";

        this.bemails = "";
        this.bnotes = "";

        this.bemployee = "";
        this.bcontractor = [];
        this.bhours = "";
        this.bradius = "";
        this.bsqft = "";
        this.bmaps = [];
        this.bcontracts = [];
        this.bexternal = "";
        this.bexternalid = "";
        this.scPin = "";
        this.scStoreId = "";
        this.bquickbooksid = null;
        this.bzone = "";
        this.bsharesetting = "2";
        this.buildingpoly = [];
        this.marker = null;
        this.primaryClient = this.secondaryClient = this.thirdClient = undefined;
        this.editedIndex = -1;
        this.editedItem = Object.assign({}, this.defaultItem)
        this.editedItem.mb_address1 = "";
        this.b_site_status = this.b_site_status_items[0]?.ssbs_id;
        this.b_source = this.b_source_items[0]?.ssst_id;

        this.dialog = true
        this.buildingdetailed = []



      }
    },

    dialog(val) {
      val || this.close()
      setTimeout(() => {
        if(val) {
          this.$refs.leaflet.mapObject.invalidateSize();
        }

        if (this.editedIndex == -1) {
          this.$refs.address.$el.focus();
        }
        if (val == true && this.editedIndex == -1) {
          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(position => {
              // this.position = position.coords;
              this.$refs.leaflet.mapObject.setView([position.coords.latitude, position.coords.longitude], 18)

            }, error => {
              // this.$refs.leaflet.mapObject.setView([41.881832, -87.623177], 14)
            })
          }
        }
      }, 400);
    }
  },
  asyncComputed: {
    trades: {
      async get() {
        return await fetch(myBaseURL + '/node/vpics/get-trades').then(response => response.json())
      },
      default: []
    },
    baseUrlComputed() {
      return myBaseURL;
    },
    geoCodedSites: {
      get() {
        return this.json_array.filter(ja => ja.status == 1);
      }
    },
    nonGeoCodedSites: {
      get() {
        return this.json_array.filter(ja => ja.status != 1);
      }
    },
    showVerifySitesButton: {
      get() {
        return this.json_array.length > 0 && this.nonGeoCodedSites.length > 0 && !this.dialogLoading;
      }
    },
    showIconsInDatatabe: {
      get() {
        return !this.dialogLoading;
      }
    },

    parseTitle: {
      get() {
        let itemsForTitle = this.gridSelectContractor.selected;
        let title = "";
        if (itemsForTitle.length > 0) {
          if (itemsForTitle.length == 1) {
            title = `${itemsForTitle[0].text}`;
            return title.length > 20 ? title.substring(0, 19) + "..." : title;
          }
          else {
            title = `${itemsForTitle[0].text}`;
            title = `${title.length > 10 ? title.substring(0, 9) + "..." : title} (+${itemsForTitle.length - 1} others)`;
            return title;
          }
        }
        else {
          return "Assign to Contractor"
        }
      }
    },
  },
  computed: {
    primaryClients() {
      return this.allClients.filter(a => !(a.sf_contact_id == this.secondaryClient || a.sf_contact_id == this.thirdClient));
    },
    secondaryClients() {
      return this.allClients.filter(a => !(a.sf_contact_id == this.primaryClient || a.sf_contact_id == this.thirdClient));
    },
    thirdClients() {
      return this.allClients.filter(a => !(a.sf_contact_id == this.primaryClient || a.sf_contact_id == this.secondaryClient));
    },
    allClients() {
      //NOTE: This will include the leads as well while only this getCustomers will have active clients. may be use that? not sure
      return this.$store.getters.getAllContacts.filter(a => a.sf_contact_type == 'Client')
        .map(client => ({
          ...client,
          displayName: (client.sf_contact_company_name || 'No Company Name') +
            ((client.sf_contact_fname || client.sf_contact_lname) ? ` (${(client.sf_contact_fname || '')} ${(client.sf_contact_lname || '')})` : '')
        }))
        .sort((a, b) => a.displayName.localeCompare(b.displayName));
    },
    externalSites() {
      if (this.bexternal == 'Aspire') {
        return this.aspireSites;
      } else if (this.bexternal == 'CaseFMS') {
        return this.caseFMSSites;
      } else if (this.bexternal == 'SMSOne') {
        return this.smsoneSites;
      } else if (this.bexternal == 'Command7') {
        return this.command7Sites;
      } else if (this.bexternal == 'Corrigopro') {
        return this.corrigoProSites;
      }
      return [];
    },
    externalSiteSearchIcon() {
      return this.bexternal == 'Corrigopro' || this.bexternal == 'Command7' || this.bexternal == 'Aspire' || this.bexternal == 'CaseFMS' || this.bexternal == 'SMSOne' ? 'mdi-magnify' : undefined
    },
    quickbooksCustomers() {
      return this.qbContacts.filter(a => a.sqc_contact_type == "CUSTOMER");
    },
    page: {
      get() {
        return this.$store.getters.getPage;
      },
    },
    buildingsgeo: {
      get() {
        let coordsarray = [];
        for (let j = 0; j < this.buildings.length; j++) {
          let coords = phpUnserialize(this.buildings[j].mb_geo);
          coords = coords.substr(1, coords.length - 2);
          coords = coords.split('),(');
          coordsarray[j] = {};
          coordsarray[j].bid = this.buildings[j].mb_id;
          coordsarray[j].coords = []
          for (let i = 0; i < coords.length; i++) {
            coordsarray[j].coords.push(coords[i].split(','));
          }
        }

        return coordsarray;
      }
    },
    buildings: {
      get() {
        this.loading = false;

        const routeId = this.$route.params.id;
        if (routeId !== undefined) {
          if (routeId !== '0') {
            this.loadingmain = true;
            this.siteDetailsDialog = true;
          }
        }

        const buildings = this.$store.getters.getBuildings;
        if (buildings && this.initialDataLoaded) {
          let activeBuildings = [];
          if(this.pageType == 'sites')
            activeBuildings = buildings.filter(building => ['1', '2'].includes(building.mb_status));
          else
            activeBuildings = buildings.filter(building => building.mb_status == '3')
          if (routeId !== undefined) {
            if (routeId === '0') {
              this.currentlyediting = null;
            } else {
              const currentBuilding = activeBuildings.find(building => building.mb_id === routeId);
              if (currentBuilding) {
                this.currentlyediting = currentBuilding;
              }
            }
          }
          return activeBuildings;
        }

        return null;
      },
    },
    contacts: {
      get() {

        return this.$store.getters.getContacts;
      },
    },
    customers: {
      get() {
        //company: company_name, firstname: fname, lastname: lname, email: email, cell: cell
        return this.$store.getters.getCustomers;
      },
    },


    contractors: {
      get() {
        return this.$store.getters.getContractors;
      },
    },
    primaryManager() {
      return this.employees.filter(a => !(a.value == this.bemployeesecondary || a.value == this.bemployeethird));
    },
    secondaryManager() {
      return this.employees.filter(a => !(a.value == this.bemployee || a.value == this.bemployeethird));
    },
    thirdTertiaryManager() {
      return this.employees.filter(a => !(a.value == this.bemployee || a.value == this.bemployeesecondary));
    },
    employees: {
      get() {
        return this.$store.getters.getEmployees;
      },
    },
    extendedprops: {
      get() {

        return this.$store.getters.getExtendedProps;
      }
    },
    extendedkeys: {
      get() {

        return this.$store.getters.getExtendedKeys;
      }
    },

    settings: {
      get() {
        this.saving = false;
        return this.$store.getters.getAccountFlags;
      }
    },


    routeserve: {
      get() {
        return this.$store.getters.getRouteServe;
      },
      set(value) {
        this.$store.commit('setRouteServe', value)
      }
    },
    routecontact: {
      get() {
        return this.$store.getters.getRouteContact;
      },
      set(value) {
        this.$store.commit('setRouteContact', value)
      }
    },
    mini: {
      get() {
        return this.$store.getters.getMini;
      },
      set(value) {
        this.$store.commit('setMini', value)
      }
    },
    forms: {
      get() {
        let forms = this.$store.getters.getForms;
        if (this.editedIndex == -1 || typeof forms == 'undefined')
          return [];

        let items = [];
        for (let j = 0; j < forms.length; j++) {
          let item = {}
          item.value = forms[j].sf_id
          item.text = forms[j].sf_form_name
          items.push(item);
        }
        return items;
      },
    },
    marginTop() {
      if (this.$vuetify.breakpoint.smAndDown)
        return '56px';
      else
        return '64px';
    },
    formTitle() {
      return this.editedIndex === -1 ? 'New Site' : 'Edit Site'
    },
    getActiveProfileID() {
      return this.$store.getters.getActiveProfileID;
    },
    getDetailed() {
      let k = this.$store.getters.getDetailed;

    },
    initialDataLoaded() {
      return this.$store.getters.getDataLoaded;
    },
    pagePermissions() {

      return window.userpermissions[this.pageID] || [];

    },
    pagePermissionEdit() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Edit');
      return permission ? permission['Value'] : true;
    },
    pagePermissionDelete() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Delete');
      return permission ? permission['Value'] : true;
    },
    pagePermissionAdd() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Add');
      return permission ? permission['Value'] : true;
    },
    cLogo() {
      return mycompanylogo;
    },
    cName() {
      return mycompanyname;
    },
    initials() {
      return myfname.charAt(0) + mylname.charAt(0);
    },
  },
  provide() {
    return {
      permissionAdd: () => this.pagePermissionAdd,
      permissionEdit: () => this.pagePermissionEdit,
      permissionDelete: () => this.pagePermissionDelete,
      showSnakeAlert: (message) => this.showAlert(message),
    }
  }
};
