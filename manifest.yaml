kind: Service
apiVersion: v1
metadata:
  name: http
  labels:
    app: sitefotos-app
spec:
  type: ClusterIP
  selector:
    app: sitefotos-app
  ports:
    - port: 80
      targetPort: 80
---
kind: Service
apiVersion: v1
metadata:
  name: nodejs
  labels:
    app: nodejs-app
spec:
  type: ClusterIP
  selector:
    app: nodejs-app
  ports:
    - port: 3000
      targetPort: 3000
---
kind: Service
apiVersion: v1
metadata:
  name: mobile
  labels:
    app: mobile-app
spec:
  type: ClusterIP
  selector:
    app: mobile-app
  ports:
    - port: 3001
      targetPort: 3001
---
kind: Service
apiVersion: v1
metadata:
  name: appdata
  labels:
    app: appdata-app
spec:
  type: ClusterIP
  selector:
    app: appdata-app
  ports:
    - port: 3004
      targetPort: 3004
---
kind: Service
apiVersion: v1
metadata:
  name: apiv1
  labels:
    app: apiv1-app
spec:
  type: ClusterIP
  selector:
    app: apiv1-app
  ports:
    - port: 3002
      targetPort: 3002
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tls-ingress
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 8m
    kubernetes.io/ingress.class: nginx
    nginx.org/websocket-services: "nodejs"
spec:
  tls:
    - hosts:
        - "${BRANCH}.sitefotos.com"
      secretName: tls-cert
  rules:
    - host: "${BRANCH}.sitefotos.com"
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: http
                port:
                  number: 80
          - pathType: Prefix
            path: "/ws"
            backend:
              service:
                name: nodejs
                port:
                  number: 3000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
spec:
  replicas: ${NGINX_REPLICAS}
  selector:
    matchLabels:
      app: sitefotos-app
  template:
    metadata:
      labels:
        app: sitefotos-app
    spec:
      volumes:
        - name: sfgooglecloud
          secret:
            optional: true # only backup to google in master for now
            secretName: sfgooglecloud
      imagePullSecrets:
        - name: do-registry
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - sitefotos-app
              topologyKey: "kubernetes.io/hostname"
      containers:
        - name: nginx
          image: "registry.digitalocean.com/sitefotos2/sitefotos:${DOCKER_TAG}"
          volumeMounts:
            - name: sfgooglecloud
              mountPath: "/etc/sfgooglecloud"
              readOnly: true
          env:
            - name: DATABASE_ADAPTOR
              value: "PDO_MYSQL"
            - name: DATABASE_PARAMS_HOST
              value: "${DATABASE_PARAMS_HOST}"
            - name: DATABASE_PARAMS_PORT
              value: "${DATABASE_PARAMS_PORT}"
            - name: DATABASE_PARAMS_DBNAME
              value: "${DATABASE_PARAMS_DBNAME}"
            - name: DATABASE_PARAMS_USERNAME
              value: "${DATABASE_PARAMS_USERNAME}"
            - name: DATABASE_PARAMS_PASSWORD
              value: "${DATABASE_PARAMS_PASSWORD}"
            - name: CHARGIFY_SUB_DOMAIN
              value: "${CHARGIFY_SUB_DOMAIN}"
            - name: TWILIO_SID
              value: "${TWILIO_SID}"
            - name: BASE_URL
              value: "${BASE_URL}"
            - name: K8S_BRANCH
              value: "${BRANCH}"
            - name: AWS_ACCESS_KEY
              value: ${AWS_ACCESS_KEY}
            - name: AWS_SECRET
              value: ${AWS_SECRET}
            - name: TWILIO_TOKEN
              value: "${TWILIO_TOKEN}"
            - name: MANDRILL_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: mandrill-api-key
                  key: MANDRILL_KEY
            - name: CAPTCHA_SITE_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: captcha-site-key
                  key: CAPTCHA_SITE_KEY
            - name: CAPTCHA_SECRET
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: captcha-secret
                  key: CAPTCHA_SECRET
            - name: RACKSPACE_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: rackspace-key
                  key: RACKSPACE_KEY
            - name: CHARGIFY_API_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only charge in master
                  name: chargify-api-key
                  key: CHARGIFY_API_KEY
          ports:
            - containerPort: 80
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /api/health
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 5
          livenessProbe:
            httpGet:
              path: /api/health
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nodejs
spec:
  replicas: ${NODE_REPLICAS}
  selector:
    matchLabels:
      app: nodejs-app
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25% 
      maxSurge: 75%       
  template:
    metadata:
      labels:
        app: nodejs-app
    spec:
      terminationGracePeriodSeconds: 120
      imagePullSecrets:
        - name: do-registry
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - nodejs-app
              topologyKey: "kubernetes.io/hostname"
      containers:
        - name: nodejs
          image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
          command: ["node"]
          args: ["/node-server/index.js"]
          env:
            - name: NODE_ENV
              value: "${NODE_ENV}"
            - name: DATABASE_PARAMS_HOST
              value: "${DATABASE_PARAMS_HOST}"
            - name: DATABASE_PARAMS_PORT
              value: "${DATABASE_PARAMS_PORT}"
            - name: DATABASE_PARAMS_DBNAME
              value: "${DATABASE_PARAMS_DBNAME}"
            - name: DATABASE_PARAMS_USERNAME
              value: "${DATABASE_PARAMS_USERNAME}"
            - name: DATABASE_PARAMS_PASSWORD
              value: "${DATABASE_PARAMS_PASSWORD}"
            - name: READ_ONLY_DATABASE_PARAMS_HOST
              value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
            - name: READ_ONLY_DATABASE_PARAMS_PORT
              value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
            - name: READ_ONLY_DATABASE_PARAMS_DBNAME
              value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_USERNAME
              value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
              value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"  
            - name: POSTGRES_PARAMS_HOST
              value: "${POSTGRES_PARAMS_HOST}"
            - name: POSTGRES_PARAMS_PORT
              value: "${POSTGRES_PARAMS_PORT}"
            - name: FLEETDB_PARAMS_DBNAME
              value: "${FLEETDB_PARAMS_DBNAME}"
            - name: FLEETDB_PARAMS_USERNAME
              value: "${FLEETDB_PARAMS_USERNAME}"
            - name: FLEETDB_PARAMS_PASSWORD
              value: "${FLEETDB_PARAMS_PASSWORD}"
            - name: TWILIO_SID
              value: "${TWILIO_SID}"
            - name: TWILIO_TOKEN
              value: "${TWILIO_TOKEN}"
            - name: CHARGIFY_SUB_DOMAIN
              value: "${CHARGIFY_SUB_DOMAIN}"
            - name: WORKORDERS_S3_ENDPOINT
              value: "${WORKORDERS_S3_ENDPOINT}"
            - name: APPLE_PRIVATE_KEY
              value: "${APPLE_PRIVATE_KEY}"
            - name: BASE_URL
              value: "${BASE_URL}"
            - name: WORKORDERS_S3_REGION
              value: "${WORKORDERS_S3_REGION}"
            - name: WORKORDERS_S3_ACCESS_KEY
              value: "${WORKORDERS_S3_ACCESS_KEY}"
            - name: WORKORDERS_S3_SECRET_KEY
              value: "${WORKORDERS_S3_SECRET_KEY}"
            - name: FILES_UPLOAD_ENDPOINT
              value: "${FILES_UPLOAD_ENDPOINT}"
            - name: FILES_S3_REGION
              value: "${FILES_S3_REGION}"
            - name: FILES_S3_ACCESS_KEY
              value: "${FILES_S3_ACCESS_KEY}"
            - name: FILES_S3_SECRET_KEY
              value: "${FILES_S3_SECRET_KEY}"
            - name: QUICKBOOKS_CLIENT_ID
              value: "${QUICKBOOKS_CLIENT_ID}"
            - name: QUICKBOOKS_CLIENT_SECRET
              value: "${QUICKBOOKS_CLIENT_SECRET}"
            - name: K8S_BRANCH
              value: "${BRANCH}"
            - name: UPSTASH_REDIS_CONNECTION_STRING
              value: "${UPSTASH_REDIS_CONNECTION_STRING"            
            - name: AWS_ACCESS_KEY
              value: ${AWS_ACCESS_KEY}
            - name: AWS_SECRET
              value: ${AWS_SECRET}
            - name: DEEPINFRA_API_KEY
              value: "${DEEPINFRA_API_KEY}"
            - name: OPENROUTER_API_KEY
              value: "${OPENROUTER_API_KEY}"
            - name: PINECONE_API_KEY
              value: "${PINECONE_API_KEY}"
            - name: OPENAI_API_KEY
              value: "${OPENAI_API_KEY}"
            - name: "PHOTOUPLOAD_SIGN_KEY"
              value: "${PHOTOUPLOAD_SIGN_KEY}"
            - name: APNS_KEY_ID
              value: "${APNS_KEY_ID}"
            - name: APNS_TEAM_ID
              value: "${APNS_TEAM_ID}"
            - name: APNS_BUNDLE_ID
              value: "${APNS_BUNDLE_ID}"
            - name: APNS_AUTH_KEY
              value: "${APNS_AUTH_KEY}"
            - name: FIREBASE_PROJECT_ID
              value: "${FIREBASE_PROJECT_ID}"
            - name: FIREBASE_PRIVATE_KEY_ID
              value: "${FIREBASE_PRIVATE_KEY_ID}"
            - name: REDIS_PERSISTENT_SERVICE_HOST
              value: "${REDIS_PERSISTENT_SERVICE_HOST}"
            - name: REDIS_PERSISTENT_SERVICE_PORT
              value: "${REDIS_PERSISTENT_SERVICE_PORT}"
            - name: REDIS_PERSISTENT_USERNAME
              value: "${REDIS_PERSISTENT_USERNAME}"
            - name: REDIS_PERSISTENT_PASSWORD
              value: "${REDIS_PERSISTENT_PASSWORD}"
            - name: FIREBASE_PRIVATE_KEY
              value: "${FIREBASE_PRIVATE_KEY}"
            - name: FIREBASE_CLIENT_EMAIL
              value: "${FIREBASE_CLIENT_EMAIL}"
            - name: FIREBASE_CLIENT_ID
              value: "${FIREBASE_CLIENT_ID}"
            - name: FIREBASE_CLIENT_X509_CERT_URL
              value: "${FIREBASE_CLIENT_X509_CERT_URL}"
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: $(GOOGLE_APPLICATION_CREDENTIALS)
            - name: RACKSPACE_API_KEY
              value: ${RACKSPACE_API_KEY}
            - name: MANDRILL_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: mandrill-api-key
                  key: MANDRILL_KEY
            - name: CHARGIFY_API_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only charge in master
                  name: chargify-api-key
                  key: CHARGIFY_API_KEY
          ports:
            - containerPort: 3000
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readiness
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 1
          livenessProbe:
            httpGet:
              path: /liveness
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mobile
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mobile-app
  template:
    metadata:
      labels:
        app: mobile-app
    spec:
      terminationGracePeriodSeconds: 120
      volumes:
        - name: sfgooglecloud
          secret:
            optional: true # only backup to google in master for now
            secretName: sfgooglecloud
      imagePullSecrets:
        - name: do-registry
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: mobile-app
      containers:
        - name: mobile
          image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
          command: ["node"]
          args: ["/node-server/index-mobile.js"]
          volumeMounts:
            - name: sfgooglecloud
              mountPath: "/etc/sfgooglecloud"
              readOnly: true
          env:
            - name: NODE_ENV
              value: "${NODE_ENV}"
            - name: DATABASE_PARAMS_HOST
              value: "${DATABASE_PARAMS_HOST}"
            - name: DATABASE_PARAMS_PORT
              value: "${DATABASE_PARAMS_PORT}"
            - name: DATABASE_PARAMS_DBNAME
              value: "${DATABASE_PARAMS_DBNAME}"
            - name: DATABASE_PARAMS_USERNAME
              value: "${DATABASE_PARAMS_USERNAME}"
            - name: DATABASE_PARAMS_PASSWORD
              value: "${DATABASE_PARAMS_PASSWORD}"
            - name: READ_ONLY_DATABASE_PARAMS_HOST
              value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
            - name: READ_ONLY_DATABASE_PARAMS_PORT
              value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
            - name: READ_ONLY_DATABASE_PARAMS_DBNAME
              value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_USERNAME
              value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
              value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
            - name: POSTGRES_PARAMS_HOST
              value: "${POSTGRES_PARAMS_HOST}"
            - name: POSTGRES_PARAMS_PORT
              value: "${POSTGRES_PARAMS_PORT}"
            - name: FLEETDB_PARAMS_DBNAME
              value: "${FLEETDB_PARAMS_DBNAME}"
            - name: FLEETDB_PARAMS_USERNAME
              value: "${FLEETDB_PARAMS_USERNAME}"
            - name: FLEETDB_PARAMS_PASSWORD
              value: "${FLEETDB_PARAMS_PASSWORD}"
            - name: TWILIO_SID
              value: "${TWILIO_SID}"
            - name: AWS_ACCESS_KEY
              value: ${AWS_ACCESS_KEY}
            - name: AWS_SECRET
              value: ${AWS_SECRET}
            - name: TWILIO_TOKEN
              value: "${TWILIO_TOKEN}"
            - name: CHARGIFY_SUB_DOMAIN
              value: "${CHARGIFY_SUB_DOMAIN}"
            - name: WORKORDERS_S3_ENDPOINT
              value: "${WORKORDERS_S3_ENDPOINT}"
            - name: APPLE_PRIVATE_KEY
              value: "${APPLE_PRIVATE_KEY}"
            - name: BASE_URL
              value: "${BASE_URL}"
            - name: WORKORDERS_S3_REGION
              value: "${WORKORDERS_S3_REGION}"
            - name: WORKORDERS_S3_ACCESS_KEY
              value: "${WORKORDERS_S3_ACCESS_KEY}"
            - name: WORKORDERS_S3_SECRET_KEY
              value: "${WORKORDERS_S3_SECRET_KEY}"
            - name: QUICKBOOKS_CLIENT_ID
              value: "${QUICKBOOKS_CLIENT_ID}"
            - name: QUICKBOOKS_CLIENT_SECRET
              value: "${QUICKBOOKS_CLIENT_SECRET}"
            - name: UPSTASH_REDIS_CONNECTION_STRING
              value: "${UPSTASH_REDIS_CONNECTION_STRING}"
            - name: APNS_KEY_ID
              value: "${APNS_KEY_ID}"
            - name: APNS_TEAM_ID
              value: "${APNS_TEAM_ID}"
            - name: APNS_BUNDLE_ID
              value: "${APNS_BUNDLE_ID}"
            - name: APNS_AUTH_KEY
              value: "${APNS_AUTH_KEY}"
            - name: FIREBASE_PROJECT_ID
              value: "${FIREBASE_PROJECT_ID}"
            - name: FIREBASE_PRIVATE_KEY_ID
              value: "${FIREBASE_PRIVATE_KEY_ID}"
            - name: FIREBASE_PRIVATE_KEY
              value: "${FIREBASE_PRIVATE_KEY}"
            - name: FIREBASE_CLIENT_EMAIL
              value: "${FIREBASE_CLIENT_EMAIL}"
            - name: FIREBASE_CLIENT_ID
              value: "${FIREBASE_CLIENT_ID}"
            - name: FIREBASE_CLIENT_X509_CERT_URL
              value: "${FIREBASE_CLIENT_X509_CERT_URL}"
            - name: K8S_BRANCH
              value: "${BRANCH}"
            - name: MANDRILL_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: mandrill-api-key
                  key: MANDRILL_KEY
            - name: CHARGIFY_API_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only charge in master
                  name: chargify-api-key
                  key: CHARGIFY_API_KEY
          ports:
            - containerPort: 3001
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readiness
              port: 3001
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 1
          livenessProbe:
            httpGet:
              path: /liveness
              port: 3001
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: appdata
spec:
  replicas: 1
  selector:
    matchLabels:
      app: appdata-app
  template:
    metadata:
      labels:
        app: appdata-app
    spec:
      terminationGracePeriodSeconds: 120
      volumes:
        - name: sfgooglecloud
          secret:
            optional: true # only backup to google in master for now
            secretName: sfgooglecloud
      imagePullSecrets:
        - name: do-registry
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: appdata-app
      containers:
        - name: appdata
          image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
          command: ["node"]
          args: ["/node-server/index-app-data.js"]
          volumeMounts:
            - name: sfgooglecloud
              mountPath: "/etc/sfgooglecloud"
              readOnly: true
          env:
            - name: NODE_ENV
              value: "${NODE_ENV}"
            - name: DATABASE_PARAMS_HOST
              value: "${DATABASE_PARAMS_HOST}"
            - name: DATABASE_PARAMS_PORT
              value: "${DATABASE_PARAMS_PORT}"
            - name: DATABASE_PARAMS_DBNAME
              value: "${DATABASE_PARAMS_DBNAME}"
            - name: DATABASE_PARAMS_USERNAME
              value: "${DATABASE_PARAMS_USERNAME}"
            - name: DATABASE_PARAMS_PASSWORD
              value: "${DATABASE_PARAMS_PASSWORD}"
            - name: READ_ONLY_DATABASE_PARAMS_HOST
              value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
            - name: READ_ONLY_DATABASE_PARAMS_PORT
              value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
            - name: READ_ONLY_DATABASE_PARAMS_DBNAME
              value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_USERNAME
              value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
              value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
            - name: POSTGRES_PARAMS_HOST
              value: "${POSTGRES_PARAMS_HOST}"
            - name: POSTGRES_PARAMS_PORT
              value: "${POSTGRES_PARAMS_PORT}"
            - name: FLEETDB_PARAMS_DBNAME
              value: "${FLEETDB_PARAMS_DBNAME}"
            - name: FLEETDB_PARAMS_USERNAME
              value: "${FLEETDB_PARAMS_USERNAME}"
            - name: FLEETDB_PARAMS_PASSWORD
              value: "${FLEETDB_PARAMS_PASSWORD}"
            - name: TWILIO_SID
              value: "${TWILIO_SID}"
            - name: AWS_ACCESS_KEY
              value: ${AWS_ACCESS_KEY}
            - name: AWS_SECRET
              value: ${AWS_SECRET}
            - name: TWILIO_TOKEN
              value: "${TWILIO_TOKEN}"
            - name: CHARGIFY_SUB_DOMAIN
              value: "${CHARGIFY_SUB_DOMAIN}"
            - name: WORKORDERS_S3_ENDPOINT
              value: "${WORKORDERS_S3_ENDPOINT}"
            - name: APPLE_PRIVATE_KEY
              value: "${APPLE_PRIVATE_KEY}"
            - name: BASE_URL
              value: "${BASE_URL}"
            - name: WORKORDERS_S3_REGION
              value: "${WORKORDERS_S3_REGION}"
            - name: WORKORDERS_S3_ACCESS_KEY
              value: "${WORKORDERS_S3_ACCESS_KEY}"
            - name: WORKORDERS_S3_SECRET_KEY
              value: "${WORKORDERS_S3_SECRET_KEY}"
            - name: QUICKBOOKS_CLIENT_ID
              value: "${QUICKBOOKS_CLIENT_ID}"
            - name: QUICKBOOKS_CLIENT_SECRET
              value: "${QUICKBOOKS_CLIENT_SECRET}"
            - name: UPSTASH_REDIS_CONNECTION_STRING
              value: "${UPSTASH_REDIS_CONNECTION_STRING}"
            - name: APNS_KEY_ID
              value: "${APNS_KEY_ID}"
            - name: APNS_TEAM_ID
              value: "${APNS_TEAM_ID}"
            - name: APNS_BUNDLE_ID
              value: "${APNS_BUNDLE_ID}"
            - name: APNS_AUTH_KEY
              value: "${APNS_AUTH_KEY}"
            - name: FIREBASE_PROJECT_ID
              value: "${FIREBASE_PROJECT_ID}"
            - name: FIREBASE_PRIVATE_KEY_ID
              value: "${FIREBASE_PRIVATE_KEY_ID}"
            - name: FIREBASE_PRIVATE_KEY
              value: "${FIREBASE_PRIVATE_KEY}"
            - name: FIREBASE_CLIENT_EMAIL
              value: "${FIREBASE_CLIENT_EMAIL}"
            - name: FIREBASE_CLIENT_ID
              value: "${FIREBASE_CLIENT_ID}"
            - name: FIREBASE_CLIENT_X509_CERT_URL
              value: "${FIREBASE_CLIENT_X509_CERT_URL}"
            - name: K8S_BRANCH
              value: "${BRANCH}"
            - name: MANDRILL_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: mandrill-api-key
                  key: MANDRILL_KEY
            - name: CHARGIFY_API_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only charge in master
                  name: chargify-api-key
                  key: CHARGIFY_API_KEY
          ports:
            - containerPort: 3004
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readiness
              port: 3004
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 1
          livenessProbe:
            httpGet:
              path: /liveness
              port: 3004
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: apiv1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: apiv1-app
  template:
    metadata:
      labels:
        app: apiv1-app
    spec:
      terminationGracePeriodSeconds: 120
      imagePullSecrets:
        - name: do-registry
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: apiv1-app
      containers:
        - name: api
          image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
          command: ["node"]
          args: ["/node-server/index-api.js"]
          env:
            - name: NODE_ENV
              value: "${NODE_ENV}"
            - name: DATABASE_PARAMS_HOST
              value: "${DATABASE_PARAMS_HOST}"
            - name: DATABASE_PARAMS_PORT
              value: "${DATABASE_PARAMS_PORT}"
            - name: DATABASE_PARAMS_DBNAME
              value: "${DATABASE_PARAMS_DBNAME}"
            - name: DATABASE_PARAMS_USERNAME
              value: "${DATABASE_PARAMS_USERNAME}"
            - name: DATABASE_PARAMS_PASSWORD
              value: "${DATABASE_PARAMS_PASSWORD}"
            - name: READ_ONLY_DATABASE_PARAMS_HOST
              value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
            - name: READ_ONLY_DATABASE_PARAMS_PORT
              value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
            - name: READ_ONLY_DATABASE_PARAMS_DBNAME
              value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_USERNAME
              value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
              value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
            - name: POSTGRES_PARAMS_HOST
              value: "${POSTGRES_PARAMS_HOST}"
            - name: POSTGRES_PARAMS_PORT
              value: "${POSTGRES_PARAMS_PORT}"
            - name: FLEETDB_PARAMS_DBNAME
              value: "${FLEETDB_PARAMS_DBNAME}"
            - name: FLEETDB_PARAMS_USERNAME
              value: "${FLEETDB_PARAMS_USERNAME}"
            - name: FLEETDB_PARAMS_PASSWORD
              value: "${FLEETDB_PARAMS_PASSWORD}"
            - name: TWILIO_SID
              value: "${TWILIO_SID}"
            - name: TWILIO_TOKEN
              value: "${TWILIO_TOKEN}"
            - name: CHARGIFY_SUB_DOMAIN
              value: "${CHARGIFY_SUB_DOMAIN}"
            - name: AWS_ACCESS_KEY
              value: ${AWS_ACCESS_KEY}
            - name: AWS_SECRET
              value: ${AWS_SECRET}
            - name: WORKORDERS_S3_ENDPOINT
              value: "${WORKORDERS_S3_ENDPOINT}"
            - name: APPLE_PRIVATE_KEY
              value: "${APPLE_PRIVATE_KEY}"
            - name: BASE_URL
              value: "${BASE_URL}"
            - name: WORKORDERS_S3_REGION
              value: "${WORKORDERS_S3_REGION}"
            - name: WORKORDERS_S3_ACCESS_KEY
              value: "${WORKORDERS_S3_ACCESS_KEY}"
            - name: WORKORDERS_S3_SECRET_KEY
              value: "${WORKORDERS_S3_SECRET_KEY}"
            - name: QUICKBOOKS_CLIENT_ID
              value: "${QUICKBOOKS_CLIENT_ID}"
            - name: QUICKBOOKS_CLIENT_SECRET
              value: "${QUICKBOOKS_CLIENT_SECRET}"
            - name: UPSTASH_REDIS_CONNECTION_STRING
              value: "${UPSTASH_REDIS_CONNECTION_STRING}"
            - name: K8S_BRANCH
              value: "${BRANCH}"
            - name: APNS_KEY_ID
              value: "${APNS_KEY_ID}"
            - name: APNS_TEAM_ID
              value: "${APNS_TEAM_ID}"
            - name: APNS_BUNDLE_ID
              value: "${APNS_BUNDLE_ID}"
            - name: APNS_AUTH_KEY
              value: "${APNS_AUTH_KEY}"
            - name: "PHOTOUPLOAD_SIGN_KEY"
              value: "${PHOTOUPLOAD_SIGN_KEY}"
            - name: FIREBASE_PROJECT_ID
              value: "${FIREBASE_PROJECT_ID}"
            - name: FIREBASE_PRIVATE_KEY_ID
              value: "${FIREBASE_PRIVATE_KEY_ID}"
            - name: FIREBASE_PRIVATE_KEY
              value: "${FIREBASE_PRIVATE_KEY}"
            - name: FIREBASE_CLIENT_EMAIL
              value: "${FIREBASE_CLIENT_EMAIL}"
            - name: FIREBASE_CLIENT_ID
              value: "${FIREBASE_CLIENT_ID}"
            - name: K8S_DEPLOYMENT
              value: "apiv1"
            - name: FIREBASE_CLIENT_X509_CERT_URL
              value: "${FIREBASE_CLIENT_X509_CERT_URL}"
            - name: MANDRILL_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only email in master
                  name: mandrill-api-key
                  key: MANDRILL_KEY
            - name: CHARGIFY_API_KEY
              valueFrom:
                secretKeyRef:
                  optional: true # only charge in master
                  name: chargify-api-key
                  key: CHARGIFY_API_KEY
          ports:
            - containerPort: 3002
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readiness
              port: 3002
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 1
          livenessProbe:
            httpGet:
              path: /liveness
              port: 3002
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nodejs-worker
spec:
  replicas: 1 
  selector:
    matchLabels:
      app: nodejs-worker-app
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25% 
  template:
    metadata:
      labels:
        app: nodejs-worker-app
    spec:
      terminationGracePeriodSeconds: 60 
      imagePullSecrets:
        - name: do-registry
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - nodejs-worker-app
              topologyKey: "kubernetes.io/hostname"
      containers:
        - name: nodejs-worker
          image: "registry.digitalocean.com/sitefotos2/sitefotos-node:${DOCKER_TAG}"
          command: ["node"]
          args: ["/node-server/worker.js"]
          env:
            - name: NODE_ENV
              value: "${NODE_ENV}"
            - name: K8S_BRANCH 
              value: "${BRANCH}"
            - name: REDIS_PERSISTENT_SERVICE_HOST
              value: "${REDIS_PERSISTENT_SERVICE_HOST}"
            - name: REDIS_PERSISTENT_SERVICE_PORT
              value: "${REDIS_PERSISTENT_SERVICE_PORT}"
            - name: REDIS_PERSISTENT_USERNAME
              value: "${REDIS_PERSISTENT_USERNAME}"
            - name: REDIS_PERSISTENT_PASSWORD
              value: "${REDIS_PERSISTENT_PASSWORD}"
            - name: DATABASE_PARAMS_HOST
              value: "${DATABASE_PARAMS_HOST}"
            - name: DATABASE_PARAMS_PORT
              value: "${DATABASE_PARAMS_PORT}"
            - name: DATABASE_PARAMS_DBNAME
              value: "${DATABASE_PARAMS_DBNAME}"
            - name: DATABASE_PARAMS_USERNAME
              value: "${DATABASE_PARAMS_USERNAME}"
            - name: DATABASE_PARAMS_PASSWORD
              value: "${DATABASE_PARAMS_PASSWORD}"
            - name: READ_ONLY_DATABASE_PARAMS_HOST
              value: "${READ_ONLY_DATABASE_PARAMS_HOST}"
            - name: READ_ONLY_DATABASE_PARAMS_PORT
              value: "${READ_ONLY_DATABASE_PARAMS_PORT}"
            - name: READ_ONLY_DATABASE_PARAMS_DBNAME
              value: "${READ_ONLY_DATABASE_PARAMS_DBNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_USERNAME
              value: "${READ_ONLY_DATABASE_PARAMS_USERNAME}"
            - name: READ_ONLY_DATABASE_PARAMS_PASSWORD
              value: "${READ_ONLY_DATABASE_PARAMS_PASSWORD}"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gotenberg
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gotenberg-app
  template:
    metadata:
      labels:
        app: gotenberg-app
    spec:
      securityContext:
        runAsUser: 1001
      containers:
        - name: gotenberg
          image: "gotenberg/gotenberg:8.21.1"
          ports:
            - containerPort: 3000
              protocol: TCP
          resources:
            limits:
              memory: 512Mi
          readinessProbe:
            tcpSocket:
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 3000
            initialDelaySeconds: 15
            periodSeconds: 20
---
kind: Service
apiVersion: v1
metadata:
  name: gotenberg
  labels:
    app: gotenberg-app
spec:
  type: ClusterIP
  selector:
    app: gotenberg-app
  ports:
    - port: 80
      targetPort: 3000
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq-app
  serviceName: "rabbitmq"
  template:
    metadata:
      labels:
        app: rabbitmq-app
    spec:
      containers:
        - name: rabbitmq
          image: "rabbitmq:3.8.19-management-alpine"
          ports:
            - containerPort: 5672
              protocol: TCP
            - containerPort: 15672
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: 5672
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 5672
            initialDelaySeconds: 15
            periodSeconds: 20
          volumeMounts:
            - mountPath: /var/lib/rabbitmq/mnesia
              name: rabbitmq-data
  volumeClaimTemplates:
    - metadata:
        name: rabbitmq-data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 5Gi
        storageClassName: do-block-storage
---
kind: Service
apiVersion: v1
metadata:
  name: rabbitmq
  labels:
    app: rabbitmq-app
spec:
  type: ClusterIP
  selector:
    app: rabbitmq-app
  ports:
    - port: 5672
      targetPort: 5672
---
kind: Service
apiVersion: v1
metadata:
  name: rabbitmq-admin
  labels:
    app: rabbitmq-app
spec:
  type: ClusterIP
  selector:
    app: rabbitmq-app
  ports:
    - port: 15672
      targetPort: 15672
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  labels:
    app: redis-app
spec:
  type: ClusterIP
  selector:
    app: redis-app
  ports:
    - port: 6379
      targetPort: 6379
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: redis-priority
value: 1000000
globalDefault: false
description: "High priority class for Redis pods to prevent eviction"
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  serviceName: "redis"
  replicas: 1
  selector:
    matchLabels:
      app: redis-app
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: redis-app
    spec:
      priorityClassName: redis-priority
      terminationGracePeriodSeconds: 30  
      containers:
        - name: redis
          image: "redis:6.2.11"
          command: ["redis-server"]
          args: [
            "--maxmemory", "${REDIS_MAX_MEMORY}",
            "--maxmemory-policy", "volatile-ttl",
            "--dir", "/data",
            "--save", "900", "1",
            "--save", "300", "10",
            "--save", "60", "10000"
          ]
          resources:
            requests:
              memory: ${REDIS_MIN_MEMORY_LIMIT}
            limits:
              memory: ${REDIS_MAX_MEMORY_LIMIT}
          ports:
            - name: redis
              containerPort: 6379
              protocol: TCP
          volumeMounts:
            - name: redis-data
              mountPath: "/data"
          readinessProbe:
            tcpSocket:
              port: 6379
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 6379
            initialDelaySeconds: 15
            periodSeconds: 20
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "redis-cli SAVE"]  
  volumeClaimTemplates:
    - metadata:
        name: redis-data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 5Gi
        storageClassName: do-block-storage
---
kind: Service
apiVersion: v1
metadata:
  name: bun-server
  labels:
    app: bun-server-app
spec:
  type: ClusterIP
  selector:
    app: bun-server-app
  ports:
    - port: 3005
      targetPort: 3005
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bun-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bun-server-app
  template:
    metadata:
      labels:
        app: bun-server-app
    spec:
      terminationGracePeriodSeconds: 120
      imagePullSecrets:
        - name: do-registry
      containers:
        - name: bun-server
          image: "registry.digitalocean.com/sitefotos2/sitefotos-bun:${DOCKER_TAG}"
          ports:
            - containerPort: 3005
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /health
              port: 3005
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: 3005
            initialDelaySeconds: 15
            periodSeconds: 20
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "256Mi"
          env:
            - name: NODE_ENV
              value: "${NODE_ENV}"
            - name: EAGLEVIEW_API_KEY
              value: "${EAGLEVIEW_API_KEY}"
